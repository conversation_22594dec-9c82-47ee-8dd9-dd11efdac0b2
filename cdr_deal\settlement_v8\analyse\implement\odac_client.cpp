﻿/*!
 * \file odac_client.cpp
 *
 * Copyright (c) 2002, Asiainfo Technologies (China), Inc.
 * All right reserved
 *
 * \brief file_brief
 *
 * \author jiangxh
 *
 * \version 2.0
 * \date 2053/05/20
 */
 
//#ifdef __ODAC__


#include "odac_client.h"
#include <iostream>
#include <time.h>  
//#include "baseodac/obsd_id.h"
//#include "baseodac/obsd_reg_func.h"
//#include "Client_Query.h"
//#include "../base/Client_Interface.h"

//using namespace PARAM;

ODAC_BEGIN_NAMESPACE_DECLARE

//ODAC_APP_INTERFACE_IMPLEMENT(ODAC_TYPE_ANALYSE);

CODACClient::CODACClient()
{
    
}

CODACClient::~CODACClient()
{    
    m_qryProxy.close();
}
/*
int CODACClient::connect(const char *pszShmKey)
{
    if( odac_client_init(pszShmKey) != 0 )
    {
        printf("odac_client_init failure!\n");
        return -1;
    }
    
    ODAC_HANDLE handle = -1;
    if( (handle = odac_app_register()) == -1 )
    {
        printf("odac_app_register failure!\n");
        return -1;
    }

    return 0;
}

int CODACClient::disconnect()
{
    odac_app_logout();
    odac_client_destroy();
    return 0;
}

int CODACClient::lock()
{
    if( odac_app_begintrans() != 0 )
    {
        return -1;
    }
    
    return 0;
}

int CODACClient::unlock()
{
    odac_app_endtrans();
    
    return 0;
}*/
int CODACClient::SplitIpPort(const char *szPort,const char *szIp)
{
    string strPort(szPort);
    string strIp(szIp);
    int32 nPosPort = strPort.find('/');
    int32 nPosIp = strIp.find('/');
    if(nPosPort==string::npos && nPosIp==string::npos)//涓�涓�ip/port
    {
        ::MBasenpquery::SProxy sTemp;
        sTemp.set_ip(string(strIp));
        sTemp.set_port(atoi(strPort.c_str()));
        m_versSockStr.push_back(sTemp);
    }
    else if((strPort.length()-1)>nPosPort && (strIp.length()-1)>nPosIp)
    {
        /*::MBasenpquery::SProxy sTemp;
        sTemp.set_ip(string(strIp.substr(0,nPosIp)));
        sTemp.set_port(atoi((strPort.substr(0,nPosPort)).c_str()));
        m_versSockStr.push_back(sTemp);

        sTemp.set_ip( strIp.substr(nPosIp+1,strIp.length()-nPosIp-1));
        sTemp.set_port( atoi((strPort.substr(nPosPort+1,strPort.length()-nPosPort-1)).c_str()));
        m_versSockStr.push_back(sTemp);
        */
        CStringList arrayIpList;
        CStringList arrayPortList;
        cdk::strings::Split(strPort,"/",arrayPortList);
        cdk::strings::Split(strIp,"/",arrayIpList);
        if(arrayIpList.size()==arrayPortList.size())
        {
            for(int i=0;i<arrayIpList.size();i++)
            {
                ::MBasenpquery::SProxy sTemp;
                sTemp.set_ip(arrayIpList[i]);
                sTemp.set_port(atoi(arrayPortList[i].c_str()));
                m_versSockStr.push_back(sTemp);
            }
        }else
        {
            LOG_ERROR(-1,"Please config right IP<%s> and Port<%s> must matched.",szIp,szPort);
            return -1;
        }
        
    }
    else
    {
            return -1;
    }
    return 0;
}
time_t str2time (const AISTD string& dt)
{
    struct tm tt;
    memset(&tt,0,sizeof(tt));
    sscanf (dt.c_str (), "%4d%2d%2d%2d%2d%2d",
        &tt.tm_year,
        &tt.tm_mon,
        &tt.tm_mday,
        &tt.tm_hour,
        &tt.tm_min,
        &tt.tm_sec);

    tt.tm_year = tt.tm_year - 1900;
    tt.tm_mon = tt.tm_mon - 1;

    return mktime(&tt);
}


int CODACClient::FindNpInfo(const char *szNumber,const char *szDate,SNpInfo &p_pNpInfo)
{
    if(!qryProxyInitFlag)
    {
        try{
            char *szPort = getenv("NP_MDB_PORT");
            char *szIp = getenv("NP_MDB_IP");
            if(SplitIpPort(szPort,szIp)==-1)
            {
                LOG_ERROR(-1,"Please config right IP<%s> and Port<%s>.",szIp,szPort);
                return -1;
            }
            m_qryProxy.init(m_versSockStr);
            qryProxyInitFlag=true;
        }catch(...)
        {
            LOG_ERROR(-1, "init np mdb client error");
            qryProxyInitFlag=false;
            return -1;
        };
    }
    p_pNpInfo.nFlag = 0;
    if(strlen(szNumber)!=11)
          return -1;
    AISTD string strRecTime(szDate);
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t t_Time  = str2time(strRecTime);
    int64 lTime = atol(strRecTime.c_str());
    int32 iNum=0;
    while(1)
    {
        m_qryProxy.setNotify(6101);
        m_qryProxy.setNotifyData(szNumber,lTime,lTime);

        vector<CNpDetail> vecNpDetail;
        //::MBasenpquery::SNpDetailList vecNpDetail;
        int nRet = m_qryProxy.sendAndRecvNotify(vecNpDetail);
        if(nRet == 0)
        {
            vector<CNpDetail>::iterator iter = vecNpDetail.begin();
            //::MBasenpquery::SNpDetailList::iterator iter = vecNpDetail.begin();
            for(;iter!=vecNpDetail.end();iter++)
            {
                p_pNpInfo.nFlag = 1;
                strcpy(p_pNpInfo.szPhoneId , iter->m_szPhoneId);
                string strTemp(iter->m_szDstNetId);
                string strNet = strTemp.substr(3,1);
                if(strNet == "1"||strNet == "2"||strNet == "5")
                        p_pNpInfo.nNetWork = 3;//CDMA
                else if(strNet == "4")
                        p_pNpInfo.nNetWork = 8;//TD
                else if(strNet == "3")
                    p_pNpInfo.nNetWork = 2;//GSM
                else
                    p_pNpInfo.nNetWork = 0;
                p_pNpInfo.nOperatorParty = atoi((strTemp.substr(2,1)).c_str());
        
                if(p_pNpInfo.nNetWork == 0)
                {
                    if(p_pNpInfo.nOperatorParty == 1)
						p_pNpInfo.nNetWork = 3;//CDMA
                    else
						p_pNpInfo.nNetWork = 2;//GSM
                }
        
            }
            break;
        }
        else
        {
            iNum++;
            LOG_INFO( " connect to NP client false affter 10 seconds try again. already try <%d> times ",iNum);
            sleep(10);
        }
        if(iNum==3)
        {
            LOG_ERROR(-1, "try to connect np client three times process whill exit");
            exit(1);
        }
    }
    return 0;
}


int CODACClient::findGsmMsc(const xc::CSnapshot& cSnapShot,
                            const char *pMscId,
                            CBpsGsmMsc &cGsmMscData,
                            const time_t &tm)
{
    int iRst = 0;
    iRst = odac_app_lookup_CBpsGsmMsc(cSnapShot, pMscId, tm, cGsmMscData);

    if(iRst != 0)
    {
      return -1;
    }

    return 0;
}

int CODACClient::findSpecialNumber(const xc::CSnapshot &cSnapShot,
                                   int                iServiceId,
                                   const char        *pCenterArea,
                                   const char        *pDigitalArea,
                                   const char        *pNumber,
                                   CBpsSpecialNumber &cSpecialNumberData,
                                   const time_t      &tm)
{
    LOG_TRACE("ANASYS findSpecialNumber IN iServiceId=<%d>",iServiceId);
    LOG_TRACE("ANASYS findSpecialNumber IN pCenterArea=<%s>",pCenterArea);
    LOG_TRACE("ANASYS findSpecialNumber IN pDigitalArea=<%s>",pDigitalArea);
    LOG_TRACE("ANASYS findSpecialNumber IN pNumber=<%s>",pNumber);
    LOG_TRACE("ANASYS findSpecialNumber IN tm=<%d>",tm);
    char aNumber[64];
    memset(aNumber,0,sizeof(aNumber));
    strncpy(aNumber,pNumber,strlen(pNumber));
    
LOOP_SPC_20220602:

    int iRst = 0;
    AISTD list<CBpsSpecialNumber> cList;
    LOG_TRACE("ANASYS NumberAnalysis_lua IN aNumber=<%s>",aNumber);

    iRst = odac_app_lookup_CBpsSpecialNumber(cSnapShot, iServiceId, aNumber, tm, cList);

    if(iRst != 0)
    {
      return iRst;
    }
    else
    {
        AISTD list<CBpsSpecialNumber>::iterator it;
        
        /*
        1.如果表中区号为0，开放区号为0，标识该特殊号码对所有地市本地有效。
          则必须号码的区号和主叫的区号相同才算。应该在只收信息费组。
        2.如果表中区号为0,开放区号不为0，则表示特殊号码为所有地市，
          对主叫是这些开放区号的有效，可能可以收长途费。
        3.如果表中区号不为0,开放区号为0，则表示特殊号码为归属区号，
        主叫号码任意地区有效，可能可以收长途费。
        4.如果表中区号不为0，开放区号不为0，则表示只有符号特殊号码归属区号
        主叫号码区号，才判定为特殊号码。一般在只收信息费组，
        当归属区号和开放区号不同时，可能在也收长途费组。
        */
        for (it = cList.begin(); it != cList.end(); ++it)
        {
            if (strcmp(it->get_areaCode(),"0") == 0)
            {
                if( strcmp(it->get_openArea(), "0") == 0 )
                {
                    cSpecialNumberData = *it;
                    return 0;
                }
                else
                {
                    if (strcmp(it->get_openArea(), pCenterArea) == 0)
                    {
                        cSpecialNumberData = *it;
                        return 0;
                    }
                }
            }
            else//不为0
            {
                if (strcmp(it->get_areaCode(), pDigitalArea) == 0)
                 {
                    if ( (strcmp(it->get_openArea(), pCenterArea) == 0) || 
                         (strcmp(it->get_openArea(), "0")==0) )
                    {
                        cSpecialNumberData = *it;
                        return 0;
                    }
                }
            }
        }
        
        it = cList.begin();
        const char* pSpecNumber = it->get_specialNumber();
        if(strlen(pSpecNumber)>1)
        {
            memset(aNumber,0,sizeof(aNumber));
            strncpy(aNumber,pSpecNumber,strlen(pSpecNumber)-1);
            goto LOOP_SPC_20220602;
        }
    }

    return -2;
}

int CODACClient::strictfindSpecialNumber(const xc::CSnapshot &cSnapShot,
                                         int                 iServiceId,
                                         const char         *pCenterArea,
                                         const char        *pDigitalArea,
                                         const char        *pNumber,
                                         CBpsSpecialNumber &cSpecialNumberData,
                                         const time_t      &tm)
{
    int iRst = 0;
    AISTD list<CBpsSpecialNumber> cList;

    iRst = odac_app_lookup_CBpsSpecialNumber(cSnapShot, iServiceId, pNumber, tm, cList);
    

    if(iRst != 0)
    {
      return -1;
    }
    else
    {
        AISTD list<CBpsSpecialNumber>::iterator it;
        
        /*
        1.如果表中区号为0，开放区号为0，标识该特殊号码对所有地市本地有效。
          则必须号码的区号和主叫的区号相同才算。应该在只收信息费组。
        2.如果表中区号为0,开放区号不为0，则表示特殊号码为所有地市，
          对主叫是这些开放区号的有效，可能可以收长途费。
        3.如果表中区号不为0,开放区号为0，则表示特殊号码为归属区号，
        主叫号码任意地区有效，可能可以收长途费。
        4.如果表中区号不为0，开放区号不为0，则表示只有符号特殊号码归属区号
        主叫号码区号，才判定为特殊号码。一般在只收信息费组，
        当归属区号和开放区号不同时，可能在也收长途费组。
        */
        for (it = cList.begin(); it != cList.end(); ++it)
        {
            if (strcmp(it->get_areaCode(),"0") == 0)
            {
                if( strcmp(it->get_openArea(), "0") == 0 )
                {
                    cSpecialNumberData = *it;
                    return 0;
                }
                else
                {
                    if (strcmp(it->get_openArea(), pCenterArea) == 0)
                    {
                        cSpecialNumberData = *it;
                        return 0;
                    }
                }
            }
            else//不为0
            {
                if (strcmp(it->get_areaCode(), pDigitalArea) == 0)
                 {
                    if ( (strcmp(it->get_openArea(), pCenterArea) == 0) || 
                         (strcmp(it->get_openArea(), "0")==0) )
                    {
                        cSpecialNumberData = *it;
                        return 0;
                    }
                }
            }
        }        
    }    

    return -2;
}

int CODACClient::findCityByAreaCode(const xc::CSnapshot &cSnapShot,
                                    const char *pAreaCode,
                                    CSysCity   &cCityData,
                                    const time_t &tm)
{
    //    去除区号前的0
    const char *pNewAreaCcode = pAreaCode;
    while (*pNewAreaCcode == '0')
        pNewAreaCcode++;

    if (0 == strlen(pNewAreaCcode))
        pNewAreaCcode = "0";
    
    int iRst = odac_app_lookup_CSysCityByAreaCode(cSnapShot, pNewAreaCcode,tm, cCityData);
    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;
}

//add function
int CODACClient::findCityByRegionCode(const xc::CSnapshot &cSnapShot,
                                      const char *cRegionCode,
                                      CSysCity   &cCityData,
                                      const time_t &tm)
{
    int iRst = odac_app_lookup_CSysCityByRegionCode(cSnapShot, cRegionCode, tm, cCityData);
    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;
}

int CODACClient::findProv(const xc::CSnapshot &cSnapShot,
                          int       iProvCode,
                          CSysProv &cProvData,
                          const time_t &tm)
{
    char szPovCode[8];
    sprintf(szPovCode, "%d", iProvCode);
    
    int iRst = odac_app_lookup_CSysProv(cSnapShot,    szPovCode, tm, cProvData);
    
    if(iRst != 0)
    {
      return -1;
    }

    return 0;    
}

int CODACClient::findAccessNumber(const xc::CSnapshot &cSnapShot,
                                  const char          *pNumber,
                                  CBpsAccessNumber    &cAccessNumber,
                                  const time_t        &tm)
{
    int iRst = odac_app_lookup_CBpsAccessNumber(cSnapShot,    pNumber, tm, cAccessNumber);
    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;
}

int CODACClient::findCountry(const xc::CSnapshot &cSnapShot,
                             const char          *szCountryCode,
                             CSysCountry         &cCountryData,
                             const time_t        &tm)
{
    int iRst = odac_app_lookup_CSysCountry(cSnapShot, szCountryCode, tm, cCountryData);
    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;    
}

int CODACClient::findMobileSeg(const xc::CSnapshot &cSnapShot,
                               const char          *szMCodeSeg,
                               CBpsAddMobileSeg    &cMobileSegData,
                               const time_t        &tm)
{
    int iRst = odac_app_lookup_CBpsAddMobileSeg(cSnapShot,    szMCodeSeg, tm,    cMobileSegData);
    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;
}    

int CODACClient::findBsmsServCode(const xc::CSnapshot &cSnapShot,
                                  const char          *pszServType,
                                  const char          *pszServCode,
                                  const int32          iMacthType,
                                  CBpsBsmsServicecode &cBsmsServCodeData,
                                  const time_t &tm)
{
    int iRst = 0;
    AISTD list<CBpsBsmsServicecode> cList;
    if (1 == iMacthType )
    {
        iRst = odac_app_lookup_CBpsBsmsServicecode(cSnapShot, pszServType, pszServCode, tm, cList);
    }
    else if (2 == iMacthType)
    {
        iRst = odac_app_lookup_CBpsBsmsServicecode(cSnapShot, pszServType, pszServCode, tm, cList);
    }
    if(iRst != 0)
    {
    return iRst;
    }
    else
    {
        AISTD list<CBpsBsmsServicecode>::iterator it;
    
        for (it = cList.begin(); it != cList.end(); ++it)
        {
            cBsmsServCodeData = *it;
                        return 0;
        }
    }

    return -1;            
}





int CODACClient::findHlr(const xc::CSnapshot &cSnapShot,
                         const char          *szHlrCode,
                         CBpsHlr             &cHlrData,
                         const time_t        &tm)
{
    int iRst = odac_app_lookup_CBpsHlr(cSnapShot, szHlrCode, tm, cHlrData);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;
}

int CODACClient::findMmmCode(const xc::CSnapshot &cSnapShot,
                             const char          *pNumber,
                             CBpsAddMmmCode      &cMmmCode,
                             const time_t        &tm)
{
    int iRst = odac_app_lookup_CBpsAddMmmCode(cSnapShot, pNumber, tm, cMmmCode);
    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;
}

int CODACClient::findPstnNumSeg(const xc::CSnapshot &cSnapShot,
                                const char *pAreaCode,
                                const char *pNumber,
                                CBpsPstnNumseg &cPstnNumsegData,
                                const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsPstnNumseg(cSnapShot, pAreaCode, pNumber,tm, cPstnNumsegData);
    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;
}

int CODACClient::findBusinessAreaRel(const xc::CSnapshot &cSnapShot,
                                     const char          *pAreaCode,
                                     const char          *pBusiAreaCode1,
                                     const char          *pBusiAreaCode2,
                                     CBpsBusinessAreaRel &cBusinessAreaRelData,
                                     const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsBusinessAreaRel(cSnapShot, pAreaCode, pBusiAreaCode1, pBusiAreaCode2, cBusinessAreaRelData);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;    
}

int CODACClient::findPstnSwitch(const xc::CSnapshot &cSnapShot,
                                const char          *pSwitchId,
                                CBpsPstnSwitch      &cPstnSwitchData,
                                const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsPstnSwitch(cSnapShot, pSwitchId, cPstnSwitchData);
    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;        
}

int CODACClient::findPstnRouter(const xc::CSnapshot &cSnapShot,
                                const char          *pSwitchId,
                                const char          *pTrunkId,
                                CBpsPstnRouter      &cPstnRouterData,
                                const time_t        &tm)
{
    int iRst = odac_app_lookup_CBpsPstnRouter(cSnapShot, pSwitchId, pTrunkId,tm, cPstnRouterData);

    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;
}

//待修改
int CODACClient::findSpecialUser(const xc::CSnapshot &cSnapShot,
            const char      *pAreaCode,
            const char      *pNumber,
            CBpsSpecialUser &cSpecialUserData,
            const time_t &tm)
{
    char  szKey[512];
    strcpy(szKey, pNumber);
    for (int i = strlen(szKey); i>=1; i--)
    {
        AISTD list<CBpsSpecialUser> cList;
        int iRst = 0;
        szKey[i] = 0;
        iRst=odac_app_lookup_CBpsSpecialUser(cSnapShot,atol(pAreaCode),pNumber,tm,cList);
        if( iRst == 0 )
        {
            AISTD list<CBpsSpecialUser>::iterator it;
        
            for (it = cList.begin(); it!= cList.end(); ++it)
            {
                if ( 0 == strcmp(it->get_startNumber(), it->get_endNumber()))
                {
                    cSpecialUserData = *it;
                    return 0;
                }
                else if (strcmp(pNumber, it->get_startNumber()) >=0 &&
                           strcmp(pNumber, it->get_endNumber()) <=0 )
                {
                    cSpecialUserData = *it;
                    return 0;
                }
            }
        }
    }
    return -2;
}

// BSCBI_FUNC_32105- 【湖北结算基线】网间语音业务分析增加查询接口库
int CODACClient::findSpecialUserByBureauCode(const xc::CSnapshot &cSnapShot,
                                            const char      *pAreaCode,
                                            const char      *pBureauCode,
                                            const char      *pNumber,
                                            CBpsSpecialNet  &cSpecialNetData,
                                            const time_t &tm)
{
    char  szKey[64]={0};
    char szBureauCode[16];

    strncpy(szKey, pNumber, sizeof(szKey)-1);

    if (NULL == pBureauCode || strlen(pBureauCode) == 0)
		strncpy(szBureauCode, "NULL", 4);
	else
		strncpy(szBureauCode, pBureauCode, sizeof(szBureauCode)-1);


    for (int i = strlen(szKey); i>=1; i--)
    {
        AISTD list<CBpsSpecialNet> cList;
        int iRst = 0;
        szKey[i] = 0;
        
        iRst=odac_app_lookup_CBpsSpecialNet(cSnapShot,pAreaCode,szBureauCode,pNumber,tm,cList);
        if( iRst == 0 )
        {
            AISTD list<CBpsSpecialNet>::iterator it;
        
            for (it = cList.begin(); it!= cList.end(); ++it)
            {
                if ( 0 == strcmp(it->get_startNumber(), it->get_endNumber()))
                {
                    cSpecialNetData = *it;
                    return 0;
                }
                else if (strcmp(pNumber, it->get_startNumber()) >=0 &&
                           strcmp(pNumber, it->get_endNumber()) <=0 )
                {
                    cSpecialNetData = *it;
                    return 0;
                }
            }
        }
    }
    return -2;

    /*char szBureauCode[56];
    char *cont_addr = 0;
    //ACQUIRE_STC_CONTAINER_ADDR(ID_CObsdBpsSpecialUser, cont_addr);

    if (NULL == pBureauCode || strlen(pBureauCode) == 0)
        strcpy(szBureauCode, "NULL");
    else
        strcpy(szBureauCode, pBureauCode);
    
    CBpsSpecialUserKey cKey(pAreaCode, pNumber, szBureauCode, 1);
    
    bool   bHaveDate = false;
    time_t tm;
    if (pDate != NULL && strlen(pDate) > 4)
    {
        bHaveDate = true;
        AISTD string strRecTime(pDate);
        if (strRecTime.size() < 14)
        {
            string strTemp("20000101000000");
            strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
        }
        tm  = str2time(strRecTime);
    }
    
    char  szKey[512];
    int32 iIdx = 1;
    strcpy(szKey, cKey.get_key(iIdx));
    int iKeyLen = strlen(szKey);
    
    CHashContainer<hash_key_string, CBpsSpecialUser> *cContainer = (CHashContainer<hash_key_string, CBpsSpecialUser> *)cont_addr; 

    int iMaxRecLen = cContainer->get_max_rec_len(iIdx);
    int iMinRecLen = cContainer->get_min_rec_len(iIdx);
    
    if (iKeyLen < iMinRecLen)
        return -3;
        
    if (iKeyLen > iMaxRecLen && iMaxRecLen > 0)
        iKeyLen = iMaxRecLen;

    for (int i = iKeyLen; i>= iMinRecLen; i--)
    {
        AISTD list<CBpsSpecialUser> cList;
        int iRst = 0;
        szKey[i] = 0;
        hash_key_string cHashKey(szKey);
        
        if (bHaveDate)
            iRst = cContainer->find(iIdx, cHashKey, tm, cList);
        else
            iRst = cContainer->find(iIdx, cHashKey, cList);

        if( iRst > 0 )
        {
            AISTD list<CBpsSpecialUser>::iterator it;
        
            for (it = cList.begin(); it != cList.end(); ++it)
            {
                if ( 0 == strcmp(it->get_startNumber(), it->get_endNumber()))
                {
                    cSpecialUserData = *it;
                    return 0;
                }
                else if (strcmp(pNumber, it->get_startNumber()) >=0 &&
                           strcmp(pNumber, it->get_endNumber()) <=0 )
                {
                    cSpecialUserData = *it;
                    return 0;
                }
            }
        }
    }
*/
    return -2;
}

int CODACClient::findMisnNumSeg(const xc::CSnapshot &cSnapShot,
                                const char     *pNumber,
                                CBpsMisnNumseg &cMisnNumsegData,
                                const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsMisnNumseg(cSnapShot, pNumber, cMisnNumsegData);
    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;    
}
/*
int CODACClient::findCCM(const xc::CSnapshot &cSnapShot,
                         const char *pCcmIp,
                         const char *pSacpIp,
                         CBpsCcm    &cCcmData,
                         const time_t &tm)
{
    return 0;
    
    int iRst = odac_app_lookup_CBpsCcm(cSnapShot, pCcmIp, cCcmData);
    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;
}*/

int CODACClient::findChannelType(const xc::CSnapshot &cSnapShot,
                                 const int        iService,
                                 const int        iBandwidth,
                                 const char      *pCodec,
                                 const int        iDir,
                                 CBpsChannelType &cChannelType,
                                 const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsChannelType(cSnapShot, iService, pCodec, iDir, cChannelType);
    
    if(iRst != 0)
    {
      return -1;
    }

    return 0;        
}


int CODACClient::findRateType( const xc::CSnapshot &cSnapShot,
                               const char *pszAccessNumber,
                               const char *pszOriArea,
                               const char *pszTermArea,
                               const int   iTollGroup,
                               int&        iRateType,      //warning return value;
                               int&        iTollType,
                               const time_t &tm)
{
    CBpsRateType    cValue;
    
    int iRst = odac_app_lookup_CBpsRateType(cSnapShot,    pszAccessNumber, pszOriArea, pszTermArea, iTollGroup, tm, cValue);
    
    if(iRst != 0)
    {
      return -1;
    }
    
    iRateType = cValue.get_rateType();
    iTollType = cValue.get_tollType();

    return 0;    
}


int CODACClient::findCarrierImsi(const xc::CSnapshot &cSnapShot,
                                 const char      *pszImsi,
                                 CBpsCarrierImsi &cCarrierImsiData,
                                 const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsCarrierImsi(cSnapShot, pszImsi, cCarrierImsiData);
    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;    
}

int CODACClient::findGsmRouter(const xc::CSnapshot &cSnapShot,
                               const char    *pszMscId,
                               const char    *pszTrunkId,
                               CBpsGsmRouter &cBpsGsmRouter,
                               const time_t  &tm)
{
    int iRst = odac_app_lookup_CBpsGsmRouterByMscIdAndTrunkId(cSnapShot, pszMscId, pszTrunkId, tm, cBpsGsmRouter);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;            
}

int CODACClient::findGsmRouterByAreaCode(const xc::CSnapshot &cSnapShot,
                                         const char    *pszMscId,
                                         const char    *pszTrunkId,
                                         const char    *pszAreaCode,
                                         CBpsGsmRouter &cBpsGsmRouter,
                                         const time_t &tm)
{
    AISTD list<CBpsGsmRouter> cList;
    int iRst = odac_app_lookup_CBpsGsmRouter(cSnapShot, pszMscId, pszTrunkId, pszAreaCode, tm, cList);
    //int iRst = odac_app_lookup_CBpsGsmRouter(cSnapShot, pszMscId, pszTrunkId, pszAreaCode, tm, cBpsGsmRouter);

    if(iRst == 0)
    {
        AISTD list<CBpsGsmRouter>::iterator it = cList.begin();

        cBpsGsmRouter = *it;
        
    }else
    {
    }

    return iRst;            
}

//-待修改，CBpsGsmRouter表应该只有两个索引，这里是第三个？
int CODACClient::findGsmRouterByTrunkFlag(const xc::CSnapshot &cSnapShot,
                                            const char    *pszMscId,
                                          const char    *pszTrunkId,
                                          const char    *pszTrunkFlag,
                                          const char    *pszAreaCode,
                                          CBpsGsmRouter &cBpsGsmRouter,
                                          const time_t &tm)
{
    char *cont_addr = 0;
    
    int iRst   = -1;
    AISTD list<CBpsGsmRouter> cList;
    
    if (strlen(pszMscId) > 0 &&
          strcmp(pszMscId, "0") != 0)
    {
        iRst = odac_app_lookup_CBpsGsmRouter(cSnapShot,pszMscId, pszTrunkId, pszAreaCode, tm, cList);
        
        if(iRst == 0)
        {
            AISTD list<CBpsGsmRouter>::iterator it;
            
            for (it = cList.begin(); it != cList.end(); ++it)
            {
                if(0 == strcmp(pszTrunkFlag, "1") )
                {
                    if( 0 <= it->get_outTrunkBusiId() )
                    {
                        cBpsGsmRouter = *it;
                        return 0;
                    }
                }
                
                if(0 == strcmp(pszTrunkFlag, "-1") )
                {
                    if( 0 <= it->get_inTrunkBusiId() )
                    {
                        cBpsGsmRouter = *it;
                        return 0;
                    }
                }
            }
        }
    }
        

    /*cList.clear();
    
    CBpsGsmRouterKey cKey2("0", pszTrunkId, pszAreaCode, 1);
    iRst = CObsdBpsGsmRouter::lookup(cont_addr, 1, cKey2, tm, cList);
        
    if(iRst == 0)
    {
        AISTD list<CBpsGsmRouter>::iterator it;
    
        for (it = cList.begin(); it != cList.end(); ++it)
        
        for (int i=0; i < cList.size(); i++)
        {
            if(0 == strcmp(pszTrunkFlag, "1") )
            {
                if( 0 <= it->get_outTrunkBusiId() )
                {
                    cBpsGsmRouter = *it;
                    return 0;
                }
            }
            
            if(0 == strcmp(pszTrunkFlag, "-1") )
            {
                if( 0 <= it->get_inTrunkBusiId() )
                {
                    cBpsGsmRouter = *it;
                    return 0;
                }
            }
        }
    }*/
    return -1;        
}

int CODACClient::findCardPrefix(const xc::CSnapshot &cSnapShot,
                                const char     *pszCardPrefix,
                                CBpsCardPrefix &cCardPrefixData,
                                const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsCardPrefixByCardPrefix(cSnapShot, pszCardPrefix, cCardPrefixData);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;
}

int CODACClient::findCardPrefixByCardType(const xc::CSnapshot &cSnapShot,
                                          const char     *pszCardPrefix,
                                          const char     *pszCardType,
                                          CBpsCardPrefix &cCardPrefixData,
                                          const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsCardPrefix(cSnapShot, pszCardPrefix, pszCardType, cCardPrefixData);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;    
}


int CODACClient::findSpBusiDescByServCode2( const xc::CSnapshot &cSnapShot,
                                            const char          *pszServCode2,
                                            const int            iBusiType,
                                            CBpsAddSpBusiDesc &cSpBusiDescData,
                                            const time_t& tm)
{
    AISTD list<CBpsAddSpBusiDesc> cList;
    int iRst = odac_app_lookup_CBpsAddSpBusiDescByServCode2(cSnapShot, pszServCode2, tm, cList);

    if(iRst != 0)
    {
      return iRst;
    }
    else
    {
        AISTD list<CBpsAddSpBusiDesc>::iterator it;
        
        for (it = cList.begin(); it != cList.end(); ++it)
        {
            if (iBusiType == -1 || iBusiType == it->get_busiType())
            {
                cSpBusiDescData = *it;
                return 0;
            }
        }
    }

    return -1;        
}


int CODACClient::findSpBusiDescByServCode2Ex( const xc::CSnapshot &cSnapShot,
                                              const char        *pszServCode2,
                                              const char        *pszAccArea,
                                              CBpsAddSpBusiDesc &cSpBusiDescData,
                                              const time_t& tm)
{
    AISTD list<CBpsAddSpBusiDesc> cList;
    int iRst = odac_app_lookup_CBpsAddSpBusiDescByServCode2(cSnapShot, pszServCode2, tm, cList);

    if(iRst != 0)
    {
      return iRst;
    }
    else
    {
        int64 llLastTime = -1;
        bool  bContinue;
        
        AISTD list<CBpsAddSpBusiDesc>::iterator it;
        
        for (it = cList.begin(); it != cList.end(); ++it)
        {
            if (pszAccArea != NULL && strlen(pszAccArea) > 0 )
                    bContinue = strncmp(it->get_accArea(), pszAccArea, strlen(pszAccArea)) ==0;
            else
                    bContinue = true;
            
            if (bContinue && (-1 == llLastTime || llLastTime < it->get_expireDate()) )
            {
                cSpBusiDescData = *it;
                llLastTime      = it->get_expireDate();
            }
        }
        
        if (llLastTime != -1)
            return 0;
        else
            return -2;
    }

    return -1;        
}


int CODACClient::findSpBusiDescBySpCode( const xc::CSnapshot& cSnapShot,
                                         const char        *pszSpCode,
                                         const int          iBusiType,
                                         CBpsAddSpBusiDesc &cSpBusiDescData,
                                         const time_t& tm)
{
    AISTD list<CBpsAddSpBusiDesc> cList;
    int iRst = odac_app_lookup_CBpsAddSpBusiDescBySpCode(cSnapShot, pszSpCode, tm, cList);

    if(iRst != 0)
    {
      return iRst;
    }
    else
    {
        AISTD list<CBpsAddSpBusiDesc>::iterator it;
            
        if (iBusiType == -99999)
        {
            if (cList.size() > 0)
            {
                it = cList.begin();
                cSpBusiDescData = *it;
                return 0;
            }
            else
                return -2;
        }

        for (it = cList.begin(); it != cList.end(); ++it)
        {
            if (iBusiType == it->get_busiType())
            {
                cSpBusiDescData = *it;
                return 0;
            }
        }
    }

    return -2;        
}


int CODACClient::findSpBusiDescByBusiCode( const xc::CSnapshot& cSnapShot,
                                           const char        *pszBusiCode,
                                           CBpsAddSpBusiDesc &cSpBusiDescData,
                                           const time_t& tm)
{
    int iRst = odac_app_lookup_CBpsAddSpBusiDescByBusiCode(cSnapShot, pszBusiCode, tm,    cSpBusiDescData);

    if(iRst != 0)
    {
      return iRst;
    }
    if (strncmp(cSpBusiDescData.get_busiCode(), pszBusiCode, strlen(pszBusiCode)) ==0)
        return 0;    
    else
        return -2;    
}

int CODACClient::findPlatformBusiDesc( const xc::CSnapshot& cSnapShot,
                                       const int                iAccCode,
                                       const char              *pszOperCode,
                                       CBpsAddPlatformBusiDesc &cPlatformBusiDesc,
                                       const time_t &tm)
{
    int iRst = 0;
    
    if (NULL == pszOperCode)
    {
        iRst = odac_app_lookup_CBpsAddPlatformBusiDescByAccCode(cSnapShot, iAccCode, cPlatformBusiDesc); //KEY1
    }
    else
    {
        iRst = odac_app_lookup_CBpsAddPlatformBusiDesc(cSnapShot, iAccCode, pszOperCode, cPlatformBusiDesc); //KEY2
    }

    if(iRst != 0)
    {
      return -1;
    }

    return 0;            
}


int CODACClient::findOperator(const xc::CSnapshot& cSnapShot,
                              const char      *pszOperCode,
                              const char      *pszHomeArea,
                              CBpsAddOperator &cOperatorData,
                              const time_t &tm)
{
    AISTD list<CBpsAddOperator> cList;
    int iRst = odac_app_lookup_CBpsAddOperator(cSnapShot, pszOperCode,tm, cList);
    
    //LOG_TRACE("findOperator   ",pszOperCode);

    if(iRst != 0)
    {
      return iRst;
    }
    else
    {
        AISTD list<CBpsAddOperator>::iterator it;
            
        if (pszHomeArea == NULL)
        {
            if (cList.size() > 0)
            {
                it = cList.begin();
                cOperatorData = *it;
                return 0;
            }
            else
                return -2;
        }

        for (it = cList.begin(); it != cList.end(); ++it)
        {
            if (strcmp(it->get_homeArea(), pszHomeArea) ==0)
            {
                cOperatorData = *it;
                return 0;
            }
        }
    }

    return -2;        
}


int CODACClient::findOperatorByOperType(const xc::CSnapshot& cSnapShot,
                                        const char      *pszOperCode,
                                        const int        iOperType,
                                        CBpsAddOperator &cOperatorData,
                                        const time_t &tm)
{
    AISTD list<CBpsAddOperator> cList;
    int iRst = odac_app_lookup_CBpsAddOperator(cSnapShot, pszOperCode,tm, cList);

    if(iRst != 0)
    {
        return iRst;
    }
    else
    {
        AISTD list<CBpsAddOperator>::iterator it;
        
        for (it = cList.begin(); it != cList.end(); ++it)
        {
            if (iOperType == it->get_operType())
            {
                cOperatorData = *it;
                return 0;
            }
        }
    }

    return -2;        
}

int CODACClient::findIsmg(const xc::CSnapshot &cSnapShot,
                          const char *pszIsmgId,
                          CBpsIsmg   &cIsmgData,
                          const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsIsmg(cSnapShot, pszIsmgId, cIsmgData);
    
    if(iRst != 0)
    {
      return iRst;
    }

    return 0;    
}

int CODACClient::parseHlrCode(const char *pszImsiCode, char *pszHlrCode)
{
        int iLength = strlen(pszHlrCode);
        char sHlrCode;
        LOG_TRACE("yangwq test code run is here3333333  %s", pszImsiCode);
        for( int i=0; i<iLength; i++ )
        {
                sHlrCode = pszHlrCode[i];
                
                if( sHlrCode>= 'A'  && sHlrCode<= 'O' )
                        pszHlrCode[i] = pszImsiCode[sHlrCode - 'A'];
        }
        return 0;
}


int CODACClient::findHlrByImsi(const xc::CSnapshot& cSnapShot,
                               const char *pszImsiCode,
                               char *pszHlrCode,
                               const time_t& tm)
{
    AISTD list<CBpsImsiHlrRegular> cList;
    //int iRst = 0;
    LOG_TRACE("yangwq test code run is here666666666666  %s",pszImsiCode);
    int iRst = odac_app_lookup_CBpsImsiHlrRegular(cSnapShot, pszImsiCode, tm, cList);
    LOG_TRACE("yangwq test code run is here  %d", iRst);
    if(iRst != 0)
    {
        return iRst;
    }
    else
    {
        AISTD list<CBpsImsiHlrRegular>::iterator it;
        LOG_TRACE("yangwq test code run is here1111111  %d", iRst);
        for (it = cList.begin(); it != cList.end(); ++it)
        {
            int iCheckIndex = it->get_checkIndex();
            int iCheckValue = it->get_checkValue();
            LOG_TRACE("yangwq test code run is here22222  %d", iCheckValue);
            if( iCheckIndex < 1 ||
                     pszImsiCode[iCheckIndex-1] == (iCheckValue + '0') )
            {
                strcpy( pszHlrCode, it->get_hlrCode() );
                parseHlrCode( pszImsiCode, pszHlrCode );
                return 0;
            }
        }
    }
    
    return -2;        
}

int CODACClient::findSpSms(const xc::CSnapshot& cSnapShot,
                            const char   *pszServCode,
                            CBpsAddSpSms &cSpSms,
                            const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsAddSpSms(cSnapShot, pszServCode, cSpSms);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;    
}

int CODACClient::findMscByFileName(const xc::CSnapshot& cSnapShot,
                                    const char           *pszFileName,
                                    CBpsAddFilenametomsc &cFilenametomsc,
                                    const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsAddFilenametomsc(cSnapShot, pszFileName, tm,cFilenametomsc);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;
}


int CODACClient::findCardFeeCodeSeg(const xc::CSnapshot& cSnapShot,
                                    const char            *pszCardNo,
                                    CBpsAddCardFeeCodeSeg &cCardFeeCodeSeg,
                                    const time_t &tm)
{
    AISTD list<CBpsAddCardFeeCodeSeg> cList;
    int iRst = odac_app_lookup_CBpsAddCardFeeCodeSeg(cSnapShot, pszCardNo, cList);

    if(iRst != 0)
    {
      return iRst;
    }

    AISTD list<CBpsAddCardFeeCodeSeg>::iterator it;
        
    for (it = cList.begin(); it != cList.end(); ++it)
    {
        if( strcmp(pszCardNo,it->get_startMsisdn())>=0 && 
              strcmp(pszCardNo,it->get_endMsisdn())<= 0 )
        {
            cCardFeeCodeSeg = *it;
            return 0;
        }
    }
    return -1;            
}


int CODACClient::findGprsIpaddrInfo(const xc::CSnapshot& cSnapShot,
                                    const char *pszProvCode,
                                    const char *pszIpAddr,
                                    const time_t &tm)
{
    AISTD list<CBpsGprsIpaddrInfo> cList;
    int iRst = odac_app_lookup_CBpsGprsIpaddrInfo(cSnapShot, pszProvCode, tm, cList);

    if(iRst != 0)
    {
      return -1;
    }
    else
    {
        AISTD list<CBpsGprsIpaddrInfo>::iterator it;
        
        for (it = cList.begin(); it != cList.end(); ++it)
        {
            if(strncmp(it->get_beginIp(),pszIpAddr,strlen(pszIpAddr))<=0 &&
                 strncmp(it->get_endIp(),pszIpAddr,strlen(pszIpAddr))>0 )
                return 0;
        }
    }

    return -2;            
}

int CODACClient::findKoreaRoamMsisdn(const xc::CSnapshot &cSnapShot,
                                     const char          *pszRoamMsisdn,
                                     CBpsKoreaRoamMsisdn &cKoreaRoamMsisdnData,
                                     const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsKoreaRoamMsisdn(cSnapShot, pszRoamMsisdn, cKoreaRoamMsisdnData);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;    
}

int CODACClient::findHlrTrademark(const xc::CSnapshot& cSnapShot,
                                  const char *pszHlrCode,
                                  const time_t &tm)
{
    CBpsHlrTrademark    cValue;
    
    int iRst = odac_app_lookup_CBpsHlrTrademark(cSnapShot, pszHlrCode,    cValue);

    if(iRst != 0)
    {
      return iRst;
    }

    return cValue.get_trademark();    
}

int CODACClient::findSpUserInfo(const xc::CSnapshot &cSnapShot,
                                const char   *pszSpCode,
                                const char   *pszOperCode,
                                const char   *pszMsisdn,
                                CVSpUserInfo &cSpUserInfo,
                                const time_t &tm)
{
    int iRst = odac_app_lookup_CVSpUserInfo(cSnapShot,    pszSpCode, pszOperCode,    pszMsisdn, tm, cSpUserInfo);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;
}

int CODACClient::findSpProd(const xc::CSnapshot& cSnapShot,
                            const char    *pszBillingMonth,
                            CBpsAddSpProd &cAddSpProdData,
                            const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsAddSpProd(cSnapShot, pszBillingMonth, cAddSpProdData);

    //if(iRst != 0)
    //{
    //  return 0;
    //}

    return 0;        
}

int CODACClient::findIrUserLimit(const xc::CSnapshot &cSnapShot,
                                 const char      *pszPartnerId,
                                 const char      *pszLimitType,
                                 CBpsIrUserLimit &cIrUserLimit,
                                 const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsIrUserLimit(cSnapShot, pszPartnerId, pszLimitType, cIrUserLimit);

    //if(iRst != 0)
    //{
    //    char szMsg[256];
    //    sprintf(szMsg,"CObsdBpsIrUserLimit::lookup_from_container failure! container addr[0x%p], stc_id=[%d].",
    //                                cont_addr, ID_CObsdBpsIrUserLimit);
    //     m_strErrMsg = string(szMsg);
    //  return 0;
    //}

    return 0;        
}

int CODACClient::findLacAreaRel(const xc::CSnapshot &cSnapShot,
                                const char      *pszLacId,
                                CVBpsLacAreaRel &cLacAreaRelData,
                                const time_t &tm)
{
    int iRst = odac_app_lookup_CVBpsLacAreaRel(cSnapShot, pszLacId, tm, cLacAreaRelData);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;        
}

int CODACClient::findCiContentData(const xc::CSnapshot &cSnapShot,
                                   const char *pszContentID,
                                   CBpsCiContentData &cCiContentData,
                                   const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsCiContentData(cSnapShot, pszContentID, tm, cCiContentData);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;    
}

int CODACClient::findFreeGsmRouter(const xc::CSnapshot &cSnapShot,
                                   const char        *pszMscId,
                                   const char        *pszTrunkId,
                                   CBpsFreeGsmRouter &cFreeGsmRouter,
                                   const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsFreeGsmRouter(cSnapShot, pszMscId, pszTrunkId, tm, cFreeGsmRouter);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;        
}

int CODACClient::findOperatorDesc(const xc::CSnapshot& cSnapShot,
                                  const int            iDrType,
                                  const int            iOperId,
                                  CBpsAddOperatorDescription &cOperatorDescData,
                                  const time_t& tm)
{
    int iRst = odac_app_lookup_CBpsAddOperatorDescription(cSnapShot,    iDrType, iOperId, tm, cOperatorDescData);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;        
}

int CODACClient::findMscCodeByTempMsc(const xc::CSnapshot &cSnapShot,
                                      const char  *pszTrunkGroup,
                                      const char  *pszTempMscCode,
                                      CBpsMscCode &cMscCode,
                                      const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsMscCode(cSnapShot, pszTrunkGroup, pszTempMscCode, cMscCode);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;    
}

int CODACClient::findLac(const xc::CSnapshot &cSnapShot,
                         const char *pszLac,
                         CBpsLac    &cLac,
                         const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsLac( cSnapShot, pszLac, tm, cLac);

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;    
}

int CODACClient::findSpRatio(const xc::CSnapshot &cSnapShot,
                             const char *pszSpCode,
                             const char *pszOperatorCode,
                             const int   nType,
                             CVSpRatio  &cSpRatioData,
                             const time_t &tm)
{
    int iRst = odac_app_lookup_CVSpRatio(cSnapShot, pszSpCode, pszOperatorCode, nType, tm, cSpRatioData); //KEY2
    if(iRst != 0)
        //iRst = odac_app_lookup_CVSpRatioBySpCodeAndSpType(cSnapShot, pszSpCode, nType,    tm, cSpRatioData); //KEY1
        iRst = odac_app_lookup_CVSpRatio(cSnapShot, pszSpCode, "-1", nType, tm, cSpRatioData); //KEY2

    if(iRst != 0)
    {
      return iRst;
    }

    return 0;    
}

int CODACClient::findAccCodeBySpOperCode(const xc::CSnapshot& cSnapShot,
                                         const char     *pszSpCode,
                                         const char     *pszOperCode,
                                         CBpsAcccodeRel &cAcccodeRel,
                                         const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsAcccodeRel(cSnapShot, pszSpCode, pszOperCode, cAcccodeRel);
    
    if(iRst == -1)
      return -1;
    
    return 0;    
}

int CODACClient::findBorderRoam(const xc::CSnapshot &cSnapShot,
                                const char        *pszMscID,
                                const char        *pszLacID,
                                const char        *pszCellID,
                                const char        *pszAreaCode,
                                CBpsBorderRoam &cBpsBorderRoam,
                                const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsBorderRoam(cSnapShot, pszMscID, pszLacID, pszCellID, pszAreaCode, tm, cBpsBorderRoam);

    if(iRst != 0)
    {
        return -1;
    }

    return 0;
}

//wanghy3 add 20161016
int CODACClient::findRcdx(const xc::CSnapshot &cSnapShot,
                          const char *pszServiceCode,
                          CVBpsRcdxUserRel &cRcdxData,
                          const time_t &tm)
{
    int iRst = odac_app_lookup_CVBpsRcdxUserRel(cSnapShot,    pszServiceCode, cRcdxData);

    if(iRst != 0)
    {
        return iRst;
    }

    return 0;
}

/*
	// delete by zhangxj7 bps_np_use change to MDB SERVER
int CODACClient::NPUserAnalysis(const xc::CSnapshot &cSnapShot,
                     const char *pszUserNumber, 
                     int        &nOperId,
                     int        &nNetType,
                     char       *pszAreaCode,
                     int        &nPortOutOperId,
                     int        &nHomeOperId,
                     const time_t &tm)
{
    CBpsNpUser cBpsNpUser;
    int iRst =    odac_app_lookup_CBpsNpUser(cSnapShot, pszUserNumber, tm, cBpsNpUser);
    if(iRst != 0)
    {
        return iRst;
    }
    else
    {
        nOperId  = cBpsNpUser.get_operId();
                nNetType = cBpsNpUser.get_netType();
                strcpy(pszAreaCode, cBpsNpUser.get_areaCode());
                nPortOutOperId = cBpsNpUser.get_portOutOperId();
                nHomeOperId = cBpsNpUser.get_homeOperId();
    }
    return 0;
}
*/
int CODACClient::findCeilCountyRel(const xc::CSnapshot &cSnapShot,
                                   const char *pszLacId,
                                   const char  *pszCeilId,
                                   CVBpsLacCeilCountyRel &cLacCeilCountyRelData,
                                   const time_t &tm)
{
        int iRst = odac_app_lookup_CVBpsLacCeilCountyRel(cSnapShot, pszLacId, pszCeilId,tm, cLacCeilCountyRelData);

        if(iRst != 0)
        {
          return -1;
        }

        return 0;
}

/*
int CODACClient::NPUserAnalysisNew(const xc::CSnapshot &cSnapShot,
                     const char *pszUserNumber,
                     int        &nOperId,
                     int        &nNetType,
                     char       *pszAreaCode,
                     int        &nPortOutOperId,
                     int        &nHomeOperId,
                     const time_t &tm)
{

    CBpsNpUser cBpsNpUser;
    int iRst =    odac_app_lookup_CBpsNpUser(cSnapShot, pszUserNumber, tm, cBpsNpUser);

//    char *cont_addr = 0;
//    CBpsNpUser cBpsNpUser;
//    ACQUIRE_STC_CONTAINER_ADDR(ID_CObsdBpsNpUser,cont_addr);
//    CBpsNpUserKey cKey(pszUserNumber);
//    int iRst =    CObsdBpsNpUser::match(cont_addr, 0, cKey, cBpsNpUser);
//    if(iRst != 0)
//    {
//        char szMsg[256];
//         sprintf(szMsg,"CObsdBpsNpUser::lookup_from_container failure! containerddr[0x%p], stc_id=[%d].", cont_addr,ID_CObsdBpsNpUser);
//        m_strErrMsg=string(szMsg);
//        return iRst;
//    }
//    else
//    {
//        nOperId  = cBpsNpUser.m_iOperId;
//                nNetType = cBpsNpUser.m_iNetType;
//                strcpy(pszAreaCode, cBpsNpUser.m_szAreaCode);
//                nPortOutOperId = cBpsNpUser.m_iPortOutOperId;
//                nHomeOperId = cBpsNpUser.m_iHomeOperId;
//    }
return 0;
}

*/

int CODACClient::NPUserAnalysisWithTime(const char *pszUserNumber, const char *pDate,
                     int        &nOperId,
                     int        &nNetType,
                     char       *pszAreaCode,
                     int        &nPortOutOperId,
                     int        &nHomeOperId,
                     const time_t &tm)
{
    /*char *cont_addr = 0;
    CBpsNpUser cBpsNpUser;
    ///ACQUIRE_STC_CONTAINER_ADDR(ID_CObsdBpsNpUser,cont_addr);
    //CBpsNpUserKey cKey(pszUserNumber);
    int iRst = 0;
    //int iRst =    CObsdBpsNpUser::lookup(cont_addr, 0, cKey, cBpsNpUser);
    time_t tm;
    
    if (pDate != NULL && strlen(pDate) > 4)
    {
        AISTD string strRecTime(pDate);
        if (strRecTime.size() < 14)
        {
            string strTemp("20000101000000");
            strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
        }
        tm  = str2time(strRecTime);
        
        //CBpsNpUserKey cKey(pszUserNumber);
        //iRst = CObsdBpsNpUser::lookup(cont_addr, 0, cKey, tm, cBpsNpUser);
    }
    else
    {
        //CBpsNpUserKey cKey(pszUserNumber);
        //iRst = CObsdBpsNpUser::lookup(cont_addr, 0, cKey, cBpsNpUser);
    }

    if(iRst != 0)
    {
        char szMsg[256];
         sprintf(szMsg,"CObsdBpsNpUser::lookup_from_container failure! containerddr[0x%p], stc_id=[%d].", cont_addr,ID_CObsdBpsNpUser);
        m_strErrMsg=string(szMsg);
        return iRst;
    }
    else
    {
        nOperId  = cBpsNpUser.m_iOperId;
                nNetType = cBpsNpUser.m_iNetType;
                strcpy(pszAreaCode, cBpsNpUser.m_szAreaCode);
                nPortOutOperId = cBpsNpUser.m_iPortOutOperId;
                nHomeOperId = cBpsNpUser.m_iHomeOperId;
    }*/
    return 0;
}

//added by gaijy 2020-08-24
int CODACClient::findBpsCardHomeProv(const xc::CSnapshot &cSnapShot,
                                     const char *szCardCode,
                                     CBpsCardHomeProv &cBpsCardHomeProv,
                                     const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsCardHomeProv(cSnapShot, szCardCode, tm,    cBpsCardHomeProv);

    return iRst;
}


int CODACClient::findIvrRatio(const xc::CSnapshot &cSnapShot,
                              const char *pszOper,
                              const char *pszSpCode,
                              const char *pszServ,
                              const int32 nfee,
                              const int32 nKey,
                              CVIvrRatio &cIvrRatio,
                              const time_t &tm)
{
    int iRst = 0;
    AISTD list<CVIvrRatio> cList;

    if (nKey == 1)
    {
        iRst = odac_app_lookup_CVIvrRatioByOperCode(cSnapShot, pszOper, tm, cList);
    }
    else if (nKey ==2)
    {
        iRst = odac_app_lookup_CVIvrRatioBySpCode(cSnapShot, pszSpCode, tm, cList);
    }
    else if (nKey ==3)
    {
        iRst = odac_app_lookup_CVIvrRatio(cSnapShot, pszSpCode, pszOper, tm, cList);
    }
    else
    {
        return -1;
    }

    if(iRst != 0)
    {
        return iRst;
    }
    else
    {
        AISTD list<CVIvrRatio>::iterator it;
        for (it = cList.begin(); it != cList.end(); ++it)
        {
            if(strcmp(it->get_servType(),pszServ)==0&&nfee==it->get_fee())
            {
                cIvrRatio = *it;
                return 0;
            }
            else
            {
                cIvrRatio = *it;
                return 0;
            }
        }
    }
    return 0;
}

int CODACClient::findBpsImsiOperInfo(const xc::CSnapshot &cSnapShot,
                                     const char* szImsiOperInfo,
                                     CBpsImsiOperInfo& cBpsImsiOperInfo,
                                     const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsImsiOperInfo(cSnapShot,    szImsiOperInfo, tm,    cBpsImsiOperInfo);

    if(iRst != 0)
    {
        return iRst;
    }
    return 0;
}

int CODACClient::findBpsImsiNumber(const xc::CSnapshot &cSnapShot,
                                   const char* szImsiNumber,
                                   CBpsImsiNumber& cBpsImsiNumber,
                                   const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsImsiNumber(cSnapShot, szImsiNumber, tm, cBpsImsiNumber);

    if(iRst != 0)
    {
        return iRst;
    }
    return 0;
}


//int CODACClient::findBpsImsiNumber(const char* szImsiNumber, const char* szStartTime, CBpsImsiNumber& cBpsImsiNumber)
//{
//    char *cont_addr = 0;
//    ACQUIRE_STC_CONTAINER_ADDR(ID_CObsdBpsImsiNumber, cont_addr);
//    CBpsImsiNumberKey cKey(szImsiNumber, 0);
//
//    bool   bHaveDate = false;
//    time_t tm;
//    //printf("get input arg,szImsiNumber:%s,szStartTime:%s\n",szImsiNumber,szStartTime);
//
//    if (szStartTime != NULL && strlen(szStartTime) > 4)
//    {
//        bHaveDate = true;
//        AISTD string strRecTime(szStartTime);
//        if (strRecTime.size() < 14)
//        {
//            string strTemp("20000101000000");
//            strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
//        }
//        tm  = str2time(strRecTime);
//    }
//
//    char  szKey[512];
//    int32 iIdx = 0;
//    strcpy(szKey, cKey.get_key(iIdx));
//    int iKeyLen = strlen(szKey);    
//    CHashContainer<hash_key_string, CBpsImsiNumber> *cContainer = (CHashContainer<hash_key_string, CBpsImsiNumber> *)cont_addr; 
//    //设置key最长匹配位数和最短匹配位数
//    int iMaxRecLen = cContainer->get_max_rec_len(iIdx);
//    int iMinRecLen = cContainer->get_min_rec_len(iIdx);
//    //printf("iKeyLen=%d,iMaxRecLen=%d,iMinRecLen=%d.\n",iKeyLen,iMaxRecLen,iMinRecLen);
//    if (iKeyLen < iMinRecLen)
//        return -3;
//        
//    if (iKeyLen > iMaxRecLen && iMaxRecLen > 0)
//        iKeyLen = iMaxRecLen;
//
//    for (int i = iKeyLen; i>= iMinRecLen; i--)
//    {
//        AISTD list<CBpsImsiNumber> cList;
//        int iRst = 0;
//        szKey[i] = 0;
//        hash_key_string cHashKey(szKey);
//
//        if (bHaveDate)
//            iRst = cContainer->find(iIdx, cHashKey, tm, cList);
//        else
//            iRst = cContainer->find(iIdx, cHashKey, cList);
//
//        if( iRst > 0 )
//        {
//            AISTD list<CBpsImsiNumber>::iterator it;
//        
//            for (it = cList.begin(); it != cList.end(); ++it)
//            {
//                //printf("jichao check number and time:szImsiNumber=%d,it->get_StartImsi()=%d,it->get_EndImsi()=%d\n",atoi(szImsiNumber),atoi(it->get_StartImsi()),atoi(it->get_EndImsi()));
//                if ( 0 == strcmp(it->get_StartImsi(), it->get_EndImsi()))
//                {
//                    cBpsImsiNumber = *it;
//                                        return 0;
//                }
//                
//                //if( atol(szImsiNumber) >= atol(it->get_StartImsi()) && atol(szImsiNumber) <= atol(it->get_EndImsi()) )
//                if(strlen(szImsiNumber) < strlen(it->get_StartImsi()) || strlen(szImsiNumber) > strlen(it->get_EndImsi()))
//                {
//                    continue;
//                }
//                else
//                {
//                    if (strcmp(szImsiNumber, it->get_StartImsi()) >= 0 && strcmp(szImsiNumber, it->get_EndImsi()) <= 0)
//                    {
//                        //printf("get imsi info\n");
//                        cBpsImsiNumber = *it;
//                                            return 0;
//                    }
//                }
//            }
//        }
//    }
//    return -2;
//}

// TODO
int CODACClient::findProvByAreaCode(const xc::CSnapshot &cSnapShot,
                                    const char *pAreaCode,
                                    CSysProv &cProvData,
                                    const time_t &tm)
{
    int iRst = odac_app_lookup_CSysProvByArea(cSnapShot, pAreaCode, tm, cProvData);

    if (iRst != 0)
    {
        return -1;
    }

    return 0;
}

int CODACClient::findCBpsMgCampInfoByCampaignId(const xc::CSnapshot &cSnapShot,
                                   const char *cszCampaignId,
                                   CBpsMgCampInfo &cBpsMgCampInfo,
                                   const time_t &tm)
{
    int iRst = odac_app_lookup_CBpsMgCampInfo(cSnapShot, cszCampaignId, tm, cBpsMgCampInfo);

    if (iRst != 0)
    {
        return -1;
    }

    return 0;
}

int CODACClient::findSpBusiDescByServCode2Only(const xc::CSnapshot &cSnapShot,
                                               const char *pszServCode2,
                                               CBpsAddSpBusiDesc &cSpBusiDescData,
                                               const time_t &tm)
{
    AISTD list<CBpsAddSpBusiDesc> cList;
    int iRst = odac_app_lookup_CBpsAddSpBusiDescByServCode2(cSnapShot, pszServCode2, tm, cList);

    if (iRst != 0)
    {
        return iRst;
    }
    else
    {
        if (cList.size() > 0)
        {
            cSpBusiDescData = cList.front();
        }
    }

    return -1;
}

int CODACClient::FindVBpsIpv4Address(const xc::CSnapshot &cSnapShot, const char* address, const time_t &tm, CVBpsIpv4Address &cVBpsIpv4Address)
{
    /*
    int iRst = odac_app_lookup_CVBpsIpv4Address(cSnapShot, address, tm, cVBpsIpv4Address);
    if(iRst != 0)
    {
        return iRst;
    }
    
    return 0;
    */

    int iRst = 0;
    AISTD list<CVBpsIpv4Address> cList;
    
    iRst = odac_app_lookup_CVBpsIpv4Address(cSnapShot, address, tm, cList);
    if(iRst != 0)
    {
        return iRst;
    }
    else
    {
        AISTD list<CVBpsIpv4Address>::iterator it;

        for (it = cList.begin(); it != cList.end(); ++it)
        {
            LOG_TRACE("address length = %d, it.ip length = %d\n", strlen(address), strlen((*it).get_key1()) );
            cVBpsIpv4Address = *it;
            return 0;
        }
    }
    return -1;

}

int CODACClient::FindBpsIpv4AddressRange(const xc::CSnapshot &cSnapShot,
    const char* address, const time_t &tm,CBpsIpv4AddressRange &cBpsIpv4AddressRange)
{
    int iRst = 0;
    AISTD list<CBpsIpv4AddressRange> cList;
    
    iRst = odac_app_lookup_CBpsIpv4AddressRange(cSnapShot, address, tm, cList);
    if(iRst != 0)
    {
        return iRst;
    }
    else
    {
        AISTD list<CBpsIpv4AddressRange>::iterator it;

        for (it = cList.begin(); it != cList.end(); ++it)
        {
            if(strcmp(it->get_startIp(),it->get_endIp()) == 0){
                cBpsIpv4AddressRange = *it;
                return 0;
            }
            if(strcmp(address,it->get_startIp())>=0 && strcmp(address,it->get_endIp())<=0){
                cBpsIpv4AddressRange = *it;
                return 0;
            }
        }
    }
    return -1;
}

int CODACClient::FindBpsIpv6AddressRange(const xc::CSnapshot &cSnapShot,
    const char* address, const time_t &tm,CBpsIpv6AddressRange &cBpsIpv6AddressRange)
{
    int iRst = 0;
    AISTD list<CBpsIpv6AddressRange> cList;

    iRst = odac_app_lookup_CBpsIpv6AddressRange(cSnapShot, address, tm, cList);
    if(iRst != 0)
    {
        return iRst;
    }
    else
    {
        AISTD list<CBpsIpv6AddressRange>::iterator it;

        for (it = cList.begin(); it != cList.end(); ++it)
        {
            if(strcmp(it->get_startIp(),it->get_endIp()) == 0){
                cBpsIpv6AddressRange = *it;
                return 0;
            }
            if(strcmp(address,it->get_startIp())>=0 && strcmp(address,it->get_endIp())<=0){
                cBpsIpv6AddressRange = *it;
                return 0;
            }
        }
    }
    return -1;
}
int CODACClient::findBpsSjDefaultPrice(const xc::CSnapshot &cSnapShot, const char *pszSettlementType, CBpsSjSmsDefaultPrice &cBpsSjSmsDefaultPriceData, const time_t &tm)
{
    int iRst = 0;
    AISTD list<CBpsSjSmsDefaultPrice> cList;

    iRst = odac_app_lookup_CBpsSjDefaultPrice(cSnapShot, pszSettlementType, tm, cList);
    if(iRst != 0)
    {
    return iRst;
    }
    else
    {
        AISTD list<CBpsSjSmsDefaultPrice>::iterator it;
    
        for (it = cList.begin(); it != cList.end(); ++it)
        {
            cBpsSjSmsDefaultPriceData = *it;
                        return 0;
        }
    }

    return -1;
}

ODAC_END_NAMESPACE_DECLARE
//#endif // __ODAC__
