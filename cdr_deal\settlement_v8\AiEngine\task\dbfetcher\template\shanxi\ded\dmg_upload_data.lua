require "dbfetcher_util"

function main(args)
    -- 获取数据库连接
    local conn = getConnect()
    -- 获取任务 ID
    local inTaskId = getCode(args, "TASK_ID")

    writeLog("开始处理")

    -- 获取 BILL_MONTH 参数
    billMonth = getBillMonth(conn, inTaskId)
    
    -- 如果获取不到，返回错误
    if not billMonth then
        writeLog("未提供 BILL_MONTH 参数")
        return -1
    end

    -- 实际结算月份等于出账月份前两月，取YYYYMM
    settleMonth = string.sub(getAdjustDate(billMonth, -2), 1, 6)  

    writeLog("BILL_MONTH:" .. billMonth .. "  实际结算月份:" .. settleMonth)

    local delSql = [[delete from jsdr.plt_dmg_up where src_serv_type = 'DMG']]
    writeLog("删除历史数据")
    executeSQL(conn, delSql)
    conn:commit()

        local insertSql = [[
        INSERT INTO jsdr.plt_dmg_up (
            bill_month,
            ded_head,
            serv_type,
            ded_type,
            done_code,
            bill_id,
            sp_code,
            operator_code,
            props_code,
            channel_code,
            bill_type,
            use_time,
            ded_time,
            ded_fee,
            call_type,
            content_id,
            order_id,
            tran_code,
            keep_field_1,
            keep_field_2,
            keep_field_3,
            src_serv_type
        )
        SELECT
            ']] .. billMonth .. [[',
            record_type,
            busi_type,
            audit_type,
            seq_id,
            msisdn,
            sp_code,
            oper_code,
            property_code,
            channel_code,
            charge_type,
            use_time,
            audit_time,
            audit_fee,
            cdr_type,
            content_id,
            order_id,
            transaction_id,
            charge_seq,
            cdr_seq,
            mrkt_ctvty_id,
            'DMG'
        FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth

    writeLog("插入数据")
    executeSQL(conn, insertSql)
    conn:commit()
    ------------------------------------------------------
    writeLog("处理完成")
    conn:close()
    return 0
end

environment = os.getenv("ENVIRONMENT")
if environment == "TEST" then
    main("TASK_ID=17990|BILL_MONTH=202502")
    os.exit()
end
