require "dbfetcher_util"

function main(args)
    -- 获取数据库连接
    local conn = getConnect()
    -- 获取任务 ID
    local inTaskId = getCode(args, "TASK_ID")

    writeLog("开始处理")

    -- 获取 BILL_MONTH 参数
    billMonth = getBillMonth(conn, inTaskId)
    
    -- 如果获取不到，返回错误
    if not billMonth then
        writeLog("未提供 BILL_MONTH 参数")
        return -1
    end

    -- 实际结算月份等于出账月份前两月，取YYYYMM
    settleMonth = string.sub(getAdjustDate(billMonth, -2), 1, 6)  

    writeLog("BILL_MONTH:" .. billMonth .. "  实际结算月份:" .. settleMonth)

    local delSql = [[delete from jsdr.dr_ded_data where src_serv_type = 'DGP']]
    writeLog("删除历史数据")
    executeSQL(conn, delSql)
    conn:commit()

    local insertSql = [[
        INSERT INTO jsdr.dr_ded_data (
            bill_month,
            ded_head,
            serv_type,
            ded_type,
            done_code,
            bill_id,
            sp_code,
            operator_code,
            props_code,
            channel_code,
            bill_type,
            use_time,
            ded_time,
            ded_fee,
            call_type,
            content_id,
            serial_number,
            src_serv_type
        )
        SELECT
            ']] .. billMonth .. [[',
            rec_type,
            busi_type,
            deduct_type,
            cdr_seq,
            msisdn,
            sp_code,
            oper_code,
            content_code,
            channel_code,
            charge_type,
            start_datetime,
            deduct_datetime,
            deduct_fee,
            cdr_type,
            game_id,
            msg_id,
            'DGP'
        FROM jsdr.dr_dgp_audit_inner_]] .. settleMonth

    writeLog("插入数据")
    executeSQL(conn, insertSql)
    conn:commit()
    ------------------------------------------------------
    writeLog("处理完成")
    conn:close()
    return 0
end

environment = os.getenv("ENVIRONMENT")
if environment == "TEST" then
    main("TASK_ID=17990|BILL_MONTH=202502")
    os.exit()
end
