﻿#ifndef __WBASS_ANALYSIS_PUBLIC_H__
#define __WBASS_ANALYSIS_PUBLIC_H__
#include "xquery_business.h"
#include "analysis_comm_global.h"
#include "impl_common.h"
#include "../rpl/rpl_initglobal.h"
#include "implement_base_define.h"
#include "xload_business.h"
#include "odac_client.h"

int NumberAnalysis(xc::CSnapshot & snapShot, 
            const char* p_pchNumber,
            int         nCallingType,
            const char* p_pMscId,
            char*       p_pchCenterArea,
            NumberInfo* p_pSNumber,
            int32 m_nServiceId);
int NumberAnalysisNew(xc::CSnapshot & snapShot, 
            const char  *p_pchNumber,
            int32          nCallingType , //1  主叫， 2 被叫
            const char  *p_pMscId  ,      //交换机ID,NOKIA交换机被叫号即使是长途也没有0
            int64    iStartTime,          //判断局数据生,失效时间
            const char        *p_pchCenterArea,
            NumberInfo*  p_pSNumber,
            int32 m_nServiceId);

//getLongType 获取长途类型  根据号码和结算所在地的关系判断长途类型 
int getLongType(xc::CSnapshot & snapShot, 
            const char* p_FromAreaCode,       
            const char* p_ToAreaCode,
            const time_t tm);  
//qinhaiping add 20051012
// getRoamType  获取漫游类型  根据被叫号码和交换机所在地 
int getRoamType(xc::CSnapshot & snapShot, 
            const char *m_szHomeCountryCode,const char *m_szAccessNumber,
            const char *m_szHomeAreaCode,const char *m_szProvId,
            const char *p_pRoamAreaCode,const char *p_pCallType);
//getGsmMscInfo 获取交换机信息  根据交换机ID,得到交换机的其他信息
int GetGsmMscInfo(xc::CSnapshot & snapShot, 
            char    *p_pchSwitchId,
            GsmMscInfo *p_SSwitchInfo);
/*
int FindNpInfo(xc::CSnapshot & snapShot,
            const char *szNumber,
            int64   iDateTime,
            SNpInfo *p_pNpInfo);
*/

//hujie add
int GetGsmMscInfoNew(xc::CSnapshot & snapShot, 
            const char    *p_pchSwitchId,
            int64   iDateTime,
            GsmMscInfo *p_SSwitchInfo);
//getGsmRouterInfo  获取路由信息,根据交换机ID和路由ID,得到路由的其他信息 
int GetGsmRouterInfo(xc::CSnapshot & snapShot, 
            const char    *p_pchSwitchId,
            const char    *p_pchTrunkId,
            int64   iDateTime,
            GsmRouterInfo *p_SRouterInfo);
int GetGsmRouterInfoNew(xc::CSnapshot & snapShot, 
            const char    *p_pchSwitchId,
            const char    *p_pchTrunkId,
            const char    *p_pchAreaCode,
            int64   iDateTime,
            GsmRouterInfo *p_SRouterInfo);
int GetGsmRouterInfoNewByTrunk(xc::CSnapshot & snapShot, 
            char    *p_pchSwitchId,
            char    *p_pchTrunkId,
            char    *p_pchTrunkFlag,
            char    *p_pchAreaCode,
            int64   iDateTime,
            GsmRouterInfo *p_SRouterInfo);
//getRoamAreaCode   根据漫游号码得到漫游地区                     
int GetRoamAreaCode(xc::CSnapshot & snapShot, 
            char *pMsrnCode,
            char *pAreaCode);
//hujie add:
int GetRoamAreaCodeNew(xc::CSnapshot & snapShot, 
            const char *p_pMsrnCode,
            int64   iDateTime, 
            char *p_pAreaCode);
// 短信号码分析   
int SMSNumberAnalysis(xc::CSnapshot & snapShot,
            const char    *p_pchNumber,
            const char    *p_pArea,
            SMSNumberInfo *p_pSNumber); 
//hujie add
int SMSNumberAnalysisNew(xc::CSnapshot & snapShot, 
            const char    *p_pchNumber,
            const char    *p_pArea,
            const int    iStartTime,
            SMSNumberInfo *p_pSNumber);
int SPNumberAnalysis(xc::CSnapshot & snapShot,
            const char   *p_pchNumber,
            int64 iDateTime,
            SPNumberInfo *p_pSNumber);  
int SPNumberSpAnalysis(xc::CSnapshot & snapShot,
            const char   *p_pchNumber,
            int64   iDateTime,
            SPNumberInfo *p_pSNumber);  
//add by tangyf:根据serv_code2得到SP_CODE,要求以失效时间最大的SP代码为准     
int SPNumberServCodeAnalysis(xc::CSnapshot & snapShot, 
            const char   *p_pchNumber,
            int64   iDateTime, 
            SPNumberInfo *p_pSNumber);
int AccCodeAnalysis(xc::CSnapshot & snapShot,
            const int32  iAccCode,
            const char   *p_pchOperCode, 
            PlatformInfo *p_pSNumber);  
int OperCodeAnalysis(xc::CSnapshot & snapShot,
            const char    *p_pchNumber,
            int64 iDateTime,
            AddOperatorInfo *p_pSNumber);
//add by zhufd at 20190310 for MCHK
int  OperCodeMCCAnalysis(xc::CSnapshot & snapShot, 
            const char    *p_opercode,
            const char      *p_homearea,
            int64   iStartTime, 
            AddOperatorInfo *p_pSNumber
);
int IsmgCodeAnalysis(xc::CSnapshot & snapShot, 
            const char *p_pchNumber,
            char    *pAreaCode);    
//zhoushang add
int ServCodeAnalysisNew(xc::CSnapshot & snapShot, 
            const char *pszServType,
            const char *p_pchNumber,
            int64   iDateTime,
            const int32 iMacthType,
            BsmsInfo* p_pSNumber);
//根据访问地获取运营商信息                                
int VisitInfoAnalysis(xc::CSnapshot & snapShot, 
            const char    *p_pchNumber,
            int64 iDateTime,
            AddOperatorInfo *p_pSNumber);    
//从imsi号获取hlrcode,并根据hlrcode判断归属地，如果找到，归属地在szAreaCode中，否则szAreaCode返回'0'    
int ImsiAreaCodeAnalysis(xc::CSnapshot & snapShot, 
            const char *p_pchImsiNumber,
            char    *szAreaCode);    
//hujie add
int ImsiAreaCodeAnalysisNew(xc::CSnapshot & snapShot,
            const char  *p_pchImsiNumber,
            int64   iDateTime,
            char    *szAreaCode);   
//  内蒙查找交换机名称
int GetMscIdFromFileName(xc::CSnapshot & snapShot, 
            const char *p_pchFileName,
            char *p_pchMscId); 
//wanghl add 
int FindGprsIpaddr(xc::CSnapshot & snapShot, 
            const char *p_pchProvCode,
            const char *p_pchIpaddr,
            int64   iDateTime);
int getProvByAreaCode(xc::CSnapshot & snapShot,
            const char* pchAreaCode,
            char* pchProv,int64   iStartTime);
// 特殊号码分析                           
int SpecialUserAnalysis(xc::CSnapshot & snapShot,
            const char * p_pchUserNumber, 
            const char * p_pchAreaCode, 
            int64 iDateTime,
            SpecialUserInfo * p_pSNumber);

// 特殊号码分析, 用于专网
int SpecialUserAnalysisSN(xc::CSnapshot & snapShot,
            const char * p_pchUserNumber, 
            const char * p_pchAreaCode, 
            const char * p_pchBureauCode, 
            int64   iDateTime,
            SpecialUserInfo * p_pSNumber);

int FindSpRatio(xc::CSnapshot & snapShot, const char *p_pchSpCode,
            const char *p_pchOperatorCode,  //业务代码
            const int nType,   //类型
            int64   iDateTime,
            SSpSettleRatio* p_pRatioInfo);  
#ifdef __NP_QUERY__
  //用于携号转网查询 gai 2019-06     
  int FindNpInfo(xc::CSnapshot & snapShot,const char    * szNumber,
            const char *szDate,SNpInfo &p_pNpInfo);
  //拆分ip与port
int SplitIpPort(xc::CSnapshot & snapShot,
            const char *szPort,
            const char *szIp);
#endif
int IvrRatioAnalysis(xc::CSnapshot & snapShot, 
            const char *szOper,
            const char *szSpCode,
            int64   iDateTime,
            const char *szServ,
            const int32 ifee,
            const int32 ikey,
            SIvrRatio *pSIvrRatio);
int findBpsCardHomeProv(xc::CSnapshot & snapShot,
            const char* szCardCode,
            int64   iDateTime,
            char* szProv);

int ImsiOperInfoAnalysis(xc::CSnapshot & snapShot,
            const char *piImsiCode,
            int64   iDateTime,
            ImsiOperInfo *poImsiOperInfo);
//added by jichao 20210323
int ImsiNumberAnalysis(xc::CSnapshot & snapShot,
            char *piImsi,
            int64   iDateTime,
            ImsiNumberInfo *poImsiNumberInfo);
            
int getUserLocation(xc::CSnapshot & snapShot,const char *pszLacId,const char *pszCellId,int64 iDateTime, UserLocationInfo *pUserLocationInfo);

int32 getDaysInMonth(const    string&    v);

int getDateFromFileName(char *pszFileName, char *fileNameDate);

int IpAddressAnalysis(xc::CSnapshot & snapShot, const char *address, int64 iDateTime, int findType, const char *ipType, char *areaCode);

int getSjDefaultPriceInfo(xc::CSnapshot & snapShot,const char* settlementType,int64 iDateTime,int32 &settlementPrice);

#endif //__WBASS_ANALYSIS_PUBLIC_H__
