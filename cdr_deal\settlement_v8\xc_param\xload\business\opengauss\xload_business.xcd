<?xml version="1.0" encoding="UTF-8"?>
<xc split="false" lowercasecodesytle="true">
	<hcode>
		<![CDATA[
			#include "express_compile.h"
			#include "xload_common_func.h"
			#include "expanalyze_base.h"
			#include "expanalyze_data.h"
			#include "split_sect_data.h"
  		]]>
	</hcode>
	<loaddbcode>
		<![CDATA[
/*
			int32 iRet = 0;
			//init billing factors
			cExprAnalyze.clearTableContainer();
      		cExprAnalyze.set_userTable(cLoadOption.m_mapSqlVariable);

			iRet = cExprAnalyze.initBillService(cDbConn);
			if (iRet != 0 )
			{
				LOG_TRACE("initBillService failed!");
			}
			iRet = cExprAnalyze.initAccessType(cDbConn);
			if (iRet != 0 )
			{
				LOG_TRACE("initAccessType failed!");
			}
			iRet = cExprAnalyze.initOppNumberType(cDbConn);
			if (iRet != 0 )
			{
				LOG_TRACE("initOppNumberType failed!");
			}
			iRet = cExprAnalyze.initTollRateType(cDbConn);
			if (iRet != 0 )
			{
				LOG_TRACE("initTollRateType failed!");
			}
			iRet = cExprAnalyze.initGprsApnType(cDbConn);
			if (iRet != 0 )
			{
				LOG_TRACE("initGprsApnType failed!");
			}
			iRet = cExprAnalyze.initGprsServiceCode(cDbConn);
			if (iRet != 0 )
			{
				LOG_TRACE("initGprsServiceCode failed!");
			}
			iRet = cExprAnalyze.initSpInfoType(cDbConn);
			if (iRet != 0 )
			{
				LOG_TRACE("initSpInfoType failed!");
			}
			iRet = cExprAnalyze.initSmsClassType(cDbConn);
			if (iRet != 0 )
			{
				LOG_TRACE("initSmsClassType failed!");
			}	
			iRet = cExprAnalyze.initDeviceTypeList(cDbConn);
			if (iRet != 0 )
			{
				LOG_TRACE("initDeviceTypeList failed!");
			}
*/
			gSplitSectData.init();
		]]>
	</loaddbcode>
	<group namespace="settle" groupname="ANALYSE_GROUP">
		<container indexs="KEY1" name="BPS_ACCCODE_REL" type="multi_index">
			<sql>
				SELECT CONCAT(SP_CODE,'!',OPERATOR_CODE) AS KEY1,ACC_CODE,SUBJECT_ACC_CODE,SUBJECT_TYPE,SP_CODE,OPERATOR_CODE,BUSI_TYPE,ACC_NAME,ACC_BUSI_NAME,IS_DISCOUNT,to_number('20010101000000') VALID_DATE,to_number('20991230235959')  EXPIRE_DATE
				FROM ^[settle.BPS_ACCCODE_REL^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAcccodeRel>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsAcccodeRel::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_ACCESS_NUMBER" type="match">
			<sql>
				SELECT CONCAT('0',ACCESS_NUMBER) as KEY1, ACCESS_NUMBER, ACCESS_NUMBER_NAME, ANALYSIS_TYPE, OPERATOR_ID, NUMBER_TYPE, CIC_FLAG, to_number('20010101000000') VALID_DATE, to_number('20991230235959')  EXPIRE_DATE
				FROM ^[settle.BPS_ACCESS_NUMBER^] 
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAccessNumber>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsAccessNumber::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_ADD_CARD_FEE_CODE_SEG" type="multi_index">
			<sql>
				SELECT START_MSISDN as KEY1,CARD_TYPE,START_MSISDN,END_MSISDN,FEE_CODE,FEE,to_number('20010101000000') AS VALID_DATE , to_number('20991230235959') AS EXPIRE_DATE
				FROM ^[settle.BPS_ADD_CARD_FEE_CODE_SEG^] ORDER BY START_MSISDN ASC
			</sql> 
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAddCardFeeCodeSeg>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        CBpsAddCardFeeCodeSeg::Type::key_list cKeyList;
			cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_ADD_FILENAMETOMSC" type="multi_index">
			<sql>
				SELECT FILENAME as KEY1, FILENAME,MSC_ID,to_number(rpad(VALID_DATE,14,0))  VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0))  EXPIRE_DATE
				FROM ^[settle.BPS_ADD_FILENAMETOMSC^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAddFilenametomsc>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsAddFilenametomsc::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_ADD_I_SMS_RATE" type="multi_index">
			<sql>
				SELECT COUNTRY_CODE as KEY1, COUNTRY_CODE, CALL_TYPE,RATE,ZONE_ID,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0))  EXPIRE_DATE
				FROM ^[settle.BPS_ADD_I_SMS_RATE^] 
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAddISmsRate>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsAddISmsRate::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_ADD_JUDGE_ZONE" type="match">
			<sql>
				SELECT NUMBER_PREFIX as KEY1, NUMBER_PREFIX, ZONE_ID,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_ADD_JUDGE_ZONE^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAddJudgeZone>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsAddJudgeZone::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_ADD_MMM_CODE" type="match">
			<sql>
				SELECT MMM_CODE as KEY1, MMM_CODE,AREA_CODE,OPERATOR_ID,NET_TYPE,PROVINCE_ID,to_number(rpad(EFF_DATE,14,0))  VALID_DATE,to_number(rpad(EXP_DATE,14,0))  EXPIRE_DATE
				FROM ^[settle.BPS_ADD_MMM_CODE^] 
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAddMmmCode>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsAddMmmCode::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_ADD_MOBILE_SEG" type="match">
			<sql>
				SELECT M_CODE_SEG as KEY1,M_CODE_SEG, OPERATOR_ID,NET_TYPE,to_number('20010101000000') AS VALID_DATE , to_number('20991230235959') AS EXPIRE_DATE
				FROM ^[settle.BPS_ADD_MOBILE_SEG^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAddMobileSeg>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        CBpsAddMobileSeg::Type::key_list cKeyList;
			cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		
		<container indexs="KEY1;KEY2" name="BPS_ADD_PLATFORM_BUSI_DESC" type="match">
			<sql>
				SELECT  CONCAT(ACC_CODE, '|',OPER_CODE) as KEY1, ACC_CODE as KEY2, OPER_ID,ACC_CODE,OPER_CODE,BUSI_NAME,PAL_TYPE,SERV_RANGE,to_number(rpad(EFF_DATE,14,0)) VALID_DATE ,to_number(rpad(EXP_DATE,14,0))  EXPIRE_DATE
				FROM ^[settle.BPS_ADD_PLATFORM_BUSI_DESC^]  
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAddPlatformBusiDesc>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsAddPlatformBusiDesc::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1;KEY2;KEY3" name="BPS_ADD_SP_BUSI_DESC" type="match">
			<sql>
				SELECT SP_CODE as KEY1, SERV_CODE2 as KEY2, BUSI_CODE as KEY3, OPER_ID,SP_CODE,SP_NAME,SERV_CODE1,SERV_CODE2,BUSI_TYPE,BUSI_CODE,BUSI_NAME,FILTER_FLAG,PAL_TYPE,ACC_AREA,SERV_RANGE,COMM_FREE_VALUE,to_number(rpad(EFF_DATE,14,0)) VALID_DATE ,to_number(rpad(EXP_DATE,14,0)) as EXPIRE_DATE
				FROM ^[settle.BPS_ADD_SP_BUSI_DESC^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAddSpBusiDesc>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsAddSpBusiDesc::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_ADD_SP_PROD" type="multi_index">
			<sql>
				SELECT BILL_MONTH as KEY1, USER_CODE,BILL_MONTH,HORTATION_FEE,PUNISHMENT_FEE,REMUNERATION_FEE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_ADD_SP_PROD^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAddSpProd>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsAddSpProd::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_ADD_SP_SMS" type="multi_index">
			<sql>
				SELECT SERV_CODE as KEY1, SP_CODE,SP_NAME,PROV_CODE,COUNTRY_CODE,SERV_CODE,to_number(rpad(START_TIME,14,0))  VALID_DATE ,to_number(rpad(END_TIME,14,0))  EXPIRE_DATE
				FROM ^[settle.BPS_ADD_SP_SMS^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAddSpSms>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsAddSpSms::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_BORDER_ROAM" type="multi_index">
			<sql>
				SELECT CONCAT(MSC_ID, ':',LAC_ID, ':', CELL_ID, ':', AREA_CODE) as KEY1, MSC_ID, UPPER(LAC_ID) LAC_ID, UPPER(CELL_ID) CELL_ID, AREA_CODE, ROAM_TYPE, RATING_FLAG, to_number(rpad(VALID_DATE,14,0)) VALID_DATE, to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_BORDER_ROAM^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsBorderRoam>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsBorderRoam::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_BSMS_SERVICECODE" type="match">
			<sql>
				SELECT CONCAT(SERV_TYPE, ':', SERV_CODE) AS KEY1, SERV_TYPE,SERV_CODE,PROV_CODE,to_number(rpad(VALID_DATE,14,0))  VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE,is_bal_port,settlement_price1,settlement_price2,settlement_price3,settlement_price4,settlement_price5 
				FROM ^[settle.BPS_BSMS_SERVICECODE^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsBsmsServicecode>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsBsmsServicecode::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_BUSINESS_AREA_REL" type="multi_index">
			<sql>
				SELECT CONCAT(AREA_CODE, '!', BUSINESS_AREA_CODE1, '!', BUSINESS_AREA_CODE2) AS KEY1, AREA_CODE,BUSINESS_AREA_CODE1,BUSINESS_AREA_CODE2,CONNECT_TYPE,to_number('20010101000000')  VALID_DATE ,to_number('20991230235959') EXPIRE_DATE
				FROM ^[settle.BPS_BUSINESS_AREA_REL^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsBusinessAreaRel>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsBusinessAreaRel::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_CARD_HOME_PROV" type="multi_index">
			<sql>
				SELECT CARD_CODE AS KEY1, PROV_CODE,CARD_CODE,to_number(rpad(VALID_DATE,14,0))  VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0))  EXPIRE_DATE
				FROM ^[settle.BPS_CARD_HOME_PROV^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsCardHomeProv>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsCardHomeProv::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1;KEY2" name="BPS_CARD_PREFIX" type="match">
			<sql>
				SELECT CARD_PREFIX as KEY1, CONCAT(CARD_PREFIX, '!', CARD_TYPE) as KEY2, CARD_PREFIX,CARD_TYPE,AREA_CODE,to_number('20010101000000') VALID_DATE,to_number('20991230235959')  EXPIRE_DATE
				FROM ^[settle.BPS_CARD_PREFIX^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsCardPrefix>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsCardPrefix::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_CARRIER_IMSI" type="match">
			<sql>
				SELECT IMSI_CODE as KEY1, IMSI_CODE,CARRIER_ID,OPEN_DATE,COUNTRY_CODE,OPER_CODE,to_number('20010101000000')  VALID_DATE,to_number('20991230235959')  EXPIRE_DATE
				FROM ^[settle.BPS_CARRIER_IMSI^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsCarrierImsi>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsCarrierImsi::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_CCM" type="multi_index">
			<sql>
				SELECT CCM_IP as KEY1, CCM_NAME,CCM_IP,SACP_IP,PROV_CODE,AREA_CODE,BUREAU_CODE,to_number('20010101000000') VALID_DATE,to_number('20991230235959') EXPIRE_DATE
				FROM ^[settle.BPS_CCM^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsCcm>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsCcm::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_CHANNEL_TYPE" type="multi_index">
			<sql>
				SELECT CONCAT(SERVICE, '!', CODEC, DIRECTION) as KEY1, SERVICE,BANDWIDTH_MIN,BANDWIDTH_MAX,CODEC,DIRECTION,CH_TYPE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0))  EXPIRE_DATE
				FROM ^[settle.BPS_CHANNEL_TYPE^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsChannelType>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsChannelType::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_CI_CONTENT_DATA" type="multi_index">
			<sql>
				SELECT CONTENT_ID as KEY1, CONTENT_ID,CONTENT_TYPE,CONTENT_NAME,SP_CODE,OPER_CODE,BILL_FLAG,PROV_CODE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_CI_CONTENT_DATA^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsCiContentData>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsCiContentData::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_FREE_GSM_ROUTER" type="multi_index">
			<sql>
				SELECT CONCAT(MSC_ID, '!', TRUNK_ID) as KEY1, MSC_ID,TRUNK_ID,SETTLER_ID,AREA_CODE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_FREE_GSM_ROUTER^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsFreeGsmRouter>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsFreeGsmRouter::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_GPRS_IPADDR_INFO" type="multi_index">
			<sql>
				SELECT PROV_CODE as KEY1, BEGIN_IP,END_IP,PROV_CODE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_GPRS_IPADDR_INFO^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsGprsIpaddrInfo>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsGprsIpaddrInfo::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_GSM_MSC" type="multi_index">
			<sql>
				SELECT MSC_ID as KEY1, MSC_ID,MSC_NAME,MSC_TYPE,VERSION,MSC_SEQ,PROV_CODE,AREA_CODE,BUREAU_CODE,LOCATION_TYPE,CALLING_MODIFY,CALLED_MODIFY,to_number('20010101000000') VALID_DATE,to_number('20991230235959')  EXPIRE_DATE
				FROM ^[settle.BPS_GSM_MSC^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsGsmMsc>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsGsmMsc::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1;KEY2" name="BPS_GSM_ROUTER" type="multi_index">
			<sql>
				SELECT CONCAT(MSC_ID, '!', TRUNK_ID) as KEY1, CONCAT(MSC_ID, '!', TRUNK_ID, '!', AREA_CODE) as KEY2, MSC_ID,TRUNK_ID,IN_TRUNK_BUSI_ID,OUT_TRUNK_BUSI_ID,SETTLER_ID,TOLL_TYPE,AREA_CODE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0))  EXPIRE_DATE
				FROM ^[settle.BPS_GSM_ROUTER^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsGsmRouter>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsGsmRouter::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_HLR_TRADEMARK" type="match">
			<sql>
				SELECT HLR_CODE as KEY1, HLR_CODE,TRADEMARK,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_HLR_TRADEMARK^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsHlrTrademark>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsHlrTrademark::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_HLR" type="match">
			<sql>
				SELECT HLR_CODE as KEY1, HLR_CODE,BUREAU_CODE,AREA_CODE,OPERATOR_ID,NET_TYPE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0))  EXPIRE_DATE
				FROM ^[settle.BPS_HLR^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsHlr>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsHlr::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_IMSI_HLR_REGULAR" type="match">
			<sql>
				SELECT IMSI_HEAD as KEY1, IMSI_HEAD,CHECK_INDEX,CHECK_VALUE,HLR_CODE,FLAG,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_IMSI_HLR_REGULAR^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsImsiHlrRegular>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsImsiHlrRegular::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_IMSI_OPER_INFO" type="multi_index">
			<sql>
				SELECT IMSI_CODE as KEY1,IMSI_CODE,IMSI_CODE_NAME,FIND_TYPE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0))  EXPIRE_DATE
				FROM ^[settle.BPS_IMSI_OPER_INFO^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsImsiOperInfo>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsImsiOperInfo::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_IR_USER_LIMIT" type="multi_index">
			<sql>
				SELECT CONCAT(PARTNER_ID, '!', LIMIT_TYPE) as KEY1,PARTNER_ID,LIMIT_TYPE,MIN_VALUE,MAX_VALUE,MIN_FEE,MAX_FEE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_IR_USER_LIMIT^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsIrUserLimit>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsIrUserLimit::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_ISMG" type="multi_index">
			<sql>
				SELECT ISMG_ID as KEY1,ISMG_ID,ISMG_NAME,PROV_CODE,REGION_CODE,AREA_CODE,to_number('20010101000000') VALID_DATE,to_number('20991230235959')  EXPIRE_DATE
				FROM ^[settle.BPS_ISMG^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsIsmg>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsIsmg::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_KOREA_ROAM_MSISDN" type="multi_index">
			<sql>
				SELECT ROAM_MSISDN as KEY1,ROAM_MSISDN,MSISDN,to_number('20010101000000') VALID_DATE,to_number('20991230235959') EXPIRE_DATE
				FROM ^[settle.BPS_KOREA_ROAM_MSISDN^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsKoreaRoamMsisdn>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsKoreaRoamMsisdn::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_LAC" type="multi_index">
			<sql>
				SELECT LAC as KEY1,LAC_ID,LAC,COUNTY_CODE,NET_TYPE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_LAC^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsLac>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsLac::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_MISN_NUMSEG" type="multi_index">
			<sql>
				SELECT NUMBER_HEAD as KEY1,NUMBER_HEAD,AREA_CODE,BUREAU_CODE,BUSINESS_AREA_CODE,OPERATOR_ID,NET_TYPE,to_number('20010101000000') VALID_DATE,to_number('20991230235959') EXPIRE_DATE
				FROM ^[settle.BPS_MISN_NUMSEG^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsMisnNumseg>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsMisnNumseg::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_MSC_CODE" type="multi_index">
			<sql>
				SELECT  CONCAT(TRUNK_GROUP, '!', TMP_MSC_CODE) as KEY1,TRUNK_GROUP,TMP_MSC_CODE,MSC_CODE,IN_OUT_TYPE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_MSC_CODE^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsMscCode>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsMscCode::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<!--
		<container indexs="KEY1" name="BPS_NP_USER" type="multi_index">
			<sql>
				SELECT SERIAL_NUMBER as KEY1,SERIAL_NUMBER,OPER_ID,NET_TYPE,AREA_CODE,PORT_OUT_OPER_ID,HOME_OPER_ID,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_NP_USER^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsNpUser>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsNpUser::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		-->
		<container indexs="KEY1" name="BPS_PSTN_NUMSEG" type="match">
			<sql>
				SELECT CONCAT(AREA_CODE, NUMBER_HEAD) as KEY1,AREA_CODE,NUMBER_HEAD,BUSINESS_AREA_CODE,OPERATOR_ID,NET_TYPE,to_number('20010101000000')  VALID_DATE ,to_number('20991230235959') EXPIRE_DATE
				FROM ^[settle.BPS_PSTN_NUMSEG^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsPstnNumseg>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsPstnNumseg::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_PSTN_ROUTER" type="multi_index">
			<sql>
				SELECT CONCAT(SWITCH_ID, '!', TRUNK_ID) as KEY1,SWITCH_ID,TRUNK_ID,IN_TRUNK_BUSI_ID,OUT_TRUNK_BUSI_ID,SETTLER_ID,TOLL_TYPE,AREA_CODE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_PSTN_ROUTER^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsPstnRouter>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsPstnRouter::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_PSTN_SWITCH" type="multi_index">
			<sql>
				SELECT SWITCH_ID as KEY1,SWITCH_ID,SWITCH_NAME,SWITCH_TYPE,VERSION,PROV_CODE,AREA_CODE,BUREAU_CODE,LOCATION_TYPE,SWITCH_SEQ,to_number(rpad(EFF_DATE,14,0))  VALID_DATE ,to_number(rpad(EXP_DATE,14,0))  EXPIRE_DATE
				FROM ^[settle.BPS_PSTN_SWITCH^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsPstnSwitch>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsPstnSwitch::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_RATE_TYPE" type="multi_index">
			<sql>
				SELECT CONCAT(ACCESS_NUMBER, '!', ORI_ZONE_CODE, '!', DEST_ZONE_CODE, '!', TOLL_GROUP_ID) as KEY1,ACCESS_NUMBER,TOLL_GROUP_ID,ORI_ZONE_CODE,DEST_ZONE_CODE,RATE_TYPE,TOLL_TYPE,to_number('20010101000000') VALID_DATE,to_number('20991230235959') EXPIRE_DATE
				FROM ^[settle.BPS_RATE_TYPE^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsRateType>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsRateType::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_SPECIAL_NUMBER" type="match">
			<sql>
				SELECT CONCAT(SERVICE_ID, SPECIAL_NUMBER) as KEY1,SERVICE_ID,SPECIAL_NUMBER,AREA_CODE,OPEN_AREA,SPECIAL_NUMBER_NAME,OPERATOR_ID,NUMBER_TYPE,NUMBER_ID,CONDITION_GROUP_ID,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_SPECIAL_NUMBER^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsSpecialNumber>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsSpecialNumber::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<!--key与其他字段有关，暂没处理-->
		<container indexs="KEY1" name="BPS_SPECIAL_USER" type="match">
			<sql>
				SELECT CONCAT(AREA_CODE,'!',jssd.colNameMaxSame(START_NUMBER,END_NUMBER)) as KEY1,START_NUMBER,END_NUMBER,NUMBER_TYPE,AREA_CODE,BUREAU_CODE,OPERATOR_ID,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_SPECIAL_USER^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsSpecialUser>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsSpecialUser::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BSR_SETTLE_ELEMENT" type="multi_index">
			<sql>
				SELECT CONCAT(SERVICE_ID, '!', BUSI_TYPE) as KEY1,SERVICE_ID,BUSI_TYPE,DR_TYPE,ELEMENT_ID,ELEMENT_NAME,XDR_LOCALTION_INDEX,ELEMENT_TYPE,ELEMENT_ATTR,to_number('20010101000000') AS VALID_DATE , to_number('20991230235959') AS EXPIRE_DATE
				FROM ^[settle.BSR_SETTLE_ELEMENT^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBsrSettleElement>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        CBsrSettleElement::Type::key_list cKeyList;
			cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_MG_CAMP_INFO" type="multi_index">
			<sql>
				SELECT CAMPAIGN_ID as KEY1,CAMPAIGN_ID,PROC_ID,OPER_TYPE,CAMPAIGN_NAME,CAMPAIGN_DESC,SERV_TYPE,SP_CODE,OPERATOR_CODE,MEMBER_TYPE,VALID_PROVINCE,BAL_TYPE,BAL_PRICE,START_DATE,END_DATE,BAL_MONTH,BEGIN_PERIOD,REC_CHANNEL,TIME_STAMP,START_CHECK_DATE,END_CHECK_DATE,to_number('20010101000000') AS VALID_DATE , to_number('20991230235959') AS EXPIRE_DATE
				FROM ^[settle.BPS_MG_CAMP_INFO^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsMgCampInfo>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        CBpsMgCampInfo::Type::key_list cKeyList;
			cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1;KEY2" name="SYS_CITY" type="match">
			<sql>
				SELECT AREA_CODE as KEY1,REGION_CODE as KEY2,AREA_CODE,CITY_NAME,PROV_CODE,REGION_CODE,to_number('20010101000000')  VALID_DATE,to_number('20991230235959') EXPIRE_DATE 
				FROM ^[settle.SYS_CITY^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CSysCity>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CSysCity::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="SYS_COUNTRY" type="match">
			<sql>
				SELECT COUNTRY_CODE as KEY1,COUNTRY_CODE,CHINESE_NAME,ENGLISH_NAME,COUNTRY_TYPE,to_number('20010101000000') AS VALID_DATE , to_number('20991230235959') AS EXPIRE_DATE
				FROM ^[settle.SYS_COUNTRY^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CSysCountry>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
					CSysCountry::Type::key_list cKeyList;
					cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
					cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
					cBean.GetKeyList(cKeyList);
					cEditContainer.insert(cKeyList, cBean);
					return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1;KEY2" name="SYS_PROV" type="match">
			<sql>
				SELECT PROV_CODE as KEY1,AREA_CODE as KEY2,PROV_CODE,PROV_NAME,AREA_CODE,DEFAULT_OPERATOR_ID,to_number('20010101000000') VALID_DATE ,  to_number('20991231000000') EXPIRE_DATE
				FROM ^[settle.SYS_PROV^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CSysProv>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CSysProv::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
        <container indexs="KEY1;KEY2" name="SYS_BUREAU" type="multi_index">
            <sql>
                SELECT BUREAU_CODE as KEY1,BUREAU_NAME,AREA_CODE as KEY2,IS_CITY,to_number('20010101000000') VALID_DATE ,  to_number('20991231000000') EXPIRE_DATE
                FROM ^[settle.SYS_BUREAU^]
            </sql>
            <cmpfunc>
                <![CDATA[
                    xload::base_cmp_func<CSysBureau>
                ]]>
            </cmpfunc>
            <loaddbcode>
                <![CDATA[
                    cSqliteInsert<<cBean;
                    cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
                    cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
                    CSysBureau::Type::key_list cKeyList;
                    cBean.GetKeyList(cKeyList);
                    cEditContainer.insert(cKeyList, cBean);
                    return true;
                ]]>
            </loaddbcode>
        </container>
		<container indexs="KEY1" name="V_BPS_LAC_AREA_REL" type="multi_index">
			<sql>
				SELECT LAC_ID as KEY1,LAC_ID,AREA_CODE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.V_BPS_LAC_AREA_REL^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CVBpsLacAreaRel>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CVBpsLacAreaRel::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="V_BPS_LAC_CEIL_COUNTY_REL" type="multi_index">
			<sql>
				SELECT CONCAT(LAC_ID, ',', CEIL_ID) as KEY1,LAC_ID,CEIL_ID,AREA_CODE,COUNTY_CODE,to_number('20010101000000') AS VALID_DATE , to_number('20991230235959') AS EXPIRE_DATE
				FROM ^[settle.V_BPS_LAC_CEIL_COUNTY_REL^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CVBpsLacCeilCountyRel>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        CVBpsLacCeilCountyRel::Type::key_list cKeyList;
			cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="V_BPS_RCDX_USER_REL" type="multi_index">
			<sql>
				SELECT SERVICE_CODE as KEY1,SERVICE_CODE,MSISDN,SPROM_ID,REGION_CODE,to_number('20010101000000') AS VALID_DATE , to_number('20991230235959') AS EXPIRE_DATE
				FROM ^[settle.V_BPS_RCDX_USER_REL^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CVBpsRcdxUserRel>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
					CVBpsRcdxUserRel::Type::key_list cKeyList;
					cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
					cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
					cBean.GetKeyList(cKeyList);
					cEditContainer.insert(cKeyList, cBean);
					return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="V_BSR_CONDITION" type="multi_index">
			<sql>
				SELECT CONCAT(SERVICE_ID, '|', DR_TYPE, '|', SETTLE_SIDE) as KEY1,RULE_ID,SERVICE_ID,DR_TYPE,SETTLE_SIDE,RULE_TYPE,ADDUP_COND_EXPR,PRI_LEVEL,COND_ID,COND_EXPR,to_number(rpad(EFFECT_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0))  EXPIRE_DATE
				FROM ^[settle.V_BSR_CONDITION^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CVBsrCondition>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
					CVBsrCondition::Type::key_list cKeyList;
					cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
					cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
					cBean.GetKeyList(cKeyList);
					cEditContainer.insert(cKeyList, cBean);
					return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="V_BSR_COND_EXPR" type="multi_index">
			<sql>
				SELECT COND_ID as KEY1,EXPR_ID,COND_ID,BASE_VALUE,MAX_VALUE,RATIO_NUMERATOR,RATIO_DEMOMINATOR,REF_CHARGE_ID,ACC_SETTLE_ID,CHARGE_DIR,SERIAL_NUMBER,IS_DEF_CALA_FLAG,DEF_CALCULATION,to_number('20010101000000') AS VALID_DATE , to_number('20991230235959') AS EXPIRE_DATE
				FROM ^[settle.V_BSR_COND_EXPR^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CVBsrCondExpr>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
					CVBsrCondExpr::Type::key_list cKeyList;
					cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
					cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
					cBean.GetKeyList(cKeyList);
					cEditContainer.insert(cKeyList, cBean);
					return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="V_BSR_CURVE_SEGMENTS" type="multi_index">
			<sql>
				SELECT COND_ID as KEY1,COND_ID,CURVE_ID,SEGMENT_ID,START_VAL,END_VAL,EXPR_ID,to_number('20010101000000') AS VALID_DATE , to_number('20991230235959') AS EXPIRE_DATE
				FROM ^[settle.V_BSR_CURVE_SEGMENTS^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CVBsrCurveSegments>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
					CVBsrCurveSegments::Type::key_list cKeyList;
					cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
					cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
					cBean.GetKeyList(cKeyList);
					cEditContainer.insert(cKeyList, cBean);
					return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1;KEY2;KEY3" name="V_IVR_RATIO" type="multi_index">
			<sql>
				SELECT OPER_CODE as KEY1,SP_CODE as KEY2,CONCAT(SP_CODE, '|', OPER_CODE) as KEY3,SERV_TYPE,SP_CODE,OPER_CODE,FEE,IN_PROP,OUT_PROP,SETTLE_MODE,SETTLE_AMOUNT,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.V_IVR_RATIO^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CVIvrRatio>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
					cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
					cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
					CVIvrRatio::Type::key_list cKeyList;
					cBean.GetKeyList(cKeyList);
					cEditContainer.insert(cKeyList, cBean);
					return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1;KEY2" name="V_SP_RATIO" type="multi_index">
			<sql>
				SELECT CONCAT(SP_CODE, '|', SP_TYPE) as KEY1,CONCAT(SP_CODE, '|', OPERATOR_CODE, '|', SP_TYPE) as KEY2,SP_CODE,OPERATOR_CODE,SP_TYPE,SP_RATIO,CM_RATIO,THIRD_RATIO,ACC_SETTLE_ID,CHARGE_DIR,SETTLE_MODE,SETTLE_AMOUNT,to_number(rpad(EFFECT_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.V_SP_RATIO^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CVSpRatio>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
					CVSpRatio::Type::key_list cKeyList;
					cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        		cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
					cBean.GetKeyList(cKeyList);
					cEditContainer.insert(cKeyList, cBean);
					return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="V_SP_USER_INFO" type="multi_index">
			<sql>
				SELECT CONCAT(SP_CODE, '!', OPER_CODE, '!', MSISDN) as KEY1,SP_CODE,OPER_CODE,REGION_CODE,MSISDN,to_number(rpad(VALID_DATE,14,0))  VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.V_SP_USER_INFO^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CVSpUserInfo>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
					cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
					cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
					CVSpUserInfo::Type::key_list cKeyList;
					cBean.GetKeyList(cKeyList);
					cEditContainer.insert(cKeyList, cBean);
					return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_IMSI_NUMBER" type="match">
			<sql>
				SELECT START_IMSI as KEY1, START_IMSI,END_IMSI,PRODUCT_TYPE,AREA_CODE,CLASSIFY,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_IMSI_NUMBER^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsImsiNumber>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsImsiNumber::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_ADD_OPERATOR_DESCRIPTION" type="multi_index">
			<sql>
				SELECT CONCAT(DR_TYPE, '|',OPER_ID) as KEY1, DR_TYPE,OPER_ID,DIS_TYPE,THRESHOLD,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_ADD_OPERATOR_DESC^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAddOperatorDescription>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsAddOperatorDescription::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="BPS_ADD_OPERATOR" type="multi_index">
			<sql>
				SELECT OPER_CODE as KEY1, OPER_ID, OPER_TYPE,OPER_CODE,OPER_NAME,OPER_ENG_NAME,HOME_AREA,SIGN_DATE,STS,STS_TIME,SPC_OPER_SEG,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_ADD_OPERATOR^] 
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsAddOperator>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CBpsAddOperator::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
			<container indexs="KEY1;KEY2" name="V_RS_XDR_DEFINE" type="match">
			<sql>
				SELECT XDR_DR_TYPE AS KEY1,CONCAT(XDR_DR_TYPE,'|',XDR_FIELD_INDEX) KEY2, XDR_FIELD_NAME, XDR_DR_TYPE,XDR_FIELD_TYPE,XDR_DEFAULT_VALUE,XDR_FIELD_FORMAT,XDR_FIELD_INDEX,DEL_FLAG,to_number('20010101000000') AS VALID_DATE , to_number('20991230235959') AS EXPIRE_DATE
				FROM ^[settle.V_RS_XDR_DEFINE^] 
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CVRsXdrDefine>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
	        cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
	        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
	        CVRsXdrDefine::Type::key_list cKeyList;
	        cBean.GetKeyList(cKeyList);
	        cEditContainer.insert(cKeyList, cBean);
	        return true;
				]]>
			</loaddbcode>
		</container>
		<container indexs="KEY1" name="V_BPS_IPV4_ADDRESS" type="match">
            <sql>
                SELECT ip as KEY1,is_pro_comp,pro_company_name,unit_name_buss_info,unit_class,belong_province,belong_city,buss_type,statis_ym,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE,prov_code,area_code
                FROM ^[settle.V_BPS_IPV4_ADDRESS^] 
            </sql>
            <cmpfunc>
                <![CDATA[
                    xload::base_cmp_func<CVBpsIpv4Address>
                ]]>
            </cmpfunc>
            <loaddbcode>
                <![CDATA[
                    cSqliteInsert<<cBean;
            cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
            cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
            CVBpsIpv4Address::Type::key_list cKeyList;
            cBean.GetKeyList(cKeyList);
            cEditContainer.insert(cKeyList, cBean);
            return true;
                ]]>
            </loaddbcode>
        </container>
        <container indexs="KEY1" name="BPS_IPV4_ADDRESS_RANGE" type="match">
            <sql>
                SELECT jssd.GetLongestSubString(start_ip,end_ip) as KEY1,customer_name,cust_id,ip_address_range_name,start_ip,end_ip,land_city,submission_city,nvl(to_number(rpad(VALID_DATE,14,0)),20240101000000) VALID_DATE,nvl(to_number(rpad(EXPIRE_DATE,14,0)),20991231235959) EXPIRE_DATE
                FROM ^[settle.BPS_IPV4_ADDRESS_RANGE^] 
            </sql>
            <cmpfunc>
                <![CDATA[
                    xload::base_cmp_func<CBpsIpv4AddressRange>
                ]]>
            </cmpfunc>
            <loaddbcode>
                <![CDATA[
                    cSqliteInsert<<cBean;
            cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
            cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
            CBpsIpv4AddressRange::Type::key_list cKeyList;
            cBean.GetKeyList(cKeyList);
            cEditContainer.insert(cKeyList, cBean);
            return true;
                ]]>
            </loaddbcode>
            </container>
            <container indexs="KEY1" name="BPS_IPV6_ADDRESS_RANGE" type="match">
            <sql>
                SELECT jssd.GetLongestSubString(start_ip,end_ip) as KEY1,customer_name,cust_id,ip_address_range_name,replace(start_ip,'::',':') start_ip,end_ip,land_city,submission_city,nvl(to_number(rpad(VALID_DATE,14,0)),20240101000000) VALID_DATE,nvl(to_number(rpad(EXPIRE_DATE,14,0)),20991231235959) EXPIRE_DATE
                FROM ^[settle.BPS_IPV6_ADDRESS_RANGE^] 
            </sql>
            <cmpfunc>
                <![CDATA[
                    xload::base_cmp_func<CBpsIpv6AddressRange>
                ]]>
            </cmpfunc>
            <loaddbcode>
                <![CDATA[
                    cSqliteInsert<<cBean;
            cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
            cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
            CBpsIpv6AddressRange::Type::key_list cKeyList;
            cBean.GetKeyList(cKeyList);
            cEditContainer.insert(cKeyList, cBean);
            return true;
                ]]>
            </loaddbcode>
            </container>
            <container indexs="KEY1" name="BPS_SERV_PRODUCT" type="multi_index">
            <sql>
                SELECT msisdn KEY1,msisdn,product_type,county_code,area_code,to_number('20010101000000') VALID_DATE,to_number('20991230235959')  EXPIRE_DATE
                FROM ^[settle.BPS_SERV_PRODUCT^] 
            </sql>
            <cmpfunc>
                <![CDATA[
                    xload::base_cmp_func<CBpsServProduct>
                ]]>
            </cmpfunc>
            <loaddbcode>
                <![CDATA[
                    cSqliteInsert<<cBean;
            cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
            cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
            CBpsServProduct::Type::key_list cKeyList;
            cBean.GetKeyList(cKeyList);
            cEditContainer.insert(cKeyList, cBean);
            return true;
                ]]>
            </loaddbcode>
            </container>
            <container indexs="KEY1" name="BPS_SJ_SMS_DEFAULT_PRICE" type="multi_index">
            <sql>
                SELECT SETTLEMENT_TYPE as KEY1,SETTLEMENT_TYPE,SETTLEMENT_PRICE,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
                FROM ^[settle.BPS_SJ_SMS_DEFAULT_PRICE^] 
            </sql>
            <cmpfunc>
                <![CDATA[
                    xload::base_cmp_func<CBpsSjSmsDefaultPrice>
                ]]>
            </cmpfunc>
            <loaddbcode>
                <![CDATA[
                    cSqliteInsert<<cBean;
                    cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
                    cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
                    CBpsSjSmsDefaultPrice::Type::key_list cKeyList;
                    cBean.GetKeyList(cKeyList);
                    cEditContainer.insert(cKeyList, cBean);
                    return true;
                ]]>
            </loaddbcode>
            </container>
			<container indexs="KEY1" name="BPS_SPECIAL_NET" type="match">
			<sql>
				SELECT CONCAT(AREA_CODE, '!', NVL(BUREAU_CODE,'NULL'), '!', jssd.colNameMaxSame(START_NUMBER,END_NUMBER)) as KEY1, START_NUMBER,END_NUMBER,NUMBER_TYPE,AREA_CODE,NVL(BUREAU_CODE,'NULL') BUREAU_CODE,OPERATOR_ID,to_number(rpad(VALID_DATE,14,0)) VALID_DATE,to_number(rpad(EXPIRE_DATE,14,0)) EXPIRE_DATE
				FROM ^[settle.BPS_SPECIAL_NET^]
			</sql>
			<cmpfunc>
				<![CDATA[
					xload::base_cmp_func<CBpsSpecialNet>
				]]>
			</cmpfunc>
			<loaddbcode>
				<![CDATA[
					cSqliteInsert<<cBean;
					cBean.set_validDate(xload::Int2Time(cBean.get_validDate()));
			        cBean.set_expireDate(xload::Int2Time(cBean.get_expireDate()));
					CBpsSpecialNet::Type::key_list cKeyList;
					cBean.GetKeyList(cKeyList);
					cEditContainer.insert(cKeyList, cBean);
					return true;
				]]>
			</loaddbcode>
		</container>
	</group>
</xc>
