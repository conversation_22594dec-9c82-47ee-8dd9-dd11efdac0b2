require "dbfetcher_util"

function main(args)
    -- 获取数据库连接
    local conn = getConnect()
    -- 获取任务 ID
    local inTaskId = getCode(args, "TASK_ID")

    writeLog("开始处理")

    -- 获取 BILL_MONTH 参数
    billMonth = getBillMonth(conn, inTaskId)
    
    -- 如果获取不到，返回错误
    if not billMonth then
        writeLog("未提供 BILL_MONTH 参数")
        return -1
    end

    -- 实际结算月份等于出账月份前两月，取YYYYMM
    settleMonth = string.sub(getAdjustDate(billMonth, -2), 1, 6)  

    writeLog("BILL_MONTH:" .. billMonth .. "  实际结算月份:" .. settleMonth)

    -- 获取当前月份（两位数）
    local currentMonth = os.date("%m")
    
    -- 获取当前时间完整格式
    local currentTime = os.date("%Y%m%d%H%M%S")

    -- 删除旧数据
    local sql = [[DELETE FROM jsdr.dr_ded_audit_inner_]] .. settleMonth .. [[ h 
                 WHERE h.audit_type = '05']]
    writeLog("删除audit_type=05的旧数据")
    executeSQL(conn, sql)
    conn:commit()

    -- 表插入拒绝付费核减数据
    local sql = [[
        INSERT INTO jsdr.dr_ded_audit_inner_]] .. settleMonth .. [[
            (file_name, day_id, month_id, record_type, busi_type, audit_type,
            seq_id, msisdn, sp_code, oper_code, charge_type, end_time, audit_time,
            audit_fee, out_flag, content_id, third_part_mark, cdr_seq,add_filter1, add_filter2,
            reserved, sett_fee,top_id, sub_type, orgnl_chrg_md, onln_chrg_seq, orgnl_trd_sttlmnth)
            SELECT  '',
                    ']] .. settleMonth .. [[01',
                    ']] .. currentMonth .. [[',
                    '20',
                    a.busi_flag,
                    '05',
                    a.last_date || lpad(rownum,6,'0'),
                    a.msisdn,
                    a.sp_code,
                    a.oper_code,
                    a.charge_type,
                    rpad(a.pro_date,14,'0'),
                    ']] .. currentTime .. [[',
                    SUM(a.ded_fee),
                    '0',
                    decode(a.busi_flag,'MUSIC','000000000000','MMK','0','CMIC','0',''),
                    decode(a.busi_flag,'CM',a.other_party,'MMS',a.other_party,'MRD',a.other_party,'MUSIC',a.other_party,'CRG',a.other_party,'M',a.other_party,'CMIC',a.other_party,''),
                    '',
                    '',
                    '',
                    '',
                    SUM(a.sett_fee),
                    '',
                    decode(a.busi_flag,'MMK','051',''),
                    decode(a.busi_flag,'MMK','01',''),
                    '',
                    ''
            FROM jsdr.dr_sett_mhtus_ddct a
            WHERE a.busi_flag in('CM','MMS','MMK','SJDH','M','CMIC','MUSIC')
              AND a.ded_fee>0
              AND a.month_id=']] .. string.sub(settleMonth, 5, 6) .. [['
            GROUP BY a.busi_flag,a.msisdn,a.sp_code,a.oper_code,a.charge_type,a.last_date || lpad(rownum,6,'0'),a.other_party,a.last_date,rpad(a.pro_date,14,'0')
    ]]

    writeLog("插入拒绝付费核减业务数据")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除SEQ_ID包含非数字字符的记录
    local sql = [[DELETE FROM jsdr.dr_ded_audit_inner_]] .. settleMonth .. [[ h 
                 WHERE h.month_id = ']] .. currentMonth .. [['
                   AND TRIM(TRANSLATE(h.seq_id, '0123456789', ' ')) IS NOT NULL]]
    writeLog("清理SEQ_ID包含非数字字符的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除不符合charge_type条件的记录
    local sql = [[DELETE FROM jsdr.dr_ded_audit_inner_]] .. settleMonth .. [[ h 
                 WHERE h.month_id = ']] .. currentMonth .. [['
                   AND h.charge_type NOT IN ('02','03','05','07','09','11','15','17','19')]]
    writeLog("删除不符合charge_type条件的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除结束时间超过90天的05类型记录
    local sql = [[DELETE FROM jsdr.dr_ded_audit_inner_]] .. settleMonth .. [[ h
                 WHERE h.month_id = ']] .. currentMonth .. [['
                   AND substr(h.end_time, 1, 8) <= TO_CHAR(SYSDATE - 90, 'YYYYMMDD')
                   AND h.audit_type = '05']]
    writeLog("删除结束时间超过90天的05类型记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除特定业务类型且THIRD_PART_MARK为空的记录
    local sql = [[DELETE FROM jsdr.dr_ded_audit_inner_]] .. settleMonth .. [[ h
                 WHERE h.month_id = ']] .. currentMonth .. [['
                   AND h.third_part_mark IS NULL
                   AND h.busi_type IN ('M','CM','MMS','MUSIC','CMIC')]]
    writeLog("删除特定业务类型且THIRD_PART_MARK为空的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 更新charge_type为03的记录，清空CDR_SEQ字段
    local sql = [[UPDATE jsdr.dr_ded_audit_inner_]] .. settleMonth .. [[ t
                 SET t.cdr_seq = ''
                 WHERE t.month_id = ']] .. currentMonth .. [['
                   AND t.charge_type = '03']]
    writeLog("更新charge_type为03的记录，清空CDR_SEQ字段")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除核减费用小于等于0的记录
    local sql = [[DELETE FROM jsdr.dr_ded_audit_inner_]] .. settleMonth .. [[ h
                 WHERE h.month_id = ']] .. currentMonth .. [['
                   AND h.audit_fee <= 0]]
    writeLog("删除核减费用小于等于0的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 更新费用为四舍五入后的整数值
    local sql = [[UPDATE jsdr.dr_ded_audit_inner_]] .. settleMonth .. [[ h
                 SET h.audit_fee = ROUND(h.audit_fee), 
                     h.sett_fee = ROUND(h.sett_fee)
                 WHERE h.month_id = ']] .. currentMonth .. [[']]
    writeLog("更新费用为四舍五入值")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除重复的05类型记录
    local sql = [[DELETE FROM jsdr.dr_ded_audit_inner_]] .. settleMonth .. [[ a
                 WHERE a.month_id = ']] .. currentMonth .. [['
                   AND a.audit_type = '05'
                   AND (a.busi_type, a.audit_type, a.msisdn, a.sp_code, a.oper_code, 
                        a.end_time, a.third_part_mark, a.cdr_seq) IN
                       (SELECT h.busi_type, h.audit_type, h.msisdn, h.sp_code, h.oper_code,
                               h.end_time, h.third_part_mark, h.cdr_seq
                          FROM jsdr.dr_ded_audit_inner_]] .. settleMonth .. [[ h
                         WHERE h.audit_type = '05'
                         GROUP BY h.busi_type, h.audit_type, h.msisdn, h.sp_code, h.oper_code,
                                  h.end_time, h.third_part_mark, h.cdr_seq
                        HAVING COUNT(1) > 1)]]
    writeLog("删除重复的05类型记录")
    executeSQL(conn, sql)
    conn:commit()
    ------------------------------------------------------
    writeLog("处理完成")
    conn:close()
    return 0
end

environment = os.getenv("ENVIRONMENT")
if environment == "TEST" then
    main("TASK_ID=17990|BILL_MONTH=202502")
    os.exit()
end
