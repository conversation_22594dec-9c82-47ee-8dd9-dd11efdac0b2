--*****************************************************************************
-- *	file:	mread.expr
-- *  brief:
-- *		define the rule of uploader gsm's "mread" file.
-- *		.... ........ ........
-- *
-- *	Copyright (c) 2003,2004,2005 Asiainfo Technologies(China),Inc.
-- *
-- *	RCS: $Id: mread.expr,v 1.1.2.55 2014/01/15 07:15:36 nijj Exp $
-- *
-- *	History:
-- *		2009-09-07  hexb  create
-- ****************************************************************************
require "libluabaseD"
require "libluadebugD"
	
	local logtrace = libluadebugD.report_trace
 	local logerror = libluadebugD.report_error
	
	<% use("settle_Xdr_app.MXdr::SXdr") %>
    --...reset.................local
    pSubCommon = nil;               --SXdr.SSubXdr.SSubCommon
    pGsmInfo = nil;                 --SXdr.SSubXdr.SGsmInfo

    pFileOpInfo = nil;
    pOriCharge = nil;

    local iDoPrintInfo	= 1;	            --1:.......log,	0:........log
	
	--iRes,t_sDealDateTime		= gethosttime();

		t_sEmpty		= " ";
	t_sDealDateTime	= "";
	iStopMinus = "";
	g_iStopMinus = "";
	t_sOutRecord		= "";
	t_sEmpty		= " ";
	t_sTrunkGroupOut = "";
	t_sProvCode		= "791"; --...
	t_iTotalFee = 0;
	--!.................
	t_iProcNo	= 0;
	t_iProcNo       = t_iFileNo
	
	t_iValidNumber	= 0;
	t_iErrorNumber	= 0;
	t_iRecordNumber	= 0;
	t_iDupNumber	= 0;
	t_iLateNumber	= 0;
	--modify by niewl 20160809, BIZBILLING_REQ_172081:deal t_sOutFileName  = "";
	--t_sOutFileName	= "";
	t_iSecondCdr	= 0;
	t_iErrLater		= 0;
	t_iErrLater     = 0;
	t_iErrLaterOnce = 0;


	t_lTotalInfoFee		= 0;
	t_lTotalDiscountFee     = 0;

	--Get Next Upload Time.
	t_sNextUploadTime = "";

	t_sDate		= "";
	t_sBeginTime    = "";
	t_sEndTime      = "";
  

--[[============================================================================
--....: init_getsdlval
--.    .: .....
--....:
--....:
--....
--==========================================================================--]]
function init_getsdlval()
	if iDoPrintInfo == 1
	then	
        logtrace('----------init_getsdlval begin-------------');
    end

    pSubXdr = <%get_struct_value('PXdr',"MXdr::SXdr.TOTAL_XDR") %>
        
	if pSubXdr == nil
	then
		if iDoPrintInfo == 1
		then
            logtrace('----------init_getsdlval failed.-------------');
        end
        return -1;
    end
        
	------  init pCommon  ------  
	pGsmInfo 		= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.GSM_INFO45") %>
	pSubCommon		= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SUB_COMMON") %>
    pReserverFifldRefs = <%get_sdl_ref('pSubCommon',"MXdr::SSubCommon.RESERVE_FIELDS")%>;
	pFileOpInfo  	= <%get_struct_value('pSubCommon', "MXdr::SSubCommon.FILE_OP_INFO")%>;
	if pGsmInfo == nil or pSubCommon == nil or pFileOpInfo == nil
	then
		if iDoPrintInfo == 1
		then
			logtrace('----------init_getsdlval failed.-------------');
		end
		return -1;
	end;

	if iDoPrintInfo == 1
	then
		logtrace('----------init_getsdlval end-------------');
	end
	return 0;
end


local function head_record()
	iRes, t_sDealDateTime = GetHostTimeR();
	if(t_iDate < tonumber(string.sub(t_sDealDateTime,0,8)))
	then
		t_sDealDateTime	= t_iDate.."235501";
	else
		if(tonumber(t_iDate) > tonumber(string.sub(t_sDealDateTime,0,8)))
		then
			t_sDealDateTime = t_iDate.."000000"
		end
	end
	t_iValidNumber  = 0;
	t_sBeginTime    = "20990101000000";
	t_sEndTime      = "20000101000000";
	t_iTotalFee     = 0;
	t_sOutRecord = "1046000"
			.. lpad(t_sProvCode, " ", 3)
			.. rpad(t_sEmpty, " ", 6)
			.. "46000000"
			.. rpad(t_sEmpty, " ", 6)
			.. lpad(tostring(t_iProcNo), "0", 3)
			.. rpad(t_sEmpty, " ", 20)
			.. lpad(t_sDealDateTime, " ", 14)
			.. "01"
			.. rpad(t_sEmpty, " ", 121)
			;
                t_iResult      = 0;
	return;
end
	
local function file_name()
	------------start reset---------
	t_sDealDateTime="";
	
	--!.................
	t_iProcNo		= 0;
	t_iValidNumber	= 0;
	t_iErrorNumber	= 0;
	t_iRecordNumber	= 0;
	t_iDupNumber	= 0;

	t_iSecondCdr	= 0;
	t_iErrLater		= 0;
	t_iErrLater		= 0;
	t_iErrLaterOnce	= 0;
	--t_sBeginTime	= "20300101000000";
	--t_sEndTime		= "20000101000000";
	t_iLaterOnceNumber = 0;
	t_sNextUploadTime = "";
	t_lTotalInfoFee = 0;
	t_lTotalDiscountFee = 0;
	t_sDate		= "";
	sFileType	= "";
	
	t_iResult	= 0;
	t_sOutRecord = "";
	t_sOutFileName	= "";
	------------end reset---------

	sFileType	= t_sFileType;
	t_iProcNo	= t_iFileNo;
	t_sDate		= string.sub(tostring(t_iDate),5,8);
	--modify by niewl 20160809, BIZBILLING_REQ_172081:add t_sOutFileName  = "";	
	t_sOutFileName	= sFileType
			.. t_sDate
			.. lpad(tostring(t_iProcNo), "0", 3)
			.. "."
			.. t_sProvCode;

	t_iResult	= 0;

	return;
end

local function tail_record()
	--iRes, t_sEndTime = GetHostTimeR();

    t_iTotalFee             = (t_iTotalFee + 5)/10;
                t_sOutRecord   = "9046000000" 
                        .. rpad(t_sEmpty, " ", 6)
                        .. "46000"
                        .. lpad(t_sProvCode, " ", 3)
                        .. rpad(t_sEmpty, " ", 6)
                        .. lpad(tostring(t_iProcNo), "0", 3)
                        .. lpad(tostring(t_iValidNumber), "0", 9)
                        .. lpad(t_sBeginTime, " ", 14)
                        .. lpad(t_sEndTime, " ", 14)
                        .. lpad(tostring(t_iTotalFee), "0", 12)
                        .. rpad(t_sEmpty, " ", 108);
    t_iResult      = 0;


	return;
end

local function null_body_record()
	t_sOutRecord	= "";
	t_iResult	= 0;

	return;
end
	
	
local function body_record()

	pGsmInfo 		= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.GSM_INFO45") %>
	pSubCommon		= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SUB_COMMON") %>
	--....XDR......
	t_iCdrType=<%get_struct_value('PXdr', "MXdr::SXdr.ORI_FILE_TYPE")%> 
	if(t_iCdrType == nil)
	then
		t_iCdrType = 0;
	end
	t_iCallType     = <%get_struct_value('pSubCommon', "MXdr::SSubCommon.CALL_TYPE") %>
	if(t_iCallType == nil)
	then
		t_iCallType = 0;
	end
	t_sRawTag       = <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.RAW_TAG") %>
	if(t_sRawTag == nil)
	then
		t_sRawTag = 0;
	end
	t_iSeq                  =    <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.SEQ") %>
	if(t_iSeq == nil)
	then
		t_iSeq = "";
	end
	t_sOdn                  =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN") %>
	if(t_sOdn == nil)
	then
		t_sOdn = "";
	end
	t_sTdn                  =   <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN") %>
	if(t_sTdn == nil)
	then
		t_sTdn = "";
	end
	t_sAdn                  =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ADN") %>
	if(t_sAdn == nil)
	then
		t_sAdn = "";
	end
	t_sOdnFixed     =   <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_FIXED") %>
	if(t_sOdnFixed == nil)
	then
		t_sOdnFixed = "";
	end
	t_sTdnFixed     =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_FIXED") %>
	if(t_sTdnFixed == nil)
	then
		t_sTdnFixed = "";
	end
	t_sAdnFixed     = <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ADN_FIXED") %>
	if(t_sAdnFixed == nil)
	then
		t_sAdnFixed = "";
	end
	t_sStartTime    =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.START_TIME") %>
	if(t_sStartTime == nil)
	then
		t_sStartTime = "";
	end
	t_iDuration   =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.DURATION") %>
	if(t_iDuration == nil)
	then
		t_iDuration = "";
	end
	t_iTimeSeg      =  <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TIME_SEG") %>
	if(t_iTimeSeg == nil)
	then
		t_iTimeSeg = "";
	end
	t_sMsrn                 =  <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSRN") %>
	if(t_sMsrn == nil)
	then
		t_sMsrn = "";
	end
	t_sMsc                  =   <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSC") %>
	if(t_sMsc == nil)
	then
		t_sMsc = 0;
	end
	t_sLac                  =   <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.LAC") %>
	if(t_sLac == nil)
	then
		t_sLac = "";
	end
	t_sCell                 =   <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.CELL") %>
	if(t_sRawTag == nil)
	then
		t_sRawTag = 0;
	end
	t_sTrunkIn      =   <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN") %>
	if(t_sTrunkIn == nil)
	then
		t_sTrunkIn = "";
	end
	t_sTrunkOut     =   <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT") %>;
	if(t_sTrunkOut == nil)
	then
		t_sTrunkOut = 0;
	end
	t_iMscVendor    =   <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSC_VENDOR") %>
	if(t_iMscVendor == nil)
	then
		t_iMscVendor = 0;
	end
	t_sOriginalFile =   <%get_struct_value('PXdr', "MXdr::SXdr.ORIGINAL_FILE")%>
	if(t_sOriginalFile == nil)
	then
		t_sOriginalFile = 0;
	end
	t_sRegionCode   =    <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_REGION_CODE") %>
	if(t_sRegionCode == nil)
	then
		t_sRegionCode = 0;
	end
	t_iTrankInOper  =   <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_OPER") %>
	if(t_iTrankInOper == nil)
	then
		t_iTrankInOper = 0;
	end
	t_sTrunkInArea  =  <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_AREA") %>
	if(t_sTrunkInArea == nil)
	then
		t_sTrunkInArea = 0;
	end
	t_iTrunkInServ  =  <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_SERV") %>
	if(t_iTrunkInServ == nil)
	then
		t_iTrunkInServ = 0;
	end
	t_iTrunkOutOper =   <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_OPER") %>
	if(t_iTrunkOutOper == nil)
	then
		t_iTrunkOutOper = 0;
	end
	t_sTrunkOutArea =   <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_AREA") %>
	if(t_sTrunkOutArea == nil)
	then
		t_sTrunkOutArea = 0;
	end
	t_iTrunkOutServ =  <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_SERV") %>
	if(t_iTrunkOutServ == nil)
	then
		t_iTrunkOutServ = 0;
	end
	t_iOdnAccType   =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_TYPE") %>
	if(t_iOdnAccType == nil)
	then
		t_iOdnAccType = 0;
	end
	t_sOdnAccNo  =    <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_NO") %>
	if(t_sOdnAccNo == nil)
	then
		t_sOdnAccNo = 0;
	end
	t_iOdnAccOper  =<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_OPER") %>
	if(t_iOdnAccOper == nil)
	then
		t_iOdnAccOper = 0;
	end
	t_sOdnHomeArea  =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_HOME_AREA") %>
	if(t_sOdnHomeArea == nil)
	then
		t_sOdnHomeArea = 0;
	end
	t_sOdnVisitArea =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_VISIT_AREA") %>
	if(t_sOdnVisitArea == nil)
	then
		t_sOdnVisitArea = 0;
	end
	t_iOdnOper      =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_OPER") %>
	if(t_iOdnOper == nil)
	then
		t_iOdnOper = 0;
	end
	t_iOdnServ              =   <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_SERV") %>
	if(t_iOdnServ == nil)
	then
		t_iOdnServ = 0;
	end
	t_iOdnNet               =   <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_NET") %>
	if(t_iOdnNet == nil)
	then
		t_iOdnNet = 0;
	end
	t_iOdnRoam              =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ROAM") %>
	if(t_iOdnRoam == nil)
	then
		t_iOdnRoam = 0;
	end
	t_iOdnLong              =   <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_LONG") %>
	if(t_iOdnLong == nil)
	then
		t_iOdnLong = 0;
	end
	t_iOdnTradeMark =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_TRADEMARK") %>
	if(t_iOdnTradeMark == nil)
	then
		t_iOdnTradeMark = 0;
	end
	t_iTdnAccType   = <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_TYPE") %>
	if(t_iTdnAccType == nil)
	then
		t_iTdnAccType = 0;
	end
	t_sTdnAccNo             =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_NO") %>
	if(t_sTdnAccNo == nil)
	then
		t_sTdnAccNo = 0;
	end
	t_sTdnAccOper   =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_OPER") %>
	if(t_sTdnAccOper == nil)
	then
		t_sTdnAccOper = "";
	end
	t_sTdnHomeArea  =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_HOME_AREA") %>
	if(t_sTdnHomeArea == nil)
	then
		t_sTdnHomeArea = 0;
	end
	t_sTdnVisitArea =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_VISIT_AREA") %>
	if(t_sTdnVisitArea == nil)
	then
		t_sTdnVisitArea = "";
	end
	t_iTdnOper              = <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_OPER") %>
	if(t_iTdnOper == nil)
	then
		t_iTdnOper = "";
	end
	t_iTdnServ              =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_SERV") %>
	if(t_iTdnServ == nil)
	then
		t_iTdnServ = 0;
	end
	t_iTdnNet               =   <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_NET") %>
	if(t_iTdnNet == nil)
	then
		t_iTdnNet = 0;
	end
	t_iTdnRoam              = <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ROAM") %>
	if(t_iTdnRoam == nil)
	then
		t_iTdnRoam = 0;
	end
	t_iTdnLong              =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_LONG") %>
	if(t_iTdnLong == nil)
	then
		t_iTdnLong = 0;
	end
	t_iTdnTradeMark =  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_TRADEMARK") %>
	if(t_iTdnTradeMark == nil)
	then
		t_iTdnTradeMark = "";
	end
	t_iDrType         =  <%get_struct_value('PXdr', "MXdr::SXdr.DR_TYPE")%>
	if(t_iDrType == nil)
	then
		t_iDrType = 0;
	end
	--t_iErrcode      = trimx(IA5STRING(getseppos(t_ori, sepp("82:;")))," ");

	  t_tdn_prepfix = "";

        if ( string.sub(t_sTdn,1,5) == "12583") 
        then
                if (  string.sub(t_sTdn,1,6) == "125834" or string.sub(t_sTdn,1,6) == "125835"or string.sub(t_sTdn,1,6) == "125839")
                then
				
                else
                        t_tdn_prepfix = string.sub(t_sTdn, 0, 6);
                        t_sTdn = string.sub(t_sTdn, 6, string.len(t_sTdn));
                end
        end
        --printStr("t_iErrcode="+t_iErrcode);
        --printInt(t_len);
        t_sImsi                 = " ";
        t_sGMsc         = t_sMsc;
        t_sGMscAreaCode         = "";
        t_iServiceType  = 0;
        t_sServiceCode  = " ";
        t_iTranType             = 3;
        t_sTrankGroupOut                = " ";
        t_iDialType             = 0;
        t_iFeeType = 0;
        t_iFee = 0;
        t_iUserType = 0;
        t_sImei = " ";
        t_sUpFileName = " ";
        t_sUpRecNum = " ";

        iTempBeforeMins = 0;
        iTempBeforeMins = 0 + g_iBeforeHours;
        iTempLaterMins = 0;
        iTempLaterMins = 0 + g_iBeforeHoursForward;

        iTempStartTime = libluabaseD.gettime(t_sStartTime);
        if(iTempStartTime==-1)
        then
                t_sErrNo = "F8050";
                return error_deal(pSubCommon,t_sErrNo);
        end
        --.............
        iStopMinus = '0'..g_iStopMinus ;
        sLateFirTime = adddt(t_sDealDateTime,-(t_iDuration+60*iStopMinus));--....
        sLateSecTime = adddt(t_sDealDateTime,-(t_iDuration+2*60*iStopMinus));--....
        --sTempDateTime = adddt(t_sDealDateTime,-(t_iDuration+60*iTempLaterMins));
        if (20 == t_iDrType)
		then
                sTempDateTime = adddt(t_sDealDateTime,-(t_iDuration+60*60*72));
        else 
                sTempDateTime = adddt(t_sDealDateTime,-(t_iDuration+60*iTempLaterMins));
		end
		stTemp = libluabaseD.gettime(sTempDateTime)
        if(iTempStartTime < stTemp)
        then
                t_sErrNo = "F8051";
				logtrace("=====================t_sErrMsg======"..iTempStartTime.."==============iTempStartTime======="..stTemp);
                return error_deal(pSubCommon,t_sErrNo);
        end
		
        sTempDateTime   = adddt(t_sDealDateTime, 60*iTempBeforeMins);
        if ( iTempStartTime > libluabaseD.gettime(sTempDateTime) )
        then
                t_sErrNo        = "F052" ;
                return error_deal(pSubCommon,t_sErrNo);
        end

        --[[
        if(t_iCallType~=2)
        then
                t_sErrNo = "F010";
                return error_deal(pSubCommon,t_sErrNo);
        end
        --]]
	
        if(string.len(t_sOdnFixed)~=11 or is_number(t_sOdnFixed) ~= 1)
        then
                t_sErrNo = "F8030";
                return error_deal(pSubCommon,t_sErrNo);
        end

        --l_TempHlrInfo = LookupHlrInfo(t_sOdnFixed,string.sub(t_sStartTime,0,8));
        --l_TempHlrInfo = LookupHlrInfo(t_sOdnFixed,t_sStartTime);
		iRes,iOperatorId,iUserType,iNetType,strHlrCode,strAreaCode = LookupHlrInfo(t_sOdnFixed,t_sStartTime);
    
	l_TempOperId = iOperatorId;
	l_TempOdnProvCode= GetProvByAreaCode(strAreaCode,t_sStartTime);
    if(l_TempOperId==0)
    then
        t_sErrNo = "F8031";
        return error_deal(pSubCommon,t_sErrNo);
    else
        if(LookupSpecNum(t_sOdnFixed,string.sub(t_sStartTime,0,8))==false)
        then
            t_sErrNo = "F8034";
            return error_deal(pSubCommon,t_sErrNo);
        end
        t_iUserType = iUserType;
        if(t_iCdrType == 691 or string.sub(t_sOriginalFile,1,4)=="IBCF")
        then
            t_iUserType = 2;
			t_iCdrType = 691;
        end
		--增加青海西藏及其非结算省错单逻辑
		if l_TempOperId ~=2 and tonumber(l_TempOdnProvCode) ~=220 and tonumber(l_TempOdnProvCode) ~=898 and tonumber(l_TempOdnProvCode) ~=270 and tonumber(l_TempOdnProvCode) ~=871
		then 
			 t_sErrNo = "F8035";
            return error_deal(pSubCommon,t_sErrNo);
		end
		
        if(t_iUserType ~= 0 and t_iUserType ~= 1 and t_iUserType ~= 2)
        then
            t_sErrNo = "F8190";
            return error_deal(pSubCommon,t_sErrNo);
        end
		
    end

	if(string.len(t_sTdn)==0 or is_number(t_sTdn) ~= 1)
	then
			t_sErrNo = "F8040";
			return error_deal(pSubCommon,t_sErrNo);
	end
    if(t_sTdnLong == 2)
    then
        t_sErrNo = "F8044";
        return error_deal(pSubCommon,t_sErrNo);
    end
	if(string.len(t_sTdn)>=3 and
			string.sub(t_sTdn,1,3) == "068" or
			--string.sub(t_sTdn,"190") or -- change by zhaozh 20211011
			--string.sub(t_sTdn,"193") or
			--string.sub(t_sTdn,"196") or
			string.sub(t_sTdn,1,3) == "197" or
			string.sub(t_sTdn,1,3) == "400" or
			string.sub(t_sTdn,1,5) == "10200" or
			string.sub(t_sTdn,1,5) =="16300" or
			string.sub(t_sTdn,1,5) == "16388" or
			string.sub(t_sTdn,1,5) == "16900" or
			string.sub(t_sTdn,1,5) == "16901" or
			string.sub(t_sTdn,1,5) == "16961" or
			string.sub(t_sTdn,1,5) == "16995" or
			string.sub(t_sTdn,1,5) == "17200" or
			string.sub(t_sTdn,1,5) == "17201" or
			string.sub(t_sTdn,1,5) == "17266" or
			string.sub(t_sTdn,1,5) == "17300" )
	then
			t_sErrNo = "F8045";
			return error_deal(pSubCommon,t_sErrNo);
	end

    if(string.len(t_sTdnAccNo)>0)
    then
        if(string.sub(t_sTdnAccNo,1,3) == "179")
        then
            if(t_sTdnAccNo ~= "17950" and t_sTdnAccNo ~= "17951")
            then
                t_sErrNo = "F8041";
                return error_deal(pSubCommon,t_sErrNo);
            end
        elseif(string.sub(t_sTdnAccNo,1,3) == "125")
        then
            if(t_sTdnAccNo ~= "12593")
            then
                t_sErrNo = "F8043";
                return error_deal(pSubCommon,t_sErrNo);
            end
        else
            t_sErrNo = "F8043";
            return error_deal(pSubCommon,t_sErrNo);
        end
    end
    if(string.sub(t_sTdnFixed,1,4) == "1349")
    then
        t_sErrNo = "F8043";
        return error_deal(pSubCommon,t_sErrNo);
    end
    if(string.sub(t_sTdnFixed,1,4) == "1749")   --add by maoyi 2017.5
    then
        t_sErrNo = "F8043";
        return error_deal(pSubCommon,t_sErrNo);
    end 
	--增加F898错单代码打错单
	if((t_iTdnNet ~= 1 and  t_iTdnNet ~=0) and string.sub(t_sTdnFixed,0,1)=="1" and string.len(t_sTdnFixed)==11)
	then
		t_sErrNo = "F8898";
        return error_deal(pSubCommon,t_sErrNo);
	end
	logtrace("=====================test code run is here9999999======"..t_sTdnFixed.."======"..t_iTdnNet.."========t_sTdnFixed======="..t_sTdnFixed);
	if(t_iTdnNet ~= 1 and string.sub(t_sTdnFixed,0,1)=="1" and string.len(t_sTdnFixed)==11)
	then
		if(string.sub(t_sTdnFixed,1,5) ~= "13000" and string.sub(t_sTdnFixed,1,5) ~= "13010")
		then
				iRes,iOperatorId,iUserType,iNetType,strHlrCode,strAreaCode = LookupHlrInfo(t_sTdnFixed,t_sStartTime);
				l_TempOperId = iOperatorId;
				if(l_TempOperId == 0)
				then
						t_sErrNo = "F8042";
						return error_deal(pSubCommon,t_sErrNo);
				end
		end
		if(string.len(t_sTdnAccNo) == 0)
		then
				if(t_iOdnLong == 2)
				then
						t_sTdn = "0086"..t_sTdnFixed;
				else
						t_sTdn = t_sTdnFixed;
				end
		else
				t_sTdn = t_sTdnAccNo + t_sTdnFixed;
		end
		t_iFeeType = 2;
	else
		if string.len(t_sTdnFixed) <= 8 then
			if(t_iOdnLong == 2)
			then
					t_sTdn = t_sTdnAccNo.."0086"..t_sTdnHomeArea..""..t_sTdnFixed;
			else
					t_sTdn = t_sTdnAccNo.."0"..t_sTdnHomeArea..""..t_sTdnFixed;
			end
		else
			t_sErrNo = "F8897";
			return error_deal(pSubCommon,t_sErrNo);
		end
		t_iFeeType = 1;
	end
	if(t_iDuration<=0)
	then
		t_sErrNo = "F8060";
		return error_deal(pSubCommon,t_sErrNo);
	end

	if(t_iDuration>10800)
	then
		t_sErrNo = "F8061";
		return error_deal(pSubCommon,t_sErrNo);
	end
    if(t_sGMsc ~= "")
    then
        if(llike(t_sGMsc,"86") ~= 0 or  string.len(t_sGMsc)~=10)
        then
            t_sErrNo = "F8070";
            return error_deal(pSubCommon,t_sErrNo);
        end
    end
	
	if(t_iErrcode~="F001" and t_iErrcode~="F002")
    then
        -- if (20 == t_iDrType)
		-- then
        --      sTempKey = tostring(t_iCallType)..t_sOdnFixed..t_sTdn..t_sStartTime.."|"..tostring(t_iDrType);
        -- else
        --       sTempKey = tostring(t_iCallType)..t_sOdnFixed..t_sTdn..t_sStartTime;         
        --    <%set_struct_value('PXdr', "MXdr::SXdr.DR_TYPE",t_iDrType)%>;
		--end

        t_iCheckFlag = checkdup(0,0);
        if(t_iCheckFlag == -1)
        then
                t_sErrNo = "F8200";
                return error_deal(pSubCommon,t_sErrNo);
        end
        if(t_iCheckFlag < 0)
        then
                t_sErrNo = "F8888";
                return error_deal(pSubCommon,t_sErrNo);
        end
    end
	
	
	if(string.sub(t_sRegionCode,1,1) =="0")
	then
		t_sGMscAreaCode = string.sub(t_sRegionCode,2,string.len(t_sRegionCode));
	else
		t_sGMscAreaCode = t_sRegionCode;
	end

	if(string.sub(t_sTdn,1,5) == "17950")
	then
			t_iDialType = 2;
	elseif(string.sub(t_sTdn,1,5) == "17951")
	then
			t_iDialType = 3;
	elseif(string.sub(t_sTdn,1,5) == "12593")
	then
			t_iDialType = 4;
	else
			t_iDialType = 1;
	end
	iTempSixSec = t_iDuration/6;
	if(t_iDuration%6 > 0)
	then
			iTempSixSec = iTempSixSec + 1;
	end
	t_iFee = iTempSixSec * 6;
	
	--[[...., ..........................
	if (20 == t_iDrType)
	{
	  l_iIsErrRec = 0;
			l_sErrCode  = trimx(IA5STRING(getseppos(t_ori, sepp("1:|")))," ");

			if (l_sErrCode >= "F080"  and  l_sErrCode <= "F082") 
			{
					l_sField_Value = trimx(IA5STRING(getseppos(t_ori, sepp("9:|")))," ");
					if (t_sGMscAreaCode == l_sField_Value ) 
							l_iIsErrRec = 1;
			}
			else if ( l_sErrCode == "F140" )  
			{
					l_iField_Value = atol(IA5STRING(getseppos(t_ori, sepp("13:|"))));
					if ( t_iTranType == l_iField_Value )
							l_iIsErrRec = 1;
			}
			else if (l_sErrCode == "F160" || l_sErrCode=="F161")  
			{
					l_iField_Value = atol(IA5STRING(getseppos(t_ori, sepp("14:|"))));
					if ( t_iDialType == l_iField_Value )
							l_iIsErrRec = 1;
			}
			else if (l_sErrCode == "F170")  
			{
					l_iField_Value = atol(IA5STRING(getseppos(t_ori, sepp("15:|"))));
					if ( t_iFeeType == l_iField_Value )
							l_iIsErrRec = 1;
			}
			else if (l_sErrCode == "F180" || l_sErrCode=="F181")  
			{
					l_iField_Value = atol(IA5STRING(getseppos(t_ori, sepp("16:|"))));
					if ( t_iFee == l_iField_Value )
							l_iIsErrRec = 1;
			}
			else if (l_sErrCode == "F190" || l_sErrCode=="F191")  
			{
					l_iField_Value = atol(IA5STRING(getseppos(t_ori, sepp("17:|"))));
					if ( t_iUserType == l_iField_Value )
							l_iIsErrRec = 1;
			}
			else
					l_iIsErrRec = 1;

			if (1 == l_iIsErrRec)
			{
					t_sErrNo = l_sErrCode;
					goto err_deal;
			}
	}--]]
	
	t_sTdn = t_tdn_prepfix..t_sTdn;
    
    if tonumber(t_sBeginTime)>tonumber(t_sStartTime)
    then 
        t_sBeginTime = t_sStartTime;
    end;
    
    if tonumber(t_sStartTime) > tonumber(t_sEndTime)
    then 
        t_sEndTime =t_sStartTime
    end
    
--[[
	if ( libluabaseD.gettime(t_sBeginTime) > iTempStartTime )
	then
			t_sBeginTime    = t_sStartTime;
	end
	if ( iTempStartTime > libluabaseD.gettime(t_sEndTime) )
	then
			t_sEndTime      = t_sStartTime;
	end
--]]		
	if(t_iCallType == nil)
	then
		t_iCallType = 0;
	end
	if(t_sImsi == nil)
	then
		t_sImsi = "";
	end
	if(t_sOdnFixed == nil)
	then
		t_sOdnFixed = "";
	end
	if(t_sTdn == nil)
	then
		t_sTdn = "";
	end
	if(t_sStartTime == nil)
	then
		t_sStartTime = "";
	end
	if(t_iDuration == nil)
	then
		t_iDuration = "";
	end
	if(t_sGMsc == nil)
	then
		t_sGMsc = "";
	end
	if(t_sGMscAreaCode == nil)
	then
		t_sGMscAreaCode = "";
	end
	if(t_sMsc == nil)
	then
		t_sMsc = "";
	end
	if(t_sServiceType == nil)
	then
		t_sServiceType = "";
	end
	if(t_sServiceCode == nil)
	then
		t_sServiceCode = "";
	end
	if(t_iTranType == nil)
	then
		t_iTranType = "";
	end
	if(t_iDialType == nil)
	then
		t_iDialType = "";
	end
	if(t_iFeeType == nil)
	then
		t_iFeeType = "";
	end
	if(t_iFee == nil)
	then
		t_iFee = "";
	end
	if(t_iUserType == nil)
	then
		t_iUserType = "";
	end
	if(t_sImei == nil)
	then
		t_sImei = "";
	end
	if(t_sUpFileName == nil)
	then
		t_sUpFileName = "";
	end
	if(t_sEmpty == nil)
	then
		t_sEmpty = "";
	end
	if(t_sAdn == nil)
	then
		t_sAdn = "";
	end
	if(t_sTrunkGroupOut == nil)
	then
		t_sTrunkGroupOut = "";
	end
	
	if(2 == t_iLocation) --body
	then	
		--!.........
		t_sOutRecord = lpad(tostring(t_iCallType), "0", 2)
				.. lpad(t_sImsi, " ", 15)
				.. rpad(t_sOdnFixed, " ", 15)
				.. rpad(t_sTdn, " ", 24)
				.. rpad(t_sStartTime, " ", 14)
				.. lpad(tostring(t_iDuration), "0", 6)
				.. rpad(t_sGMsc," ",10)
				.. rpad(t_sGMscAreaCode," ",4)
				.. rpad(t_sMsc, " ", 10)
				.. lpad(t_sServiceType, "0", 3)
				.. lpad(t_sServiceCode, " ", 4)
				.. tostring(t_iTranType)
				.. tostring(t_iDialType)
				.. tostring(t_iFeeType)
				.. lpad(tostring(t_iFee),"0",6)
				.. tostring(t_iUserType)
				.. rpad(t_sImei, " ", 20)
				.. rpad(t_sUpFileName, " ", 15)
				.. rpad(t_sEmpty," ",6)
				.. rpad(t_sAdn, " ", 15)
				.. rpad(t_sTrunkGroupOut," ",7)
				.. rpad(t_sEmpty, " ", 10)
				;
		if(string.len(t_sOutRecord)~=190)
		then
				t_sErrNo = "F1100";
				return error_deal(pSubCommon,t_sErrNo);
		end
		
		t_iValidNumber = t_iValidNumber + 1;
		
		t_iTotalFee = t_iTotalFee + t_iFee;
		if(libluabaseD.gettime(sLateFirTime) > iTempStartTime and libluabaseD.gettime(sLateSecTime) < iTempStartTime)
		then--.......
			t_iResult = 3;
		elseif(libluabaseD.gettime(sLateSecTime) > iTempStartTime)
		then--.......
			t_iResult = 0;
		else
		
			t_iResult      = 0;
		end

		return;
	end
end


function upload_main()
	if iDoPrintInfo == 1 then
		logtrace('------------------------------upload_mread_main begin--------------------------------');
	end

	--0:head 1:tail 2:body 3:null body 4:filename 
	if(t_iLocation == 0) then
		head_record();
	elseif(t_iLocation == 1) then
		tail_record();
	elseif(t_iLocation == 2) then
		init_getsdlval();
		body_record();
	elseif(t_iLocation == 3) then
		null_body_record();
	elseif(t_iLocation == 4) then
		file_name();
	end

	if iDoPrintInfo == 1 then
		logtrace('------------------------------upload_mread_main end--------------------------------');
	end
end


function error_deal(pSubCommon,sErrorCode)
	<%set_struct_value('pFileOpInfo', "MFileOp::SFileOp.TREAT_FLAG",1)%>
	<%set_struct_value('pFileOpInfo',"MFileOp::SFileOp.ERROR_CODE",sErrorCode) %>
	logtrace("error_deal,errocode:"..sErrorCode)
	
	
	if(t_iCallType == nil)
	then
		t_iCallType = 0;
	end
	if(t_sImsi == nil)
	then
		t_sImsi = "";
	end
	if(t_sOdnFixed == nil)
	then
		t_sOdnFixed = "";
	end
	if(t_sTdn == nil)
	then
		t_sTdn = "";
	end
	if(t_sStartTime == nil)
	then
		t_sStartTime = "";
	end
	if(t_iDuration == nil)
	then
		t_iDuration = "";
	end
	if(t_sGMsc == nil)
	then
		t_sGMsc = "";
	end
	if(t_sGMscAreaCode == nil)
	then
		t_sGMscAreaCode = "";
	end
	if(t_sMsc == nil)
	then
		t_sMsc = "";
	end
	if(t_sServiceType == nil)
	then
		t_sServiceType = "";
	end
	if(t_sServiceCode == nil)
	then
		t_sServiceCode = "";
	end
	if(t_iTranType == nil)
	then
		t_iTranType = "";
	end
	if(t_iDialType == nil)
	then
		t_iDialType = "";
	end
	if(t_iFeeType == nil)
	then
		t_iFeeType = "";
	end
	if(t_iFee == nil)
	then
		t_iFee = "";
	end
	if(t_iUserType == nil)
	then
		t_iUserType = "";
	end
	if(t_sImei == nil)
	then
		t_sImei = "";
	end
	if(t_sUpFileName == nil)
	then
		t_sUpFileName = "";
	end
	if(t_sEmpty == nil)
	then
		t_sEmpty = "";
	end
	if(t_sAdn == nil)
	then
		t_sAdn = "";
	end
	if(t_sTrunkGroupOut == nil)
	then
		t_sTrunkGroupOut = "";
	end
	
	--!......
	t_iErrorNumber	= t_iErrorNumber + 1;
	  t_sOutRecord =  t_sErrNo .. "000000"
				.. lpad(tostring(t_iCallType), "0", 2)
				.. lpad(t_sImsi, " ", 15)
				.. rpad(t_sOdnFixed, " ", 15)
				.. rpad(t_sTdn, " ", 24)
				.. rpad(t_sStartTime, " ", 14)
				.. lpad(tostring(t_iDuration), "0", 6)
				.. rpad(t_sGMsc," ",10)
				.. rpad(t_sGMscAreaCode," ",4)
				.. rpad(t_sMsc, " ", 10)
				.. lpad(t_sServiceType, "0", 3)
				.. lpad(t_sServiceCode, " ", 4)
				.. tostring(t_iTranType)
				.. tostring(t_iDialType)
				.. tostring(t_iFeeType)
				.. lpad(tostring(t_iFee),"0",6)
				.. tostring(t_iUserType)
				.. rpad(t_sImei, " ", 20)
				.. rpad(t_sUpFileName, " ", 15)
				.. rpad(t_sEmpty," ",6)
				.. rpad(t_sAdn, " ", 15)
				.. rpad(t_sTrunkGroupOut," ",7)
				.. rpad(t_sEmpty, " ", 10)
				;
		 	
	t_iResult	= 1;
end

