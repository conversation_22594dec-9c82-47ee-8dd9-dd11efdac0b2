#!/bin/python
#-*- coding: utf-8 -*-

# 脚本功能: 重批汇总后-生成运营报表工单

#import cx_Oracle
import datetime
import time
import sys
import logging
import os
import traceback
import cmi_common_util
import psycopg2


def fetchAll(dbConnect, sql, parameters = {}, arraysize=500):
    try:
        cur = dbConnect.cursor()
        cur.arraysize = arraysize
        cur.execute(sql, parameters)
        rows = cur.fetchall()
        cur.close()
        return rows
    except psycopg2.Error as exc:
        print(traceback.format_exc())
        logging.error("error:", traceback.format_exc())
    return None

def getTaskId(dbConnect):
    ## make seq
    sql = "select SYS_TASK_LOG$SEQ.nextval from dual"
    cur = dbConnect.cursor()
    cur.execute(sql)
    seqRows = cur.fetchall()
    cur.close()
    seQ = -1

    for row in seqRows:
        seQ = row[0]
    return seQ


def getWorkDays(dbConnect):
    # 查3天内重批汇总结束的工单(本脚本执行周期为2小时)
    sql ="""
            select work_day, ctid from dps_cmi_action_log where action_state=0 and action_code='sms_stat' 
                and redo_task_id>0 and end_time > CURRENT_TIMESTAMP - INTERVAL '3 days' order by work_day
        """
    rowIdslist = []
    workDaysSet = set()

    rows = fetchAll(dbConnect,  sql) 
    if not rows is None and len(rows)>0:
        for row in rows:
            workDay = str(row[0])
            rowId = row[1]

            rowIdslist.append({'row_key': rowId})
            workDaysSet.add(workDay)

    else:
        print("dps_cmi_action_log no data to gen task")
        logging.info("dps_cmi_action_log no data to gen task")
    
    print("rowIdslist:", rowIdslist)
    print("workDaysSet:", workDaysSet)
    return workDaysSet, rowIdslist

def updateState(dbConnect, rowIdslist):
    sql = """
            update dps_cmi_action_log set action_state=8 where ctid = %(row_key)s
        """
    
    if len(rowIdslist) > 0:
        cur = dbConnect.cursor()
        cur.executemany(sql.format(workDay), rowIdslist)
        cur.close()
        dbConnect.commit()


def doReportData(dbConnect, workDay):
    taskNum = 0
    today = datetime.date.today().strftime('%Y%m%d')
    if workDay < today:
        try:
            # 没有正在做的605任务，才插
            iCnt = 0
            qrySql = """select count(1) from SYS_TASK_LOG a, sys_task_args_detail b 
                        where a.task_code=605 and a.state not in (7,0,-1) and date_trunc('day', a.start_time) = CURRENT_DATE 
                        and a.task_id=b.task_id and b.task_args_code='WORK_DAY' and b.task_args_value='{0}'
                    """.format(workDay)
            rows = fetchAll(dbConnect, qrySql ) 
            if not rows is None:
                for row in rows:
                    iCnt = row[0]
                    break
            
            if iCnt == 0:
                cur = dbConnect.cursor() 
                taskId = getTaskId(dbConnect)
                sql = "insert into  SYS_TASK_LOG (task_id,task_flow_name,task_code,start_time,state) values (" + str(taskId) + ", 'extract a2p', 605,sysdate, 1)"
                cur.execute(sql)
                sql = "insert into sys_task_args_detail (task_id,task_args_code, task_args_value) values (" + str(taskId) + ",'WORK_DAY','" + workDay + "')"
                cur.execute(sql)
                sql = "insert into sys_task_args_detail (task_id,task_args_code, task_args_value) values (" + str(taskId) + ",'BUSI_TYPE','14')"
                cur.execute(sql)
                taskNum += 1
            dbConnect.commit()
        except psycopg2.Error as exc:
            print(traceback.format_exc())
            logging.error("error:", traceback.format_exc())
        except Exception as err:
            print(traceback.format_exc())
            logging.error("error:", traceback.format_exc())
            dbConnect.rollback()
        finally:
            if 'cur' in locals():
                cur.close() 
    else:
        print("workDay %s is today. Skipping database operations." % workDay)
    
    return taskNum


if __name__ == '__main__':
    strProdPa = os.getenv('PROD_PA')
    strLogDir = strProdPa + "/center/log/python"
    strLogFile = strLogDir + "/a2p_gen_oper_task.log"

    # 日志信息里增加进程ID
    LOG_FORMAT = '%(asctime)s pid:%(process)d - %(levelname)s - %(message)s'
    DATE_FORMAT = "%m/%d/%Y %H:%M:%S %p"

    logging.basicConfig(filename=strLogFile, level=logging.DEBUG, format=LOG_FORMAT, datefmt=DATE_FORMAT)

    if not os.path.exists(strLogDir):
        os.makedirs(strLogDir)

    print("start....")
    logging.info("start....")
    dbConnect = cmi_common_util.get_gs_connect()

    try:
        taskNum = 0
        taskNumAll = 0
        workDaysSet, rowIdslist = getWorkDays(dbConnect)
        for workDay in workDaysSet:
            taskNum = doReportData(dbConnect, workDay)
            taskNumAll = taskNumAll + taskNum
        
        print("gen taskNumAll:{}".format(taskNumAll))
        logging.info("gen taskNumAll:{}".format(taskNumAll))

        updateState(dbConnect, rowIdslist)
                
    except Exception as err:
        print(traceback.format_exc())
        logging.error("error:", traceback.format_exc())
                
    dbConnect.close()
    print("end...")
    logging.info("success gen a2p operation task ")
