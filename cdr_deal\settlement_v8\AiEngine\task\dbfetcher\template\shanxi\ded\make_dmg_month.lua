require "dbfetcher_util"

function main(args)
    -- 表清理需要挪到前一个lua去做  待修改
    -- 获取数据库连接
    local conn = getConnect()
    -- 获取任务 ID
    local inTaskId = getCode(args, "TASK_ID")

    writeLog("开始处理")

    -- 获取 BILL_MONTH 参数
    billMonth = getBillMonth(conn, inTaskId)
    
    -- 如果获取不到，返回错误
    if not billMonth then
        writeLog("未提供 BILL_MONTH 参数")
        return -1
    end

    -- 实际结算月份等于出账月份前两月，取YYYYMM
    settleMonth = string.sub(getAdjustDate(billMonth, -2), 1, 6)  

    writeLog("BILL_MONTH:" .. billMonth .. "  实际结算月份:" .. settleMonth)

    -- 获取当前月份（两位数）
    local currentMonth = os.date("%m")
    
    -- 获取当前时间完整格式
    local currentTime = os.date("%Y%m%d%H%M%S")

    -- 插入MG业务05类型数据
    local sql = [[INSERT INTO jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[
        (file_name, day_id, month_id, record_type, busi_type, audit_type,
        seq_id, msisdn, sp_code, oper_code, property_code, channel_code, 
        charge_type, use_time, audit_time, audit_fee, cdr_type, content_id,
        order_id, transaction_id, charge_seq, cdr_seq, reserved, out_flag, mrkt_ctvty_id)
        SELECT  '',
                ']] .. settleMonth .. [[01',
                ']] .. currentMonth .. [[',
                '20',
                'MIGU',
                '05',
                a.last_date || lpad(rownum, 6, 0),
                a.msisdn,
                a.sp_code,
                a.oper_code,
                '',
                a.channel_code,
                '03',
                rpad(a.pro_date, 14, '0'),
                ']] .. currentTime .. [[',
                sum(a.ded_fee),
                '02',
                '',
                '',
                '',
                '',
                a.cdr_seq,
                '',
                '0',
                a.mrkt_ctvty_id
        FROM jsdr.dr_sett_mhtus_ddct a
        WHERE a.month_id = ']] .. string.sub(settleMonth, 5, 6) .. [['
          AND a.busi_flag = 'MIGU'
          AND a.ded_fee > 0
        GROUP BY 
            a.last_date || lpad(rownum, 6, 0),
            a.msisdn,
            a.sp_code,
            a.oper_code,
            a.channel_code,
            a.charge_type,
            a.last_date,
            rpad(a.pro_date, 14, '0'),
            a.cdr_seq,
            a.mrkt_ctvty_id
    ]]

    writeLog("插入MG业务05类型数据")
    executeSQL(conn, sql)
    conn:commit()

    -- 构造插入 SQL（核心逻辑：从 staging 表插入到 inner_${settleMonth} 表）
    local sql = [[
        INSERT INTO jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[
        (file_name, day_id, month_id, record_type, busi_type, audit_type,
        seq_id, msisdn, sp_code, oper_code, property_code, channel_code, 
        charge_type, use_time, audit_time, audit_fee, cdr_type, content_id,
        order_id, transaction_id, charge_seq, cdr_seq, reserved, out_flag, mrkt_ctvty_id)
        SELECT 
            file_name, 
            ']] .. settleMonth .. [[01',
            ']] .. currentMonth .. [[',
            '20',
            'MIGU',
            '05',
            seq_id, 
            msisdn, 
            sp_code, 
            oper_code, 
            property_code, 
            channel_code, 
            charge_type, 
            use_time, 
            ']] .. currentTime .. [[',
            audit_fee, 
            cdr_type, 
            content_id,
            order_id, 
            transaction_id, 
            charge_seq, 
            cdr_seq, 
            reserved, 
            out_flag, 
            mrkt_ctvty_id
        FROM jsdr.dr_dmg_audit_staging
    ]]

    writeLog("插入手工导入数据至inner表")
    executeSQL(conn, sql)
    conn:commit()
    ------------------------------------------------------
    writeLog("处理完成")
    conn:close()
    return 0
end

environment = os.getenv("ENVIRONMENT")
if environment == "TEST" then
    main("TASK_ID=17990|BILL_MONTH=202502")
    os.exit()
end
