{
	$ERRCODE = 0;
	$FILE_HEAD = "SUCCESS";
	$CDR_TYPE       =       atoi(IA5STRING(getseppos(t_ori, sepp("0:;"))));
    $ORIGINAL_FILE  =       trimx(IA5STRING(getseppos(t_ori, sepp("1:;")))," ");
    $DIST_FEE_CODE  =       trimx(IA5STRING(getseppos(t_ori, sepp("2:;")))," ");
    $COUNTY_CODE    =       trimx(IA5STRING(getseppos(t_ori, sepp("3:;")))," ");
    $CALL_TYPE      =       atoi(IA5STRING(getseppos(t_ori, sepp("4:;"))));
    $RAW_TAG        =       trimx(IA5STRING(getseppos(t_ori, sepp("5:;")))," ");
    $SEQ    =       atoi(IA5STRING(getseppos(t_ori, sepp("6:;"))));
    $ODN    =       trimx(IA5STRING(getseppos(t_ori, sepp("7:;")))," ");
    $TDN    =       trimx(IA5STRING(getseppos(t_ori, sepp("8:;")))," ");
    $ADN    =       trimx(IA5STRING(getseppos(t_ori, sepp("9:;")))," ");
    $ODN_FIXED      =       trimx(IA5STRING(getseppos(t_ori, sepp("10:;")))," ");
    $TDN_FIXED      =       trimx(IA5STRING(getseppos(t_ori, sepp("11:;")))," ");
    $ADN_FIXED      =       trimx(IA5STRING(getseppos(t_ori, sepp("12:;")))," ");
    $START_TIME     =       trimx(IA5STRING(getseppos(t_ori, sepp("13:;")))," ");
    $DURATION       =       atoi(IA5STRING(getseppos(t_ori, sepp("14:;"))));
    $TIME_SEG       =       atoi(IA5STRING(getseppos(t_ori, sepp("15:;"))));
    $MSRN   =       trimx(IA5STRING(getseppos(t_ori, sepp("16:;")))," ");
    $MSC         =       trimx(IA5STRING(getseppos(t_ori, sepp("17:;")))," ");
    $LAC            =       trimx(IA5STRING(getseppos(t_ori, sepp("18:;")))," ");
    $CELL           =       trimx(IA5STRING(getseppos(t_ori, sepp("19:;")))," ");
    $TRUNK_IN       =       trimx(IA5STRING(getseppos(t_ori, sepp("20:;")))," ");
    $TRUNK_OUT      =       trimx(IA5STRING(getseppos(t_ori, sepp("21:;")))," ");
    $MSC_VENDOR     =       atoi(IA5STRING(getseppos(t_ori, sepp("22:;"))));
    $REGION_CODE    =       trimx(IA5STRING(getseppos(t_ori, sepp("23:;")))," ");
    $TRUNK_IN_OPER  =       atoi(IA5STRING(getseppos(t_ori, sepp("24:;"))));
    $TRUNK_IN_AREA  =       trimx(IA5STRING(getseppos(t_ori, sepp("25:;")))," ");
    $TRUNK_IN_SERV  =       atoi(IA5STRING(getseppos(t_ori, sepp("26:;"))));
    $TRUNK_OUT_OPER =       atoi(IA5STRING(getseppos(t_ori, sepp("27:;"))));
    $TRUNK_OUT_AREA =       trimx(IA5STRING(getseppos(t_ori, sepp("28:;")))," ");
    $TRUNK_OUT_SERV =       atoi(IA5STRING(getseppos(t_ori, sepp("29:;"))));
    $ODN_ACC_TYPE   =       atoi(IA5STRING(getseppos(t_ori, sepp("30:;"))));
    $ODN_ACC_NO     =       trimx(IA5STRING(getseppos(t_ori, sepp("31:;")))," ");
    $ODN_ACC_OPER   =       atoi(IA5STRING(getseppos(t_ori, sepp("32:;"))));
    $ODN_HOME_AREA  =       trimx(IA5STRING(getseppos(t_ori, sepp("33:;")))," ");
    $ODN_VISIT_AREA =       trimx(IA5STRING(getseppos(t_ori, sepp("34:;")))," ");
    $ODN_OPER       =       atoi(IA5STRING(getseppos(t_ori, sepp("35:;"))));
    $ODN_SERV       =       atoi(IA5STRING(getseppos(t_ori, sepp("36:;"))));
    $ODN_NET        =       atoi(IA5STRING(getseppos(t_ori, sepp("37:;"))));
    $ODN_ROAM       =       atoi(IA5STRING(getseppos(t_ori, sepp("38:;"))));
    $ODN_LONG       =       atoi(IA5STRING(getseppos(t_ori, sepp("39:;"))));
    $ODN_TRADEMARK  =       atoi(IA5STRING(getseppos(t_ori, sepp("40:;"))));
    $TDN_ACC_TYPE   =       atoi(IA5STRING(getseppos(t_ori, sepp("41:;"))));
    $TDN_ACC_NO     =       trimx(IA5STRING(getseppos(t_ori, sepp("42:;")))," ");
    $TDN_ACC_OPER   =       atoi(IA5STRING(getseppos(t_ori, sepp("43:;"))));
    $TDN_HOME_AREA  =       trimx(IA5STRING(getseppos(t_ori, sepp("44:;")))," ");
    $TDN_VISIT_AREA =       trimx(IA5STRING(getseppos(t_ori, sepp("45:;")))," ");
    $TDN_OPER       =       atoi(IA5STRING(getseppos(t_ori, sepp("46:;"))));
    $TDN_SERV       =       atoi(IA5STRING(getseppos(t_ori, sepp("47:;"))));
    $TDN_NET        =       atoi(IA5STRING(getseppos(t_ori, sepp("48:;"))));
    $TDN_ROAM       =       atoi(IA5STRING(getseppos(t_ori, sepp("49:;"))));
    $TDN_LONG       =       atoi(IA5STRING(getseppos(t_ori, sepp("50:;"))));
    $TDN_TRADEMARK  =       atoi(IA5STRING(getseppos(t_ori, sepp("51:;"))));
    $SELF_TRADEMARK =       atoi(IA5STRING(getseppos(t_ori, sepp("52:;"))));
    $SIX_SEC        =       atoi(IA5STRING(getseppos(t_ori, sepp("53:;"))));
    $MINUTES        =       atoi(IA5STRING(getseppos(t_ori, sepp("54:;"))));
    $IDLE_SIX_SECS  =       atoi(IA5STRING(getseppos(t_ori, sepp("55:;"))));
    $IDLE_MINUTES   =       atoi(IA5STRING(getseppos(t_ori, sepp("56:;"))));
    $FIVE_MIN       =       atoi(IA5STRING(getseppos(t_ori, sepp("57:;"))));
    $AFTER_MINS     =       atoi(IA5STRING(getseppos(t_ori, sepp("58:;"))));
    $LOCAL_TYPE     =       atoi(IA5STRING(getseppos(t_ori, sepp("59:;"))));
    $ISMULTIMEDIA   =       trimx(IA5STRING(getseppos(t_ori, sepp("60:;")))," ");
    $SETTLE_COND_ID =       atoi(IA5STRING(getseppos(t_ori, sepp("61:;"))));
    $SETTLE_SIDE    =       atoi(IA5STRING(getseppos(t_ori, sepp("62:;"))));
    $ACC_SETTLE_ID  =       atoi(IA5STRING(getseppos(t_ori, sepp("63:;"))));
    $CHARGE_DIR     =       atoi(IA5STRING(getseppos(t_ori, sepp("64:;"))));
    $CHARGE         =       atoi(IA5STRING(getseppos(t_ori, sepp("65:;"))));
    $SETTLE_SIDE1   =       atoi(IA5STRING(getseppos(t_ori, sepp("66:;"))));
    $ACC_SETTLE_ID1 =       atoi(IA5STRING(getseppos(t_ori, sepp("67:;"))));
    $CHARGE_DIR1    =       atoi(IA5STRING(getseppos(t_ori, sepp("68:;"))));
    $CHARGE1        =       atoi(IA5STRING(getseppos(t_ori, sepp("69:;"))));
    $SETTLE_SIDE2   =       atoi(IA5STRING(getseppos(t_ori, sepp("70:;"))));
    $ACC_SETTLE_ID2 =       atoi(IA5STRING(getseppos(t_ori, sepp("71:;"))));
    $CHARGE_DIR2    =       atoi(IA5STRING(getseppos(t_ori, sepp("72:;"))));
    $CHARGE2        =       atoi(IA5STRING(getseppos(t_ori, sepp("73:;"))));
    $DEAL_DATE      =       trimx(IA5STRING(getseppos(t_ori, sepp("74:;")))," ");
    $INPUT_DATE     =       trimx(IA5STRING(getseppos(t_ori, sepp("75:;")))," ");
    $COND_ID        =       trimx(IA5STRING(getseppos(t_ori, sepp("76:;")))," ");
    $RESERVED1      =       trimx(IA5STRING(getseppos(t_ori, sepp("77:;")))," ");
    $RESERVED2      =       trimx(IA5STRING(getseppos(t_ori, sepp("78:;")))," ");
    $RESERVED3      =       trimx(IA5STRING(getseppos(t_ori, sepp("79:;")))," ");
    $SERVICE_ID= 45;
    $DR_TYPE = 4;

//analyze
	//定义一段常量
	c_oper_mobile	= 2;	//	中国移动
	c_oper_rail	= 8;    //铁通
	c_oper_special  = 9999; //	特殊运营商
	///c_call_type_nodeal1 = "11"; //关口局转接局内话单
	c_call_type_nodeal1 = 11;
	//c_call_type_in = "01";    //入局
	c_call_type_in = 1;
	//c_call_type_out = "02";   //出局、
	c_call_type_out = 2;
	c_call_type_nodeal2 = "22";  //关口局转接局外话单
	c_call_type_nodeal2 = 22;
	c_net_type_gsm = 2;     //       网络类型为GSM
	c_net_type_cdma = 3;    //	 网络类型为CDMA
	c_number_type_blank = 1;
	c_number_type_zero  = 2;
	c_number_type_comm  = 3;
	c_number_type_error = 4;
	//处理msc
	l_switch_info = GsmSwitchInfo($MSC);
	l_area = GsmSwitchInfoAreaCode(l_switch_info);
	$REGION_CODE = l_area;
	l_date = substr($START_TIME, 0, 8);
	//add by zhangxj7@20181204
 	if($RAW_TAG == "2" || $RAW_TAG == "11" || $RAW_TAG == "A3")
	{
		$CALL_TYPE = c_call_type_in; //入局:移动被叫
	}
	if($RAW_TAG == "10"|| $RAW_TAG == "12" || $RAW_TAG == "A4")
	{
		$CALL_TYPE = c_call_type_out;////出局:移动主叫
	}
	//处理入中继
	if($TRUNK_IN != "")
	{
		l_in_trunk_info = GsmRouterInfo($MSC,$TRUNK_IN,  l_date);
		bGetInfo = GsmRouterInfoGetInfo(l_in_trunk_info);
		if(bGetInfo == 0 )
		{
			if($RAW_TAG == "2" || $RAW_TAG== "11"||$RAW_TAG == "A3")
			{
				$FILE_HEAD="ERR_TRUNK";
				$ERRCODE=130103;
				return;
			}
			else
			{
				$TRUNK_IN_OPER = c_oper_mobile;
				$TRUNK_IN_AREA = l_area;
				$TRUNK_IN_SERV = 0;
			}	
		}
		else
		{
			$TRUNK_IN_OPER = GsmRouterInfoSettlerId(l_in_trunk_info);
			$TRUNK_IN_AREA = GsmRouterInfoAreaCode(l_in_trunk_info);
			//l_isip = GsmRouterInfoTollType(l_in_trunk_info);
			$TRUNK_IN_SERV = GsmRouterInfoInTrunkBusiId(l_in_trunk_info);
		}	
	}

	//处理出中继
	if($TRUNK_OUT != "")
	{
		l_out_trunk_info = GsmRouterInfo($MSC,$TRUNK_OUT,  l_date);
		bGetInfo = GsmRouterInfoGetInfo(l_out_trunk_info);
		if(bGetInfo == 0)
		{
			if($RAW_TAG == "10" || $RAW_TAG== "12" || $RAW_TAG == "A4")  //出			
			{
				$FILE_HEAD="ERR_TRUNK";
				$ERRCODE=130103;
				return;
			}
			else
			{	
				$TRUNK_OUT_OPER = c_oper_mobile;
				$TRUNK_OUT_AREA = l_area;
				$TRUNK_OUT_SERV = 0;
			}
		}   
		else			
		{
			$TRUNK_OUT_OPER = GsmRouterInfoSettlerId(l_out_trunk_info);
			$TRUNK_OUT_AREA = GsmRouterInfoAreaCode(l_out_trunk_info);
			$TRUNK_OUT_SERV = GsmRouterInfoOutTrunkBusiId(l_out_trunk_info);
		}
	}
	//other oper to 铁通
	if(($CALL_TYPE == c_call_type_out) && ($TRUNK_IN_OPER !=2) && ($TRUNK_OUT_OPER==8))
	{
		//$TRUNK_OUT_OPER=2;
		$TRUNK_IN_OPER=2;
	}
	//铁通to other
	if (($CALL_TYPE == c_call_type_in) && ($TRUNK_OUT_OPER !=2) && ($TRUNK_IN_OPER==8))
	{
		//$TRUNK_IN_OPER=2;
		$TRUNK_OUT_OPER=2;
	}
	//主叫号码
	
	if($ODN == "")
		$ODN = "0";	
	//  add by zhaofx 20140718 携号转网
	st_odn_tmp = $ODN;
	if ( llike($ODN, "1241") || llike($ODN, "1242") || llike($ODN, "1243") )
		st_odn_tmp = substr($ODN, 4, strlen($ODN) - 4);
	l_ori_number_info = NPUserAnalysis(st_odn_tmp, l_date);
	l_ori_number_type = Number_NumberType(l_ori_number_info);	
	if(l_ori_number_type != c_number_type_error)
	{
		lt_odn_oper = Number_OperatorParty(l_ori_number_info);
		lt_port_out_oper_id = NPUser_PortOutOperId(l_ori_number_info);
		lt_home_oper_id = NPUser_HomeOperId(l_ori_number_info);
		$RESERVED2 = ltoa(lt_odn_oper) + ltoa(lt_port_out_oper_id) + ltoa(lt_home_oper_id);
	}
	else
		l_ori_number_info = NumberAnalysisNew(st_odn_tmp, 1, $MSC, l_date, l_area);
	l_ori_number_type = Number_NumberType(l_ori_number_info);	
	if(l_ori_number_type != c_number_type_error||$LOCAL_TYPE == 1)
	{
		$ODN_OPER = Number_OperatorParty(l_ori_number_info);
		l_country_code = Number_HomeCountryCode(l_ori_number_info);
		if(l_country_code == "")
			l_country_code = "86";			
		if (l_country_code == "86")
		{
			$ODN_HOME_AREA = Number_HomeAreaCode(l_ori_number_info);
			if($ODN_HOME_AREA == "")
				$ODN_HOME_AREA = "10";				
		}
		else
		{
			$ODN_HOME_AREA = "00" + l_country_code;
		}
		$ODN_FIXED = Number_OriginalNumber(l_ori_number_info);
		$ODN_VISIT_AREA = $ODN_HOME_AREA;

		$ODN_NET = Number_NetWork(l_ori_number_info);
		if ($ODN_NET == 1 && $ODN_HOME_AREA == "791")
		{
			$ODN_OPER = $TRUNK_IN_OPER;
		}
		$ODN_SERV = Number_SpecialType(l_ori_number_info);
		if($ODN_SERV == 0)
			$ODN_SERV = 1;
		
		$ODN_ACC_TYPE = Number_AccessFlag(l_ori_number_info);
		if(($RAW_TAG == "2" || $RAW_TAG == "11"||$RAW_TAG == "A3")&& $ODN_ACC_TYPE != 0 && $ODN_OPER!=8 && $ODN_ACC_TYPE != 26 )   //modify for 193 ip etc;modify by migi 20121220 tie tong 4007
				$ODN_HOME_AREA = "NNN";		
		if(($RAW_TAG == "10"|| $RAW_TAG == "12"||$RAW_TAG == "A4") && ($ODN_OPER ==2 || $ODN_OPER ==3) && $ODN_HOME_AREA=="NNN")
			$ODN_HOME_AREA = l_area;  //mobile number ,out going unknown 

		l_longtype = GetLongType($ODN_HOME_AREA ,l_area);
		$ODN_LONG = l_longtype;		
		if($ODN_ACC_TYPE == 0)
	    		$ODN_ACC_TYPE = 1;

		//处理国际异常来话
		if($LOCAL_TYPE == 1) 
			$ODN_LONG=2;
		// add by zhaofx 20140326 昌九一体化
		if ($ODN_LONG != 0)
		{
			if ( ($ODN_HOME_AREA == "791" &&  $REGION_CODE == "792") ||
			     ($ODN_HOME_AREA == "792" &&  $REGION_CODE == "791") )
				$ODN_LONG = 0;
				
			//昌抚一体化,20160101开始生效
			t_DtStartTime	= gettime($START_TIME);
			t_DtValidTime	= gettime("20160101000000");
			if( t_DtStartTime >= t_DtValidTime)
			{
				if ( ($ODN_HOME_AREA == "791" &&  $REGION_CODE == "794") ||
				     ($ODN_HOME_AREA == "794" &&  $REGION_CODE == "791") )
					$ODN_LONG = 0;
			}

		}

		$ODN_ACC_OPER = Number_AccessOperator(l_ori_number_info);
		$ODN_ACC_NO = Number_AccessNumber(l_ori_number_info);

		if($ODN_ACC_OPER == 0)
			$ODN_ACC_OPER = $ODN_OPER;
	}
	else
	{
		$FILE_HEAD = "ERRODNNUMBER";
		$ERRCODE = 130102;
		return;
	}
	if(($ODN_NET != 3) && (substr($ODN_HOME_AREA,0,2)!="00")) 
	{	
		l_special_user_info = SpecialUserAnalyze($ODN_FIXED,$ODN_HOME_AREA,l_date);
		if(SpecialUserInfoGetInfo(l_special_user_info)==1)
		{
			$ODN_NET = SpecialUserInfoNumberType(l_special_user_info);
			$ODN_OPER = SpecialUserInfoOperatorId(l_special_user_info);
			//add by zhufd at 20181206
			$OND_BUERAU_CODE=SpecialUserInfoBureauCode(l_special_user_info);
		}
	}
  // 一卡多号
	t_tdn_prepfix = "";
	if ( llike($TDN, "12583") )
	{
		t_tdn_prepfix = substr($TDN, 0, 6);
		$TDN = substr($TDN, 6, strlen($TDN) - 6);
	}
	// 

	//被叫号码	
	if($TDN == "")
	{
		$FILE_HEAD = "ERRTDNNUMBER";
		$ERRCODE = 130202;
		return;
	}

	//  add by zhaofx 20140718 携号转网
	st_tdn_tmp = $TDN;
	if ( llike($TDN, "1241") || llike($TDN, "1242") || llike($TDN, "1243") )
		st_tdn_tmp = substr($TDN, 4, strlen($TDN) - 4);
	
	l_term_number_info = NPUserAnalysis(st_tdn_tmp, l_date);
	l_term_number_type = Number_NumberType(l_term_number_info);
	if(l_term_number_type != c_number_type_error)
	{
		lt_tdn_oper = Number_OperatorParty(l_term_number_info);
		lt_port_out_oper_id = NPUser_PortOutOperId(l_term_number_info);
		lt_home_oper_id = NPUser_HomeOperId(l_term_number_info);
		$RESERVED3 = ltoa(lt_tdn_oper) + ltoa(lt_port_out_oper_id) + ltoa(lt_home_oper_id);
	}
	else
		l_term_number_info = NumberAnalysisNew(st_tdn_tmp, 2, $MSC, l_date, l_area);
	//l_term_number_info = NumberAnalysisNew($TDN, 2, $MSC, l_date, l_area);
	// --
	l_roam_area_code = GetRoamAreaCode($MSRN);
	l_term_number_type = Number_NumberType(l_term_number_info);
	if(l_term_number_type != c_number_type_error)
	{
		//  add by zhaofx 20140718 携号转网
		st_tdn_fixed = Number_OriginalNumber(l_term_number_info);
		if ( llike(st_tdn_fixed, "1241") || llike(st_tdn_fixed, "1242") || llike(st_tdn_fixed, "1243") )
		{
			st_tdn_fixed = substr(st_tdn_fixed, 4, strlen(st_tdn_fixed) - 4);
			l_fixed_number_info = NPUserAnalysis(st_tdn_fixed, l_date);
			l_fixed_number_type = Number_NumberType(l_fixed_number_info);
			if(l_fixed_number_type != c_number_type_error)
			{
				l_term_number_info = l_fixed_number_info;
				lt_tdn_oper = Number_OperatorParty(l_term_number_info);
				lt_port_out_oper_id = NPUser_PortOutOperId(l_term_number_info);
				lt_home_oper_id = NPUser_HomeOperId(l_term_number_info);
				$RESERVED3 = ltoa(lt_tdn_oper) + ltoa(lt_port_out_oper_id) + ltoa(lt_home_oper_id);
			}
			else
				l_term_number_info = NumberAnalysisNew(st_tdn_fixed, 2, $MSC, l_date, l_area);
		}
		// --
		$TDN_OPER = Number_OperatorParty(l_term_number_info);
		l_country_code = Number_HomeCountryCode(l_term_number_info);
		if(l_country_code == "")
			l_country_code = "86";			
		if (l_country_code == "86")
		{
			$TDN_HOME_AREA = Number_HomeAreaCode(l_term_number_info);
			if($TDN_HOME_AREA == "")
				$TDN_HOME_AREA= "10";
		}
		else
		{
			$TDN_HOME_AREA = "00" + l_country_code;
		}
		$TDN_FIXED = Number_OriginalNumber(l_term_number_info);

		$TDN_NET = Number_NetWork(l_term_number_info);
		if ($TDN_NET == 1 && $TDN_HOME_AREA == "791")
		{
			$TDN_OPER = $TRUNK_OUT_OPER;
		}
		$TDN_SERV = Number_SpecialType(l_term_number_info);
		if($TDN_SERV == 0)
			$TDN_SERV = 1;
		if($MSRN!="") //缺省值
			$TDN_VISIT_AREA = l_roam_area_code;
		else
		{
			if((Number_HomeCountryCode(l_term_number_info)!="86") && 
				($CALL_TYPE == c_call_type_in) && (substr($TDN,0,5)!="17951"))
				$TDN_VISIT_AREA = l_area;
		}

		if($TDN_HOME_AREA == "NNN" && ($TDN_OPER == 2 || $TDN_OPER == 3))
			$TDN_HOME_AREA = l_area;
		if($TDN_VISIT_AREA == "")
			$TDN_VISIT_AREA = $TDN_HOME_AREA;

		$TDN_LONG = GetLongType($TDN_HOME_AREA, l_area);

		$TDN_ACC_TYPE = Number_AccessFlag(l_term_number_info);
		if($TDN_ACC_TYPE == 0)
			$TDN_ACC_TYPE = 1;

		$TDN_ACC_OPER = Number_AccessOperator(l_term_number_info);
		$TDN_ACC_NO = Number_AccessNumber(l_term_number_info);

		if($TDN_ACC_OPER == 0)
			$TDN_ACC_OPER = $TDN_OPER ; 
	}
	else
	{
  		$FILE_HEAD = "ERRTDNNUMBER";
		$ERRCODE = 130302;
		return;
	}
	if($TDN_NET != 3 && substr($TDN_HOME_AREA,0,2)!="00") //
	{
		l_special_user_info = SpecialUserAnalyze($TDN_FIXED,$TDN_HOME_AREA,l_date);
		if(SpecialUserInfoGetInfo(l_special_user_info)==1)
		{
			$TDN_NET = SpecialUserInfoNumberType(l_special_user_info);
			$TDN_OPER = SpecialUserInfoOperatorId(l_special_user_info);
			//add by zhufd at 20181206
			$TDN_BUERAU_CODE=SpecialUserInfoBureauCode(l_special_user_info);
		}
	}

	//add user trademark
	//add by zhangxj7 from shmMain get tradeMark
	l_serv_product=ServProductAnalyzeSN($ODN_FIXED);
	$ODN_TRADEMARK=GetServBrand(l_serv_product);
	if($ODN_TRADEMARK == -1)
        {
                  $ODN_TRADEMARK = 0; //判断返回值为-1时，将品牌值重置为0。
        }
 	
	l_serv_product=ServProductAnalyzeSN($TDN_FIXED);
	$TDN_TRADEMARK=GetServBrand(l_serv_product);
        if($TDN_TRADEMARK == -1)
        {
                  $TDN_TRADEMARK = 0; //判断返回值为-1时，将品牌值重置为0。
        }

	$COUNTY_CODE = GetServAreaCode(l_serv_product);

	if($CALL_TYPE == c_call_type_in)
		$SELF_TRADEMARK = $TDN_TRADEMARK;
	else
		$SELF_TRADEMARK = $ODN_TRADEMARK;

	//if($TRUNK_IN_OPER == c_oper_mobile && $CALL_TYPE == c_call_type_in)
	if($TRUNK_IN_OPER == c_oper_mobile && $CALL_TYPE == c_call_type_in && $TRUNK_OUT_OPER !=8 )
	{
		$FILE_HEAD = "ERR INCALL";
		$ERRCODE = 130105;
		return;
	}       

	if($TRUNK_OUT_OPER == c_oper_mobile && $CALL_TYPE == c_call_type_out && $TRUNK_IN_OPER != 8)
	{
		$FILE_HEAD = "ERR INCALL";
		$ERRCODE = 130105;
		return;
	}  
	if($CALL_TYPE == c_call_type_in)
		$SETTLE_SIDE = $TRUNK_IN_OPER;
	else
	{
		if($CALL_TYPE == c_call_type_out)
			$SETTLE_SIDE = $TRUNK_OUT_OPER;
		if($CALL_TYPE == c_call_type_nodeal2)
		{
			$SETTLE_SIDE2 = $TRUNK_OUT_OPER;
			$SETTLE_SIDE = $TRUNK_IN_OPER;
		}	
	}
	if( llike($ODN, "0950") )
	{
		$ODN_FIXED = substr($ODN, 1, length($ODN) - 1);
		$LOCAL_TYPE = 1;
	}
	if( llike($TDN, "0950") )
	{
		$TDN_FIXED = substr($TDN, 1, length($TDN) - 1);
		$LOCAL_TYPE = 1;
	}
	if(($TDN_HOME_AREA == "NNN" && $CALL_TYPE == c_call_type_in) || 
		($CALL_TYPE == c_call_type_out && ($TDN_HOME_AREA == "NNN" ||($LOCAL_TYPE != 1 && $ODN_HOME_AREA == "NNN"))))
	{
		$FILE_HEAD = "ERRTDNNUMBER";
		$ERRCODE = 130402;
		return;
	}
	$TDN_ROAM = GetRoamType(l_term_number_info,l_roam_area_code,itoa($CALL_TYPE));
	$IDLE_SIX_SECS = GetIdleDuration($START_TIME,$DURATION,6);
	$IDLE_MINUTES = GetIdleDuration($START_TIME,$DURATION,60);

	l_six_secs = $SIX_SEC - $IDLE_SIX_SECS;
	$SIX_SEC = l_six_secs;
	if($CALL_TYPE == c_call_type_nodeal1 && $ODN_OPER == c_oper_mobile && $TDN_OPER == c_oper_mobile)
	{
		$FILE_HEAD = "NODEALCALALTYPE";
		$ERRCODE = 139101;
		return;
	}
	// 一卡多号
	$TDN = t_tdn_prepfix + $TDN;
	//enmu(0:长途;1:区内;2:占被叫区间;3:不占被叫区间;)
	if($TDN_NET==1 && $ODN_NET==1 && $ODN_LONG==0  && $TND_LONG==0 )
	{
		$LOCAL_TYPE=10+getSysBureauIsBureauCall($OND_BUERAU_CODE,$TDN_BUERAU_CODE);
	}
	/*新的规则不影响非融合地市，对入/出中继为2，7，8 的非移动GSM 全部归宿移动TD*/
	if(( atoi($REGION_CODE) == 790 ) || ( atoi($REGION_CODE) == 701 ) || ( atoi($REGION_CODE) == 793 ) || ( atoi($REGION_CODE) == 796 ) || ( atoi($REGION_CODE) == 797 ) || ( atoi($REGION_CODE) == 799 ) || ( atoi($REGION_CODE) == 792 ) || ( atoi($REGION_CODE) == 791 ) || ( atoi($REGION_CODE) == 794 ) || ( atoi($REGION_CODE) == 795 ) || ( atoi($REGION_CODE) == 798 ))
        {
		//bps_gsm_route无论配置的settler_id 是3还是9这里都设置成9
		if($TRUNK_IN_OPER == 3 )
		{
			$TRUNK_IN_OPER = 9;
		}
		if($TRUNK_OUT_OPER == 3 )
		{
			$TRUNK_OUT_OPER = 9;
		}
		//bps_gsm_route无论配置的settler_id 是2还是8这里都设置成2
		if($TRUNK_IN_OPER == 8 )
		{
			$TRUNK_IN_OPER = 2;
		}
		if($TRUNK_OUT_OPER == 8 )
		{
			$TRUNK_OUT_OPER = 2;
		}
		//add by zhufd at 20190523
		//联通主叫网络类型不为固网,判定入中继为移网中继
		if(($CALL_TYPE == 1) && ($TRUNK_IN_OPER == 9 && $ODN_NET!=1) )
		{
			//联通转接电信移网实际$ODN_NET=3(CDMA),这里转换成$ODN_NET=2看做成联通移网
			if($ODN_OPER == 1)
			{
				$ODN_NET = 2;
			}
			//国际及港澳台用户通过中国联通固网长途电话网呼叫落地到移动用户,每6秒结入6厘
			if($ODN_NET == 2 && $ODN_LONG == 2)
			{}
			//联通固网长途电话呼叫落地到本地移动用户或特服（含IP落地）,每6秒结入6厘
			else if($ODN_NET == 0 && ($ODN_LONG==1 || $ODN_LONG==10) && $TDN_LONG==0)
			{}
			//联通gsm
			else
			{
				$TRUNK_IN_OPER = 3;
				$SETTLE_SIDE = $TRUNK_IN_OPER;
			}
		}
		else if(($CALL_TYPE == 1) && ($TRUNK_IN_OPER == 9 && $TRUNK_OUT_OPER == 8 && $ODN_NET==1))
		{
			$CALL_TYPE = 21;
			$SETTLE_SIDE = $TRUNK_IN_OPER;
		}
		//联通被叫网络类型不为固网,判定出中继为移网中继
		if(($CALL_TYPE == 2) && ($TRUNK_OUT_OPER == 9 && $TDN_NET!=1) )
		{
			//移动呼叫联通服务台全部放在pstn侧
			if($TDN_NET == 0)
			{}
			else
			{
				$TRUNK_OUT_OPER = 3;
				$SETTLE_SIDE = $TRUNK_OUT_OPER;
			}
		}
		else if(($CALL_TYPE == 2) && ($TRUNK_OUT_OPER == 9 && $TRUNK_IN_OPER == 8 && $ODN_NET==1))
		{
			$CALL_TYPE = 22;
			$SETTLE_SIDE = $TRUNK_OUT_OPER;
		}
		//移动主叫其他运营商,如果网络类型为固网则判定为铁通固话入中继
		if(($CALL_TYPE == 2) && ($TRUNK_IN_OPER == 2 && ($ODN_NET==1||($ODN_NET==0 && $ODN_OPER == 8))))
		{
			$CALL_TYPE = 22;
			$TRUNK_IN_OPER = 8;
		}
		//移动被叫,如果网络类型为固网判定为铁通固话出中继
		if(($CALL_TYPE == 1) && ($TRUNK_OUT_OPER == 2 && ($TDN_NET==1||($TDN_NET==0 && $TDN_OPER == 8))) )
		{
			$CALL_TYPE = 21;
			$TRUNK_OUT_OPER = 8;
		}
		//add by zhufd at 20190523 end

		//通过网络类型判断如果为移动固话，则用铁通与其他运营商的批价规则
                if( ($CALL_TYPE == 1) &&(($TRUNK_OUT_OPER == 2|| $TRUNK_OUT_OPER == 7|| $TRUNK_OUT_OPER == 8)&&(($TDN_NET !=2 )&&($TDN_NET !=6))))
		{	//排除被叫移动特服
                        //if(((TDN_ACC_TYPE !=1) || ($TDN_SERV!=1))&&($TDN_NET ==0)&&($TRUNK_OUT_OPER == 2 || $TRUNK_OUT_OPER == 7))
                        if(((TDN_ACC_TYPE !=1) || ($TDN_SERV!=1))&&($TDN_NET ==0)&&($TDN_OPER != 8))
			{}
			else
				$CALL_TYPE = 21;
		}
                else if(($CALL_TYPE == 2 )&&(($TRUNK_IN_OPER == 2|| $TRUNK_IN_OPER == 7|| $TRUNK_IN_OPER == 8)&&(($ODN_NET != 2)&&($ODN_NET !=6))))
		{	//排除主叫移动特服
                        //if((($ODN_ACC_TYPE !=1) || ($ODN_SERV!=1))&&($ODN_NET ==0)&&($TRUNK_IN_OPER == 2|| $TRUNK_IN_OPER == 7))
                        if((($ODN_ACC_TYPE !=1) || ($ODN_SERV!=1))&&($ODN_NET ==0)&&($ODN_OPER != 8))
			{}
			else
                        	$CALL_TYPE =22;
		}
                else{}
		//移动GSM呼叫联通移网业务台（移网中继）也批到固网业务台
		if( ($CALL_TYPE == 2) && ( $TRUNK_OUT_OPER == 3 ) && ( $TDN_NET == 0 && $TDN_SERV != 1) )
		{
			$TRUNK_OUT_OPER = 9;
			$SETTLE_SIDE = $TRUNK_OUT_OPER;
		}
		//融合地市本地联通业务台外呼中国移动GSM
		if($CALL_TYPE == 1 && ($TRUNK_IN_OPER==3 || $TRUNK_IN_OPER==9 ) && $TRUNK_OUT_OPER==2 && $ODN_SERV != 1 
			&& ($TDN_NET== 2||$TDN_NET==6) && $ODN_LONG == 0)
		{
			if($TRUNK_IN_OPER==3)
			{
				$TRUNK_IN_OPER = 9;
				$SETTLE_SIDE = $TRUNK_IN_OPER;
			}
			$CALL_TYPE=21;
		}
		//融合地市移动业务台外呼中国联通固网
		if($CALL_TYPE == 2 && $TRUNK_IN_OPER==2 && $TRUNK_OUT_OPER==9 && $ODN_SERV != 1 
			&& $TDN_NET== 1 && $ODN_LONG == 0)
		{
			$CALL_TYPE=22;
		}
		//移动TD呼叫联通业务台或特服资费6分每分钟
		if($CALL_TYPE == 2 && $TRUNK_IN_OPER==2 && $TRUNK_OUT_OPER==9 && $ODN_NET == 6
			&& $TDN_SERV != 1 && $ODN_LONG == 0)
		{
			$CALL_TYPE=22;
		}
        }
	cond_id = settleInNewModel($SERVICE_ID,$DR_TYPE, $SETTLE_SIDE,$START_TIME);
    	$COND_ID = itoa(cond_id);
        if($CALL_TYPE == 21||$CALL_TYPE ==1 )
       		$CALL_TYPE = 1;
        else if($CALL_TYPE == 22||$CALL_TYPE ==2 )
                $CALL_TYPE =2;
        else{}
}

