﻿#ifndef _XQUERY_BUSINESS_H_
#define _XQUERY_BUSINESS_H_

#include "xload_business.h"

using namespace settle::ANALYSE_GROUP;

extern int32 odac_app_lookup_CBpsAcccodeRel(
        const xc::CSnapshot &cSnapShot,
        const char *cszSpCode,
        const char *cszOperatorCode,
        CBpsAcccodeRel &cVal);

extern int32 odac_app_lookup_CBpsAccessNumber(
        const xc::CSnapshot &cSnapShot,
        const char *cszAccessNumber,
        const time_t &tm,
        CBpsAccessNumber &cVal);

extern int32 odac_app_lookup_CBpsAddCardFeeCodeSeg(
    const xc::CSnapshot& cSnapShot,
    const char * cszStartMsisdn,
    AISTD list<CBpsAddCardFeeCodeSeg> &lst);

extern int32 odac_app_lookup_CBpsAddFilenametomsc(
        const xc::CSnapshot &cSnapShot,
        const char *cszFileName,
        const time_t &tm,
        CBpsAddFilenametomsc &cVal);

extern int32 odac_app_lookup_CBpsAddISmsRate(
        const xc::CSnapshot &cSnapShot,
        const char *cszCountryCode,
        const time_t &tm,
        CBpsAddISmsRate &cVal);

extern int32 odac_app_lookup_CBpsAddJudgeZone(
        const xc::CSnapshot& cSnapShot,
        const char * cszNumberPrefix,
        const time_t& tm,
        CBpsAddJudgeZone &cVal);

extern int32 odac_app_lookup_CBpsAddMmmCode(
        const xc::CSnapshot& cSnapShot,
        const char * cszMmmCode,
        const time_t& tm,
        CBpsAddMmmCode &cVal);

extern int32 odac_app_lookup_CBpsAddMobileSeg(
        const xc::CSnapshot& cSnapShot,
        const char * cszMCodeSeg,
        const time_t& tm,
        CBpsAddMobileSeg &cVal);

extern int32 odac_app_lookup_CBpsAddOperatorDescription(
        const xc::CSnapshot& cSnapShot,
        const int32& iDrType,
        const int32& iOperId,
        const time_t& tm,
        CBpsAddOperatorDescription &cVal);

extern int32 odac_app_lookup_CBpsAddOperator(
        const xc::CSnapshot& cSnapShot,
        const char * cszOperCode,
        const time_t& tm,
        AISTD list<CBpsAddOperator> &lst);

extern int32 odac_app_lookup_CBpsAddPlatformBusiDesc(
        const xc::CSnapshot& cSnapShot,
        const int32& iAccCode,
        const char * cszOperCode,
        CBpsAddPlatformBusiDesc &cVal);

extern int32 odac_app_lookup_CBpsAddPlatformBusiDescByAccCode(
        const xc::CSnapshot& cSnapShot,
        const int32& iAccCode,
        CBpsAddPlatformBusiDesc &cVal);
//SP_CODE as KEY1, SERV_CODE2 as KEY2, BUSI_CODE as KEY3
extern int32 odac_app_lookup_CBpsAddSpBusiDescBySpCode(
        const xc::CSnapshot& cSnapShot,
        const char * cszSpCode,
        const time_t& tm,
        AISTD list<CBpsAddSpBusiDesc> &lst);
        
extern int32 odac_app_lookup_CBpsAddSpBusiDescByServCode2(
        const xc::CSnapshot& cSnapShot,
        const char * cszServCode,
        const time_t& tm,
        AISTD list<CBpsAddSpBusiDesc> &lst);
        
extern int32 odac_app_lookup_CBpsAddSpBusiDescByBusiCode(
        const xc::CSnapshot& cSnapShot,
        const char * cszBusiCode,
        const time_t& tm,
        CBpsAddSpBusiDesc &cVal);

extern int32 odac_app_lookup_CBpsAddSpProd(
        const xc::CSnapshot& cSnapShot,
        const char * cszBillMonth,
        CBpsAddSpProd &cVal);

extern int32 odac_app_lookup_CBpsAddSpSms(
        const xc::CSnapshot& cSnapShot,
        const char * cszServCode,
        CBpsAddSpSms &cVal);
//CONCAT(MSC_ID, ':',LAC_ID, ':', CELL_ID, ':', AREA_CODE) as KEY1
extern int32 odac_app_lookup_CBpsBorderRoam(
        const xc::CSnapshot& cSnapShot,
        const char *cszMscId,
        const char *cszLacId,
        const char *cszCellId,
        const char *cszAreaCode,
        const time_t &tm,
        CBpsBorderRoam &cVal);
//CONCAT(SERV_TYPE, ':', SERV_CODE) AS KEY1
extern int32 odac_app_lookup_CBpsBsmsServicecode(
        const xc::CSnapshot& cSnapShot,
        const char *cszServType,
        const char *cszServCode,
        const time_t &tm,
        AISTD list<CBpsBsmsServicecode> &lst);
//CONCAT(AREA_CODE, '!', BUSINESS_AREA_CODE1, '!', BUSINESS_AREA_CODE2) AS KEY1
extern int32 odac_app_lookup_CBpsBusinessAreaRel(
        const xc::CSnapshot &cSnapShot,
        const char *cszAreaCode,
        const char *cszBusinessAreaCode1,
        const char *cszBusinessAreaCode2,
        CBpsBusinessAreaRel &cVal);

extern int32 odac_app_lookup_CBpsCardHomeProv(
        const xc::CSnapshot &cSnapShot,
        const char *cszCardCode,
        const time_t &tm,
        CBpsCardHomeProv &cVal);
//CARD_PREFIX as KEY1, CONCAT(CARD_PREFIX, '!', CARD_TYPE) as KEY2
extern int32 odac_app_lookup_CBpsCardPrefixByCardPrefix(
        const xc::CSnapshot &cSnapShot,
        const char *cszCardPrefix,
        CBpsCardPrefix &cVal);

extern int32 odac_app_lookup_CBpsCardPrefix(
        const xc::CSnapshot &cSnapShot,
        const char *cszCardPrefix,
        const char *cszCardType,
        CBpsCardPrefix &cVal);
//IMSI_CODE as KEY1
extern int32 odac_app_lookup_CBpsCarrierImsi(
        const xc::CSnapshot &cSnapShot,
        const char *cszImsiCode,
        CBpsCarrierImsi &cVal);
//CCM_IP as KEY1
extern int32 odac_app_lookup_CBpsCcm(
        const xc::CSnapshot &cSnapShot,
        const char *cszCcmIp,
        CBpsCcm &cVal);
//CONCAT(SERVICE, '!', CODEC, DIRECTION) as KEY1
extern int32 odac_app_lookup_CBpsChannelType(
        const xc::CSnapshot &cSnapShot,
        const int32 &iService,
        const char *cszCodec,
        const int32 &iDirection,
        CBpsChannelType &cVal);
//CONTENT_ID as KEY1
extern int32 odac_app_lookup_CBpsCiContentData(
        const xc::CSnapshot &cSnapShot,
        const char *cszContent,
        const time_t &tm,
        CBpsCiContentData &cVal);
//CONCAT(MSC_ID, '!', TRUNK_ID) as KEY1
extern int32 odac_app_lookup_CBpsFreeGsmRouter(
        const xc::CSnapshot &cSnapShot,
        const char *cszMscId,
        const char *cszTrunkId,
        const time_t &tm,
        CBpsFreeGsmRouter &cVal);
//PROV_CODE as KEY1
extern int32 odac_app_lookup_CBpsGprsIpaddrInfo(
    const xc::CSnapshot &cSnapShot,
    const char *cszProvCode,
    const time_t &tm,
    AISTD list<CBpsGprsIpaddrInfo> &lst);
//MSC_ID as KEY1
extern int32 odac_app_lookup_CBpsGsmMsc(
        const xc::CSnapshot &cSnapShot,
        const char *cszMscId,
        const time_t &tm,
        CBpsGsmMsc &cVal);
//CONCAT(MSC_ID, '!', TRUNK_ID) as KEY1, CONCAT(MSC_ID, '!', TRUNK_ID, '!', AREA_CODE) as KEY2
extern int32 odac_app_lookup_CBpsGsmRouterByMscIdAndTrunkId(
        const xc::CSnapshot &cSnapShot,
        const char *cszMscId,
        const char *cszTrunkId,
        const time_t &tm,
        CBpsGsmRouter &cVal);

extern int32 odac_app_lookup_CBpsGsmRouter(
    const xc::CSnapshot &cSnapShot,
    const char *cszMscId,
    const char *cszTrunkId,
    const char *cszAreaCode,
    const time_t &tm,
    AISTD list<CBpsGsmRouter> &lst);
//HLR_CODE as KEY1
extern int32 odac_app_lookup_CBpsHlrTrademark(
        const xc::CSnapshot& cSnapShot,
        const char * cszHlrCode,
        CBpsHlrTrademark &cVal);
//HLR_CODE as KEY1
extern int32 odac_app_lookup_CBpsHlr(
        const xc::CSnapshot& cSnapShot,
        const char * cszHlrCode,
        const time_t& tm,
        CBpsHlr &cVal);
//IMSI_HEAD as KEY1
extern int32 odac_app_lookup_CBpsImsiHlrRegular(
    const xc::CSnapshot& cSnapShot,
    const char * cszImsiHead,
    const time_t& tm,
    AISTD list<CBpsImsiHlrRegular> &lst
);
//START_IMSI as KEY1, START_IMSI,END_IMSI
extern int32 odac_app_lookup_CBpsImsiNumber(
        const xc::CSnapshot &cSnapShot,
        const char *cszStartImsi,
        const time_t &tm,
        CBpsImsiNumber &cVal);
//2023/06/25
extern int32 odac_app_lookup_CBpsImsiOperInfo(
        const xc::CSnapshot &cSnapShot,
        const char *cszImsiCode,
        const time_t &tm,
        CBpsImsiOperInfo &cVal);
//SELECT CONCAT(PARTNER_ID, '!', LIMIT_TYPE) as KEY1
extern int32 odac_app_lookup_CBpsIrUserLimit(
    const xc::CSnapshot &cSnapShot,
    const char* pPartnerId,
    const char *cszLimitType,
    CBpsIrUserLimit &cVal);
extern int32 odac_app_lookup_CBpsIsmg(
        const xc::CSnapshot &cSnapShot,
        const char *cszIsmgId,
        CBpsIsmg &cVal);
extern int32 odac_app_lookup_CBpsKoreaRoamMsisdn(
        const xc::CSnapshot &cSnapShot,
        const char *cszRoamMsisdn,
        CBpsKoreaRoamMsisdn &cVal);
extern int32 odac_app_lookup_CBpsLac(
        const xc::CSnapshot &cSnapShot,
        const char *cszLac,
        const time_t &tm,
        CBpsLac &cVal);
extern int32 odac_app_lookup_CBpsMisnNumseg(
        const xc::CSnapshot &cSnapShot,
        const char *szNumberHead,
        CBpsMisnNumseg &cVal);
extern int32 odac_app_lookup_CBpsMscCode(
        const xc::CSnapshot &cSnapShot,
        const char *cszTrunkGroup,
        const char *cszTmpMscCode,
        CBpsMscCode &cVal);
/* 
	//delete by zhangxj7 bps_np_use change to MDB SERVER
	extern int32 odac_app_lookup_CBpsNpUser(
        const xc::CSnapshot &cSnapShot,
        const char *cszSerialNumber,
        const time_t &tm,
        CBpsNpUser &cVal);*/
extern int32 odac_app_lookup_CBpsPstnNumseg(
        const xc::CSnapshot &cSnapShot,
        const char *cszAreaCode,
        const char *cszNumberHead,
        const time_t &tm,
        CBpsPstnNumseg &cVal);
extern int32 odac_app_lookup_CBpsPstnRouter(
        const xc::CSnapshot &cSnapShot,
        const char *cszSwitchId,
        const char *cszTrunkId,
        const time_t &tm,
        CBpsPstnRouter &cVal);
extern int32 odac_app_lookup_CBpsPstnSwitch(
        const xc::CSnapshot &cSnapShot,
        const char *cszSwitchId,
        CBpsPstnSwitch &cVal);
extern int32 odac_app_lookup_CBpsRateType(
        const xc::CSnapshot &cSnapShot,
        const char *cszAccessNumber,
        const char *cszOriZoneCode,
        const char *cszDestZoneCode,
        const int32 &iTollGroupId,
        const time_t &tm,
        CBpsRateType &cVal);
extern int32 odac_app_lookup_CBpsSpecialNumber(
        const xc::CSnapshot &cSnapShot,
        const int32 &iServiceId,
        const char *cszSpecialNumber,
        const time_t &tm,
        AISTD list<CBpsSpecialNumber> &lst);
//key与其他字段有关，暂没处理
/*
extern int32 odac_app_lookup_CBpsSpecialUser(
        const xc::CSnapshot &cSnapShot,
        const int32 &iServiceId,
        const char *cszSpecialNumber,
        const time_t &tm,
        CBpsSpecialUser &cVal);
*/        
int32 odac_app_lookup_CBpsSpecialUser(
    const xc::CSnapshot &cSnapShot,
    const int32 &t_SAreaCode,
    const char *cszSpecialNumber,
    const time_t &tm,
    AISTD list<CBpsSpecialUser> &lst
    /*CBpsSpecialUser &cVal*/);
extern int32 odac_app_lookup_CBsrSettleElement(
        const xc::CSnapshot &cSnapShot,
        const int32 &iServiceId,
        const int32 &iBusiType,
        const time_t &tm,
        CBsrSettleElement &cVal);
extern int32 odac_app_lookup_CBpsMgCampInfo(
        const xc::CSnapshot &cSnapShot,
        const char * cszKey,
        // const int32 &iBusiType,
        const time_t &tm,
        CBpsMgCampInfo &cVal);
extern int32 odac_app_lookup_CSysCityByAreaCode(
        const xc::CSnapshot &cSnapShot,
        const char *cszAreaCode,
        const time_t &tm,
        CSysCity &cVal);
extern int32 odac_app_lookup_CSysCityByRegionCode(
        const xc::CSnapshot &cSnapShot,
        const char *cRegionCode,
        const time_t &tm,
        CSysCity &cVal);
extern int32 odac_app_lookup_CSysCountry(
        const xc::CSnapshot &cSnapShot,
        const char *cszCountryCode,
        const time_t &tm,
        CSysCountry &cVal);
extern int32 odac_app_lookup_CSysProv(
        const xc::CSnapshot &cSnapShot,
        const char *cszProvCode,
        const time_t &tm,
        CSysProv &cVal); 
extern int32 odac_app_lookup_CVBpsLacAreaRel(
        const xc::CSnapshot &cSnapShot,
        const char *cszLacId,
        const time_t &tm,
        CVBpsLacAreaRel &cVal);
extern int32 odac_app_lookup_CVBpsLacCeilCountyRel(
        const xc::CSnapshot &cSnapShot,
        const char *cszLacId,
        const char *cszCeilId,
        const time_t &tm,
        CVBpsLacCeilCountyRel &cVal);

extern int32 odac_app_lookup_CVBpsRcdxUserRel(
        const xc::CSnapshot &cSnapShot,
        const char *cszServiceCode,
        CVBpsRcdxUserRel &cVal);
        
extern int32 odac_app_lookup_CVBsrCondition(
        const xc::CSnapshot &cSnapShot,
        const int32 &iServiceId,
        const int32 &iDrType,
        const int32 &iSettleSide,
        AISTD list<CVBsrCondition> &lst);

extern int32 odac_app_lookup_CVBsrCondExpr(
        const xc::CSnapshot &cSnapShot,
        const int32 &iCondId,
        const time_t &tm,
        CVBsrCondExpr &cVal);
extern int32 odac_app_lookup_CVBsrCurveSegments(
        const xc::CSnapshot &cSnapShot,
        const int32 &iCondId,
        const time_t &tm,
        CVBsrCurveSegments &cVal);
extern int32 odac_app_lookup_CVIvrRatio(
        const xc::CSnapshot &cSnapShot,
        const char *cszSpCode,
        const char *cszOperCode,
        const time_t &tm,
        AISTD list<CVIvrRatio> &lst);
extern int32 odac_app_lookup_CVIvrRatioByOperCode(
        const xc::CSnapshot &cSnapShot,
        const char *cszOperCode,
        const time_t &tm,
        AISTD list<CVIvrRatio> &lst);
extern int32 odac_app_lookup_CVIvrRatioBySpCode(
        const xc::CSnapshot &cSnapShot,
        const char *cszSpCode,
        const time_t &tm,
        AISTD list<CVIvrRatio> &lst);
extern int32 odac_app_lookup_CVSpRatioBySpCodeAndSpType(
        const xc::CSnapshot &cSnapShot,
        const char *szSpCode,
        const int32 &iSpType,
        const time_t &tm,
        CVSpRatio &cVal);
extern int32 odac_app_lookup_CVSpRatio(
        const xc::CSnapshot &cSnapShot,
        const char *szSpCode,
        const char *szOperatorCode,
        const int32 &iSpType,
        const time_t &tm,
        CVSpRatio &cVal);
extern int32 odac_app_lookup_CVSpUserInfo(
        const xc::CSnapshot &cSnapShot,
        const char *cszSpCode,
        const char *cszOperCode,
        const char *cszMsisdn,
        const time_t &tm,
        CVSpUserInfo &cVal);

/*
extern int32 odac_app_lookup_CBpsImsiNumber(
        const xc::CSnapshot& cSnapShot,
        const int32 &iServiceId,
        const char *cszKey,
        const time_t &tm,
        CBpsImsiNumber &cVal);
*/

extern int32 odac_app_lookup_CSysProvByArea(
    const xc::CSnapshot &cSnapShot,
    const char *cszAreaCode,
    const time_t &tm,
    CSysProv &cVal);

extern int32 odac_app_lookup_CVBpsIpv4Address(const xc::CSnapshot &cSnapShot, const char *address, const time_t &tm, AISTD list<CVBpsIpv4Address> &lst);

extern int32 odac_app_lookup_CBpsIpv4AddressRange(const xc::CSnapshot &cSnapShot, const char *address, const time_t &tm, AISTD list<CBpsIpv4AddressRange> &lst);

extern int32 odac_app_lookup_CBpsIpv6AddressRange(const xc::CSnapshot &cSnapShot, const char *address, const time_t &tm, AISTD list<CBpsIpv6AddressRange> &lst);

extern int32 odac_app_lookup_CBpsServProduct(const xc::CSnapshot &cSnapShot, const char *msisdn, CBpsServProduct &cVal);

extern int32 odac_app_lookup_CBpsSjDefaultPrice(const xc::CSnapshot& cSnapShot, const char *pszSettlementType, const time_t &tm, AISTD list<CBpsSjSmsDefaultPrice> &lst);

extern int32 odac_app_lookup_CSysBureau(const xc::CSnapshot &cSnapShot, const char *cszBureauCode, CSysBureau &cVal); 

extern int32 odac_app_lookup_CBpsSpecialNet(
    const xc::CSnapshot &cSnapShot,
    const char *pszAreaCode,
    const char *pszBureauCode,
    const char *cszSpecialNumber,
    const time_t &tm,
    AISTD list<CBpsSpecialNet> &lst);
    

#endif /* _CLIENT_QUERY_H */
