{
	$ERRCODE = 0;
	$FILE_HEAD = "SUCCESS";
	$DR_TYPE	=	atoi(IA5STRING(getseppos(t_ori,sepp("0:;"))));
	$ORIGINAL_FILE	=	trimx(IA5STRING(getseppos(t_ori,sepp("1:;")))," ");
	$COUNTY_CODE	=	trimx(IA5STRING(getseppos(t_ori,sepp("2:;")))," ");
	$DIST_FEE_CODE	=	trimx(IA5STRING(getseppos(t_ori,sepp("3:;")))," ");
	$SETTLE_SIDE	=	atoi(IA5STRING(getseppos(t_ori, sepp("4:;"))));  
	$CALL_TYPE	=	trimx(IA5STRING(getseppos(t_ori, sepp("5:;")))," ");
	$RAW_TAG	=	trimx(IA5STRING(getseppos(t_ori, sepp("6:;")))," ");		
	$SEQ		=	atoi(IA5STRING(getseppos(t_ori, sepp("7:;"))));		
	$ODN		=	trimx(IA5STRING(getseppos(t_ori, sepp("8:;")))," ");		
	$TDN		=	trimx(IA5STRING(getseppos(t_ori, sepp("9:;")))," ");		
	$ADN		=	trimx(IA5STRING(getseppos(t_ori, sepp("10:;")))," ");		
	$ODN_FIXED	=	trimx(IA5STRING(getseppos(t_ori, sepp("11:;")))," ");		
	$TDN_FIXED	=	trimx(IA5STRING(getseppos(t_ori, sepp("12:;")))," ");		
	$ADN_FIXED	=	trimx(IA5STRING(getseppos(t_ori, sepp("13:;")))," ");		
	$START_TIME	=	trimx(IA5STRING(getseppos(t_ori, sepp("14:;")))," ");		
	$DURATION	=	atoi(IA5STRING(getseppos(t_ori, sepp("15:;"))));		
	$TIME_SEG	=	atoi(IA5STRING(getseppos(t_ori, sepp("16:;"))));		
	$MSRN		=	trimx(IA5STRING(getseppos(t_ori, sepp("17:;")))," ");		
	$MSC		=	trimx(IA5STRING(getseppos(t_ori, sepp("18:;")))," ");		
	$LAC		=	trimx(IA5STRING(getseppos(t_ori, sepp("19:;")))," ");		
	$CELL		=	trimx(IA5STRING(getseppos(t_ori, sepp("20:;")))," ");		
	$TRUNK_IN	=	trimx(IA5STRING(getseppos(t_ori, sepp("21:;")))," ");		
	$TRUNK_OUT	=	trimx(IA5STRING(getseppos(t_ori, sepp("22:;")))," ");		
	$MSC_VENDOR	=	atoi(IA5STRING(getseppos(t_ori, sepp("23:;"))));
	$REGION_CODE	=	trimx(IA5STRING(getseppos(t_ori, sepp("24:;")))," ");					
	$TRUNK_IN_OPER	=	atoi(IA5STRING(getseppos(t_ori, sepp("25:;"))));		
	$TRUNK_IN_AREA	=	trimx(IA5STRING(getseppos(t_ori, sepp("26:;")))," ");		
	$TRUNK_IN_SERV	=	atoi(IA5STRING(getseppos(t_ori, sepp("27:;"))));		
	$TRUNK_OUT_OPER	=	atoi(IA5STRING(getseppos(t_ori, sepp("28:;"))));		
	$TRUNK_OUT_AREA	=	trimx(IA5STRING(getseppos(t_ori, sepp("29:;")))," ");		
	$TRUNK_OUT_SERV	=	atoi(IA5STRING(getseppos(t_ori, sepp("30:;"))));		
	$ODN_ACC_TYPE	=	atoi(IA5STRING(getseppos(t_ori, sepp("31:;"))));		
	$ODN_ACC_NO	=	trimx(IA5STRING(getseppos(t_ori, sepp("32:;")))," ");		
	$ODN_ACC_OPER	=	atoi(IA5STRING(getseppos(t_ori, sepp("33:;"))));		
	$ODN_HOME_AREA	=	trimx(IA5STRING(getseppos(t_ori, sepp("34:;")))," ");		
	$ODN_VISIT_AREA	=	trimx(IA5STRING(getseppos(t_ori, sepp("35:;")))," ");		
	$ODN_OPER	=	atoi(IA5STRING(getseppos(t_ori, sepp("36:;"))));		
	$ODN_SERV	=	atoi(IA5STRING(getseppos(t_ori, sepp("37:;"))));		
	$ODN_NET	=	atoi(IA5STRING(getseppos(t_ori, sepp("38:;"))));		
	$ODN_ROAM	=	atoi(IA5STRING(getseppos(t_ori, sepp("39:;"))));		
	$ODN_LONG	=	atoi(IA5STRING(getseppos(t_ori, sepp("40:;"))));		
	$ODN_TRADEMARK	=	atoi(IA5STRING(getseppos(t_ori, sepp("41:;"))));		
	$TDN_ACC_TYPE	=	atoi(IA5STRING(getseppos(t_ori, sepp("42:;"))));		
	$TDN_ACC_NO	=	trimx(IA5STRING(getseppos(t_ori, sepp("43:;")))," ");		
	$TDN_ACC_OPER	=	atoi(IA5STRING(getseppos(t_ori, sepp("44:;"))));		
	$TDN_HOME_AREA	=	trimx(IA5STRING(getseppos(t_ori, sepp("45:;")))," ");		
	$TDN_VISIT_AREA	=	trimx(IA5STRING(getseppos(t_ori, sepp("46:;")))," ");		
	$TDN_OPER	=	atoi(IA5STRING(getseppos(t_ori, sepp("47:;"))));		
	$TDN_SERV	=	atoi(IA5STRING(getseppos(t_ori, sepp("48:;"))));		
	$TDN_NET	=	atoi(IA5STRING(getseppos(t_ori, sepp("49:;"))));		
	$TDN_ROAM	=	atoi(IA5STRING(getseppos(t_ori, sepp("50:;"))));		
	$TDN_LONG	=	atoi(IA5STRING(getseppos(t_ori, sepp("51:;"))));		
	$TDN_TRADEMARK	=	atoi(IA5STRING(getseppos(t_ori, sepp("52:;"))));
	$SELF_TRADEMARK	=	atoi(IA5STRING(getseppos(t_ori, sepp("53:;"))));
	$SETTLE_COND_ID =	atoi(IA5STRING(getseppos(t_ori, sepp("54:;"))));		
	$ACC_SETTLE_ID	=	atoi(IA5STRING(getseppos(t_ori, sepp("55:;"))));		
	$CHARGE_DIR	=	atoi(IA5STRING(getseppos(t_ori, sepp("56:;"))));		
	$CHARGE		=	atoi(IA5STRING(getseppos(t_ori, sepp("57:;"))));		
	$ACC_SETTLE_ID2	=	atoi(IA5STRING(getseppos(t_ori, sepp("58:;"))));		
	$CHARGE_DIR2	=	atoi(IA5STRING(getseppos(t_ori, sepp("59:;"))));		
	$CHARGE2	=	atoi(IA5STRING(getseppos(t_ori, sepp("60:;"))));		
	$SETTLE_SIDE2	=	atoi(IA5STRING(getseppos(t_ori, sepp("61:;"))));		
	$ACC_SETTLE_ID3	=	atoi(IA5STRING(getseppos(t_ori, sepp("62:;"))));		
	$CHARGE_DIR3	=	atoi(IA5STRING(getseppos(t_ori, sepp("63:;"))));		
	$CHARGE3	=	atoi(IA5STRING(getseppos(t_ori, sepp("64:;"))));		
	$ACC_SETTLE_ID4	=	atoi(IA5STRING(getseppos(t_ori, sepp("65:;"))));		
	$CHARGE_DIR4	=	atoi(IA5STRING(getseppos(t_ori, sepp("66:;"))));		
	$CHARGE4	=	atoi(IA5STRING(getseppos(t_ori, sepp("67:;"))));		
	$SIX_SEC	=	atoi(IA5STRING(getseppos(t_ori, sepp("68:;"))));		
	$MINUTES	=	atoi(IA5STRING(getseppos(t_ori, sepp("69:;"))));		
	$IDLE_SIX_SECS	=	atoi(IA5STRING(getseppos(t_ori, sepp("70:;"))));		
	$IDLE_MINUTES	=	atoi(IA5STRING(getseppos(t_ori, sepp("71:;"))));		
	$FIVE_MIN	=	atoi(IA5STRING(getseppos(t_ori, sepp("72:;"))));	
	$AFTER_MINS	=	atoi(IA5STRING(getseppos(t_ori, sepp("73:;")))); 
	$LOCAL_TYPE	=	atoi(IA5STRING(getseppos(t_ori, sepp("74:;"))));
	$DEAL_DATE	=	trimx(IA5STRING(getseppos(t_ori, sepp("75:;")))," ");		
	$INPUT_DATE	=	trimx(IA5STRING(getseppos(t_ori, sepp("76:;")))," ");		
	$COND_ID	=	trimx(IA5STRING(getseppos(t_ori, sepp("77:;")))," ");		
	$RESERVED1	=	trimx(IA5STRING(getseppos(t_ori, sepp("78:;")))," ");		
	$RESERVED2	=	trimx(IA5STRING(getseppos(t_ori, sepp("79:;")))," ");		
	$RESERVED3	=	trimx(IA5STRING(getseppos(t_ori, sepp("80:;")))," ");	
	$DEAL_TYPE	=	atoi(IA5STRING(getseppos(t_ori, sepp("81:;"))));	
	$service_id= 45;
	$dr_type = 4;
// 回收话单时，需要读取批次号字段
	if ($DR_TYPE >= 820 && $DR_TYPE <= 822)
		$BATCH_ID = atoi(IA5STRING(getseppos(t_ori,sepp("84:;"))));
	else
		$BATCH_ID = 0;
//analyze
	//定义一段常量
	c_oper_mobile	= 2;//中国移动
	c_oper_rail	= 8;//铁通
	c_oper_rail_n	= 4;//遗留铁通
	c_oper_special  = 9999;//特殊运营商
	c_oper_mix = 800;//专网混群
	c_operid_max = 99;//oper_id的最大值,超出该值为专网
	c_call_type_nodeal1 = "11"; //关口局转接局内话单
	c_call_type_in = "01";  //入局
	c_call_type_out = "02"; //出局
	c_call_type_nodeal2 = "22"; //关口局转接局外话单
	c_net_type_gsm = 2;     //网络类型为GSM
	c_net_type_cdma = 3;    //网络类型为CDMA
	c_number_type_blank = 1;
	c_number_type_zero  = 2;
	c_number_type_comm  = 3;
	c_number_type_error = 4;	
	//按月入库
	$INPUT_DATE = substr($START_TIME, 0, 6);
 	//剔除华为交换机的前转话单
 	if ("18" == $RAW_TAG)
 	{
		$FILE_HEAD = "ERR_TRANS";
		$ERRCODE = 139501;
		return;
 	}
 	//剔除爱立信交换机的前转话单
 	if (1 == $MSC_VENDOR)
 	{
 		if (llike($ORIGINAL_FILE,"E"))
 		{
 			if ("A3" == $RAW_TAG || "B3" == $RAW_TAG)
 			{
				$FILE_HEAD = "ERR_TRANS";
				$ERRCODE = 139501;
				return;
 			}
 		}
 	}
	//处理msc
	l_switch_info = GsmSwitchInfo($MSC);
	l_area = GsmSwitchInfoAreaCode(l_switch_info);
	$REGION_CODE = l_area;
	l_date = substr($START_TIME, 0, 8);
	l_MSC_BureauCode = trimx(GsmSwitchInfoBureauCode(l_switch_info)," ");
	//处理入中继
	if($TRUNK_IN != "")
	{
		if (0 == isSettledGsmRouter($MSC,$TRUNK_IN,l_date) )
		{
			$FILE_HEAD="ERR_TRUNK";
			$ERRCODE=130110;
			return;
		}
    l_in_trunk_info = GsmRouterInfo($MSC,$TRUNK_IN,l_date);
		bGetInfo = GsmRouterInfoGetInfo(l_in_trunk_info);
		if(bGetInfo == 0 )
		{
				$FILE_HEAD="ERR_TRUNK";
				$ERRCODE=130103;
				return;
		}
		else
		{
			$TRUNK_IN_OPER = GsmRouterInfoSettlerId(l_in_trunk_info);
			$TRUNK_IN_AREA = GsmRouterInfoAreaCode(l_in_trunk_info);
			$TRUNK_IN_SERV = GsmRouterInfoInTrunkBusiId(l_in_trunk_info); 
		}
	}
	//处理出中继
	if($TRUNK_OUT != "")
	{
		if (0 == isSettledGsmRouter($MSC,$TRUNK_OUT,l_date) )
		{
			$FILE_HEAD="ERR_TRUNK";
			$ERRCODE=130110;
			return;
		}
		l_out_trunk_info = GsmRouterInfo($MSC,$TRUNK_OUT,  l_date);
		bGetInfo = GsmRouterInfoGetInfo(l_out_trunk_info);
		if(bGetInfo == 0)
		{
				$FILE_HEAD="ERR_TRUNK";
				$ERRCODE=130104;
				return;
		}
		else
		{
			$TRUNK_OUT_OPER = GsmRouterInfoSettlerId(l_out_trunk_info);
			$TRUNK_OUT_AREA = GsmRouterInfoAreaCode(l_out_trunk_info);
			$TRUNK_OUT_SERV = GsmRouterInfoOutTrunkBusiId(l_out_trunk_info);
		}
	}
	it_rail_ticket = 0;
	//分析呼叫类型 - 两个中继都存在的情况
	if(($TRUNK_IN_OPER != c_oper_mobile) && ($TRUNK_IN_OPER != 0) && ($TRUNK_OUT_OPER == c_oper_mobile))
	{
		$CALL_TYPE =c_call_type_in;
		goto call_type_over;
	}
	if($TRUNK_OUT_OPER != c_oper_mobile && $TRUNK_OUT_OPER != 0 &&$TRUNK_IN_OPER == c_oper_mobile)
	{
		$CALL_TYPE =c_call_type_out;
		goto call_type_over;
	}
	if($TRUNK_OUT_OPER == c_oper_mobile &&$TRUNK_IN_OPER == c_oper_mobile)
	{
		$CALL_TYPE =c_call_type_nodeal1;
		goto call_type_over;
	}
	if($TRUNK_OUT_OPER != c_oper_mobile && $TRUNK_OUT_OPER!=0 && $TRUNK_IN_OPER!=0 && $TRUNK_IN_OPER != c_oper_mobile)
	{
	  if($TRUNK_IN_OPER == c_oper_rail_n && $TRUNK_OUT_OPER != c_oper_rail_n && $TRUNK_OUT_OPER != c_oper_rail)
	   {
	   $CALL_TYPE =c_call_type_out;
	   it_rail_ticket = 2;
	   goto call_type_over;
	   }
	  if($TRUNK_IN_OPER == c_oper_rail_n && $TRUNK_OUT_OPER == c_oper_rail)
	   {
	   $CALL_TYPE =c_call_type_in;
	   it_rail_ticket = 2;
	   goto call_type_over;
	   }
	  if($TRUNK_OUT_OPER == c_oper_rail_n && $TRUNK_IN_OPER != c_oper_rail_n && $TRUNK_IN_OPER != c_oper_rail)
	   {
	   $CALL_TYPE =c_call_type_in;
	   it_rail_ticket = 2;
	   goto call_type_over;
	   }
	  if($TRUNK_OUT_OPER == c_oper_rail_n && $TRUNK_IN_OPER == c_oper_rail)
	   {
	   $CALL_TYPE =c_call_type_out;
	   it_rail_ticket = 2;
	   goto call_type_over;
	   }
	   
		$CALL_TYPE = c_call_type_nodeal2;//湖北移动需要根据号码再次分析
		goto call_type_over;
	}
	//分析呼叫类型 只有一个中继的情况
	if($TRUNK_IN_OPER != c_oper_mobile && $TRUNK_OUT_OPER == 0 && $TRUNK_IN_OPER != c_oper_rail_n)
	{
		$CALL_TYPE = c_call_type_in;
		goto call_type_over;
	}
	if($TRUNK_IN_OPER == c_oper_mobile && $TRUNK_OUT_OPER ==0)
	{
		$FILE_HEAD = "ERRCALLTYPE";
		$ERRCODE = 130106;
		return;
	}
	if($TRUNK_OUT_OPER != c_oper_mobile && $TRUNK_IN_OPER == 0 && $TRUNK_OUT_OPER !=c_oper_rail_n)
	{
		$CALL_TYPE = c_call_type_out;
		goto call_type_over;
	}
	if($TRUNK_OUT_OPER == c_oper_mobile && $TRUNK_IN_OPER == 0)
	{
		$FILE_HEAD = "ERRCALLTYPE";
		$ERRCODE = 130106;
		return;
	}
	//湖北移动还需要根据号码判断 CALL_TYPE
call_type_over:	
	
	// 20141110 携号转网改造后，只有应付部分话单出现在A2话单中// 为确保应收话单的精确性，如果有A2的应收话单出现，判为错单.
	if (1 == $MSC_VENDOR && $RAW_TAG == "A2" && $CALL_TYPE == c_call_type_in)
	{
		$FILE_HEAD = "ERRCALLTYPE";
		$ERRCODE = 130113;
		return;
	}
	if($CALL_TYPE == "")
	{
		$FILE_HEAD = "ERRCALLTYPE";
		$ERRCODE = 130106;
		return;
	}
	// 分析主叫号码
	if($ODN == "")
	{
		$FILE_HEAD = "ERRODNNUMBER";
		$ERRCODE = 130102;
		return;
	}
	//去掉爱立信关口局短号码 20180816
		st_ODN_ORI = $ODN;
	if(length($ODN)>12 && substr($ODN,0,9)== "071412540" || substr($ODN,0,9)== "071512540" || substr($ODN,0,9)== "071112540")
		$ODN =substr($ODN,0,4)+ substr($ODN,12,length($ODN)-12);
	//add by zhaofx 20140918 携号转网
	l_ori_number_info = NPUserAnalysis($ODN, l_date);
	l_ori_number_type = Number_NumberType(l_ori_number_info);
	if(l_ori_number_type == c_number_type_error)
	{
		l_ori_number_info = NumberAnalysisNew($ODN, 1, $MSC, l_date, l_area);
		l_ori_number_type = Number_NumberType(l_ori_number_info);
	}
	if(l_ori_number_type != c_number_type_error||$LOCAL_TYPE == 1)//市话类型 1：是 0：否
	{
		$ODN_OPER = Number_OperatorParty(l_ori_number_info);
		l_country_code = Number_HomeCountryCode(l_ori_number_info);
		if(l_country_code == "")
			l_country_code = "86";
		if (l_country_code == "86")
		{
			$ODN_HOME_AREA = Number_HomeAreaCode(l_ori_number_info);
			if($ODN_HOME_AREA == "")
				$ODN_HOME_AREA = "10";
		}
		else
		{
			$ODN_HOME_AREA = "00" + l_country_code;
		}
		$ODN_FIXED = Number_OriginalNumber(l_ori_number_info);
		$ODN_VISIT_AREA = $ODN_HOME_AREA;
		$ODN_NET = Number_NetWork(l_ori_number_info);
		if ($ODN_NET == 1 && $ODN_HOME_AREA == itoa($PROVCODE))
		{
			$ODN_OPER = $TRUNK_IN_OPER;
		}
		$ODN_SERV = Number_SpecialType(l_ori_number_info);	
		if($ODN_SERV == 0)
			$ODN_SERV = 1;
		$ODN_ACC_TYPE = Number_AccessFlag(l_ori_number_info);
		if(($RAW_TAG == "2" || $RAW_TAG== "11")&& $ODN_ACC_TYPE != 0)  //modify for 193 ip etc
				$ODN_HOME_AREA = "NNN";
		if(($RAW_TAG == "10" || $RAW_TAG == "12") && ($ODN_OPER ==2 || $ODN_OPER ==3) && $ODN_HOME_AREA=="NNN")
			$ODN_HOME_AREA = l_area; //mobile number ,out going unknown

		l_longtype = GetLongType($ODN_HOME_AREA ,l_area);
		$ODN_LONG = l_longtype;
		if($ODN_ACC_TYPE == 0)
	    		$ODN_ACC_TYPE = 1;
		//处理国际异常来话
		if($LOCAL_TYPE == 1)
			$ODN_LONG=2;
		$ODN_ACC_OPER = Number_AccessOperator(l_ori_number_info);
		$ODN_ACC_NO = Number_AccessNumber(l_ori_number_info);
		if($ODN_ACC_OPER == 0)
			$ODN_ACC_OPER = $ODN_OPER;
		//湖北移动判断长途落
		if ($ODN_SERV == 2 || $ODN_SERV == 30 || ($ODN_OPER == 1 && $ODN_SERV == 1)) 
		{
			l_areacode = "0" + $REGION_CODE;
			if ( llike($ODN, "400") ||(llike($ODN, "0") && !llike($ODN, l_areacode)) && !llike($ODN, "00"))
			{
				if ($ODN_LONG == 0)
				{
					$ODN_LONG=1;
					$ODN_HOME_AREA="10";
				}
			}
		}
		// 湖北移动卫通
		if ($ODN_OPER == 7)
		{
			if (llike($ODN_FIXED, "1349"))
			{
				$ODN_LONG=1;
				$ODN_HOME_AREA="10";
			}
		}
	}
	else
	{
		$FILE_HEAD = "ERRODNNUMBER";
		$ERRCODE = 130102;
		return;
	}
	st_OdnBureauCode = "LOCAL";
	//增加约束!=6 解决江汉TD号码被当作固话判断赋值出错问题 luoh 20181205
	if(($ODN_NET != 3) && ($ODN_NET != 6) && (substr($ODN_HOME_AREA,0,2)!="00"))
	{
		l_special_user_info = SpecialUserAnalyze($ODN_FIXED,$ODN_HOME_AREA,l_date);
		//l_special_user_info = SpecialUserAnalyzeSN($ODN_FIXED,$ODN_HOME_AREA,"",l_date);
		if(SpecialUserInfoGetInfo(l_special_user_info)==1)
		{
			$ODN_NET = SpecialUserInfoNumberType(l_special_user_info);
			$ODN_OPER = SpecialUserInfoOperatorId(l_special_user_info);
			st_OdnBureauCode = SpecialUserInfoBureauCode(l_special_user_info);
		}
	}
	//分析被叫号码	
	if($TDN == "")
	{
		$FILE_HEAD = "ERRTDNNUMBER";
		$ERRCODE = 130102;
		return;
	}
	//去掉爱立信关口局短号码 20180816
	st_TDN_ORI = $TDN;	
	if(length($TDN)>12 &&substr($TDN,0,9)== "071412540" || substr($TDN,0,9)== "071512540" || substr($TDN,0,9)== "071112540")
		$TDN = substr($TDN,0,4)+substr($TDN,12,length($TDN)-12);
   //一卡多号
	t_tdn_prepfix = "";
	if ( llike($TDN, "12583") )
		$TDN = substr($TDN, 6, strlen($TDN) - 6);
	//add by zhaofx 20140918 携号转网
	st_TDN_RD = "";
	if ( llike($TDN, "1241") || llike($TDN, "1242") || llike($TDN, "1243") )
	{
		st_TDN_RD = substr($TDN, 0, 4);
		$TDN = substr($TDN, 4, strlen($TDN) - 4);
	}
	l_term_number_info = NumberAnalysisNew($TDN, 2, $MSC, l_date, l_area);
	l_roam_area_code = GetRoamAreaCode($MSRN);
	l_term_number_type = Number_NumberType(l_term_number_info);
	if(l_term_number_type != c_number_type_error)
	{
		//  add by zhaofx 20140918 携号转网
		$TDN_FIXED = Number_OriginalNumber(l_term_number_info);
		if ( llike($TDN_FIXED, "1241") || llike($TDN_FIXED, "1242") || llike($TDN_FIXED, "1243") )
		{
			st_TDN_RD = substr($TDN_FIXED, 0, 4);
			$TDN_FIXED = substr($TDN_FIXED, 4, strlen($TDN_FIXED) - 4);
			l_term_number_info = NumberAnalysisNew($TDN_FIXED, 2, $MSC, l_date, l_area);
			l_term_number_type = Number_NumberType(l_term_number_info);
			if(l_term_number_type == c_number_type_error)
			{
  				$FILE_HEAD = "ERRTDNNUMBER";
				$ERRCODE = 130102;
				return;
			}
		}
		$TDN_OPER = Number_OperatorParty(l_term_number_info);
		$TDN_NET = Number_NetWork(l_term_number_info);
		//add by zhaofx 20140918 携号转网
		if (st_TDN_RD != ""  && $TDN_OPER != 7)
		{
			if (st_TDN_RD == "1241" && $TDN_OPER != 1)
			{
				$TDN_OPER = 1;
				$TDN_NET  = 3;
			}
			else if (st_TDN_RD == "1242" && $TDN_OPER != 2)
			{
				$TDN_OPER = 2;
				$TDN_NET = 2;
			}
			else if (st_TDN_RD == "1243" && $TDN_OPER != 3)
			{
				$TDN_OPER = 3;
				$TDN_NET = 2;
			}
		}
		l_country_code = Number_HomeCountryCode(l_term_number_info);
		if(l_country_code == "")
			l_country_code = "86";
		if (l_country_code == "86")
		{
			$TDN_HOME_AREA = Number_HomeAreaCode(l_term_number_info);
			if($TDN_HOME_AREA == "")
				$TDN_HOME_AREA= "10";
		}
		else
		{
			$TDN_HOME_AREA = "00" + l_country_code;
		}
		$TDN_FIXED = Number_OriginalNumber(l_term_number_info);
		if ($TDN_NET == 1 && $TDN_HOME_AREA == itoa($PROVCODE))
		{
			$TDN_OPER = $TRUNK_OUT_OPER;
		}
		$TDN_SERV = Number_SpecialType(l_term_number_info);
		if($TDN_SERV == 0)
			$TDN_SERV = 1;
		if($MSRN!="")
			$TDN_VISIT_AREA = l_roam_area_code;
		else
		{
			if((Number_HomeCountryCode(l_term_number_info)!="86") && 
				($CALL_TYPE == c_call_type_in) && (substr($TDN,0,5)!="17951"))
				$TDN_VISIT_AREA = l_area;
		}
		if($TDN_HOME_AREA == "NNN" && ($TDN_OPER == 2 || $TDN_OPER == 3))
			$TDN_HOME_AREA = l_area;
		if($TDN_VISIT_AREA == "")
			$TDN_VISIT_AREA = $TDN_HOME_AREA;
		$TDN_LONG = GetLongType($TDN_HOME_AREA, l_area);
		$TDN_ACC_TYPE = Number_AccessFlag(l_term_number_info);
		if($TDN_ACC_TYPE == 0)
			$TDN_ACC_TYPE = 1;

		$TDN_ACC_OPER = Number_AccessOperator(l_term_number_info);
		$TDN_ACC_NO = Number_AccessNumber(l_term_number_info);
		if($TDN_ACC_OPER == 0)
			$TDN_ACC_OPER = $TDN_OPER ;
		//IP电话被叫号码为空，默认为长途
		if (9 == $TDN_ACC_TYPE && 0 == $TDN_LONG)
		{
			if ("" == trimx($TDN_FIXED, " "))
				$TDN_LONG = 1;
		}
		//湖北移动卫通
		if ($TDN_OPER == 7)
		{
			if (llike($TDN_FIXED, "1349"))
			{
				$TDN_LONG=1;
				$TDN_HOME_AREA="10";
			}
		}
	}
	else
	{
  		$FILE_HEAD = "ERRTDNNUMBER";
		$ERRCODE = 130102;
		return;
	}
	st_TdnBureauCode = "LOCAL";
	//增加约束!=6 解决江汉TD号码被当作固话判断赋值出错问题 luoh 20181205
	if($TDN_NET != 3 && $TDN_NET != 6 &&substr($TDN_HOME_AREA,0,2)!="00")
	{
		l_special_user_info = SpecialUserAnalyze($TDN_FIXED,$TDN_HOME_AREA,l_date);
		//l_special_user_info = SpecialUserAnalyzeSN($TDN_FIXED,$TDN_HOME_AREA,"",l_date);
		if(SpecialUserInfoGetInfo(l_special_user_info)==1)
		{
			$TDN_NET = SpecialUserInfoNumberType(l_special_user_info);
			$TDN_OPER = SpecialUserInfoOperatorId(l_special_user_info);
			st_TdnBureauCode = SpecialUserInfoBureauCode(l_special_user_info);
		}
	}
	//add user trademark
	$ODN_TRADEMARK=0;//GetServProduct($ODN_FIXED);($TDN_FIXED)
	$TDN_TRADEMARK=0;
	$SELF_TRADEMARK=0;

	if($TRUNK_IN_OPER == c_oper_mobile && $CALL_TYPE == c_call_type_in)
	{
		$FILE_HEAD = "ERR INCALL";
		$ERRCODE = 130105;
		return;
	}
	if($TRUNK_OUT_OPER == c_oper_mobile && $CALL_TYPE == c_call_type_out)
	{
		$FILE_HEAD = "ERR INCALL";
		$ERRCODE = 130105;
		return;
	}	
	//it_rail_ticket = 0;	
	// 铁通融合，重新处理下 $CALL_TYPE 
	if ($CALL_TYPE == c_call_type_nodeal2)
	{
		if (($TRUNK_IN_OPER == c_oper_rail || $TRUNK_IN_OPER == 18) && 
		    ($TRUNK_OUT_OPER != c_oper_rail && $TRUNK_OUT_OPER != 18))
		{
			$CALL_TYPE = c_call_type_out;
		  it_rail_ticket = 1;
		}
		else if  (($TRUNK_IN_OPER != c_oper_rail && $TRUNK_IN_OPER != 18) && 
		          ($TRUNK_OUT_OPER == c_oper_rail || $TRUNK_OUT_OPER == 18))
		{
			$CALL_TYPE = c_call_type_in;
      it_rail_ticket = 1;		
		}			
		if (($TRUNK_IN_OPER == c_oper_rail || $TRUNK_IN_OPER == 18) &&
		    ($TRUNK_OUT_OPER == c_oper_rail || $TRUNK_OUT_OPER == 18))
		  {
		    $CALL_TYPE = c_call_type_nodeal1;
			}
	}	
	//add by jiangxb3 20180427 铁通融合有固话与移话混群。调整指定的固话号段类型新增为21
	if ($TRUNK_IN_OPER == 2 && (($ODN_NET == 21 || $ODN_OPER ==8) && $ODN_LONG == 0))
	  {
	    $TRUNK_IN_OPER = 8;
	    if($TRUNK_OUT_OPER == c_oper_rail_n)
	      it_rail_ticket = 2;
	    else
	      it_rail_ticket = 1;
	  }
	else if ($TRUNK_OUT_OPER == 2 && (($TDN_NET == 21 || $TDN_OPER ==8) && $TDN_LONG == 0))
	 {
	    $TRUNK_OUT_OPER = 8;
	    if($TRUNK_IN_OPER == c_oper_rail_n)
	      it_rail_ticket = 2;
	    else
	      it_rail_ticket = 1;
	  }

	if($CALL_TYPE == c_call_type_in)
	 {
	  $SETTLE_SIDE = $TRUNK_IN_OPER;
	 }
	else
	{
		if($CALL_TYPE == c_call_type_out)
		 {
		  $SETTLE_SIDE = $TRUNK_OUT_OPER;			
		 }
		if($CALL_TYPE == c_call_type_nodeal2)
		{
			if ($TRUNK_OUT_OPER < c_operid_max)
			{
				$SETTLE_SIDE = $TRUNK_OUT_OPER;
				$SETTLE_SIDE2 = $TRUNK_IN_OPER;
			}
			else
			{
				$SETTLE_SIDE2 = $TRUNK_OUT_OPER;
				$SETTLE_SIDE = $TRUNK_IN_OPER;
			//对外开放95、96、123的业务台	与 外运营商呼叫，作为移动固话结算。
			//只考虑呼入;呼出有语音专线，这种以普通移动网结算 20181015  不加批价规则
				if ($TRUNK_OUT_OPER > c_operid_max && $TRUNK_IN_OPER != 2 )
				  {
				   $CALL_TYPE = c_call_type_in;
				   it_rail_ticket = 1; 
				  }
				
			}
		}
	}
	if(($TDN_HOME_AREA == "NNN" && $CALL_TYPE == c_call_type_in) || 
		($CALL_TYPE == c_call_type_out && ($TDN_HOME_AREA == "NNN" ||($LOCAL_TYPE != 1 && $ODN_HOME_AREA == "NNN"))))
	{
		$FILE_HEAD = "ERRTDNNUMBER";
		$ERRCODE = 130102;
		return;
	}
	$TDN_ROAM = GetRoamType(l_term_number_info,l_roam_area_code,$CALL_TYPE);
  if($CALL_TYPE == c_call_type_nodeal1)
	{
		$FILE_HEAD = "NODEALCALALTYPE";
		$ERRCODE = 139101;
		return;
	}
	//湖北移动出入中继的对端运营商都不是移动时，根据主被叫号码分析CALL_TYPE。//增加C网13的判断 20180426
	if ($CALL_TYPE == c_call_type_nodeal2)
	{
		if ($ODN_OPER > c_operid_max || $ODN_OPER == 2 || $ODN_OPER == 8)
		{
			$CALL_TYPE = c_call_type_out;
			
			if ($ODN_OPER == 2)
			{
			  if ($TRUNK_OUT_OPER == 13)
			    $SETTLE_SIDE = $TRUNK_OUT_OPER;
			  else
				  $SETTLE_SIDE = $TDN_OPER;
			}
			else if ($ODN_OPER == 8)
			{
			  it_rail_ticket = 1;
				if ($TRUNK_OUT_OPER == 13)
					$SETTLE_SIDE = 13;
				else if ($TRUNK_IN_OPER == 4 && $TRUNK_OUT_OPER == 4 && $TDN_OPER==8)
				  {$SETTLE_SIDE = 4;
				it_rail_ticket = 2;}
				else
					$SETTLE_SIDE = $TDN_OPER;
			}
			$TRUNK_OUT_OPER = $SETTLE_SIDE;
		}
		else if ($TDN_OPER > c_operid_max || $TDN_OPER == 2 || $TDN_OPER == 8)
		{
			$CALL_TYPE = c_call_type_in;			
			if ($TDN_OPER == 2)
			{
			  if ($TRUNK_IN_OPER == 13)
			    $SETTLE_SIDE = $TRUNK_IN_OPER;
			  else
			    $SETTLE_SIDE = $ODN_OPER;
			}
			else if ($TDN_OPER == 8)
			{
			  it_rail_ticket = 1;
				if ($TRUNK_IN_OPER == 13)
					$SETTLE_SIDE = 13;
				else if ($TRUNK_IN_OPER == 4 && $TRUNK_OUT_OPER == 4 && $ODN_OPER==8)
				  {$SETTLE_SIDE = 4;
				it_rail_ticket = 2;}
				else
					$SETTLE_SIDE = $ODN_OPER;	
			}
			$TRUNK_IN_OPER = $SETTLE_SIDE;
		}
	}
	// IMS
	if ($SETTLE_SIDE == 18)
		$SETTLE_SIDE = 8;
	if ($SETTLE_SIDE2 == 18)
		$SETTLE_SIDE2 =8;
	// 专网结算对象$SETTLE_SIDE2
	if ($SETTLE_SIDE > c_operid_max)
	{
		$SETTLE_SIDE2 = $SETTLE_SIDE;
		//铁通遗留话单呼叫移动专网、混群 20181015 加批价规则
		if ($TRUNK_OUT_OPER > c_operid_max && $TRUNK_IN_OPER == 4)
		  {
		  $SETTLE_SIDE  = $TRUNK_IN_OPER;
		  $CALL_TYPE = c_call_type_in;
		  it_rail_ticket = 2;
		  }
		else if ($TRUNK_OUT_OPER > c_operid_max && $TRUNK_IN_OPER != 2)
		  {
		  $SETTLE_SIDE  = $TRUNK_IN_OPER;
		  $CALL_TYPE = c_call_type_in;
		  it_rail_ticket = 1;
		  }
		else{
		$SETTLE_SIDE  = 0;
		}
	}
	// 900开头话单打为错单
	if (10 == $ODN_ACC_TYPE || 10 == $TDN_ACC_TYPE)
	{
		$FILE_HEAD = "ERRNUMBER";
		$ERRCODE = 139401;
		return;
	}	
	// 出入中继对端运营商、主被号码归属运营商都没有移动，该话单不参与网间语音结算
	if ( $SETTLE_SIDE > 0 && $SETTLE_SIDE <= c_operid_max && it_rail_ticket == 0 )
	{
		if ($ODN_OPER != 2 && $TDN_OPER !=2 && $TRUNK_IN_OPER !=2 && $TRUNK_OUT_OPER !=2 )
		{
			if ($TRUNK_IN_OPER < c_operid_max && $TRUNK_OUT_OPER < c_operid_max ) 
				    $SETTLE_SIDE = 0;
		}
	}
	 // 专网错单
  if ($SETTLE_SIDE2 > c_operid_max)
	 {
	 		if ($DURATION <= 3) 
	 		{
	 			if ($SETTLE_SIDE <= 0)
	 			{
					$FILE_HEAD = "NODEALDURATION";
					$ERRCODE = 139201;
					return;
				}
				else
					$SETTLE_SIDE2 = 0;
	 		}
	 		if (llike($TDN_FIXED, "12121") && ($REGION_CODE=="27" || $REGION_CODE=="710" || $REGION_CODE=="716"))
	 		{
	 			if ($SETTLE_SIDE <= 0)
	 			{
					$FILE_HEAD = "ERRTDNNUMBER";
					$ERRCODE = 139301;
					return;
				}
				else
					$SETTLE_SIDE2 = 0;
	 		}
	 		// 专网用户呼叫联通用户，生成2条话单，// 出中继对端运营商,一条为移动(A0)，一条为联通(A2)，
	 		// 网间结算一次，但专网会结算两次
	 		if ($RAW_TAG == "A0" && $TRUNK_OUT_OPER == c_oper_mobile && $TDN_OPER != c_oper_mobile )
				$SETTLE_SIDE2 = 0;
	 }
	 // 分析专网号头
	 if ($SETTLE_SIDE2 > c_operid_max)
	 {
	    if(0 == $ODN_LONG && $TRUNK_IN_OPER>=c_operid_max)
	    {
	    	l_special_user_info = SpecialUserAnalyzeSN($ODN_FIXED,$REGION_CODE,l_MSC_BureauCode,$START_TIME);
				if(SpecialUserInfoGetInfo(l_special_user_info)==1)
	    		$ODN_OPER = SpecialUserInfoOperatorId(l_special_user_info);
	    }
		  if(0 == $TDN_LONG && $TRUNK_OUT_OPER>=c_operid_max)
	    {
	    	l_special_user_info = SpecialUserAnalyzeSN($TDN_FIXED,$REGION_CODE,l_MSC_BureauCode,$START_TIME);
	    	if(SpecialUserInfoGetInfo(l_special_user_info)==1)
	    		$TDN_OPER = SpecialUserInfoOperatorId(l_special_user_info);
	    }
	 		if ($SETTLE_SIDE2 >= c_oper_mix)
	 		{
				if ($ODN_OPER > c_operid_max)
					$SETTLE_SIDE2 = $ODN_OPER;
				else if ($TDN_OPER > c_operid_max)
					$SETTLE_SIDE2 = $TDN_OPER;	
			}
	 }	 
	if ($DURATION > 180)
		$AFTER_MINS	= ($DURATION - 180 + 59) / 60;	 
	if ($SETTLE_SIDE2 > c_operid_max)
	{
		$IDLE_MINUTES = ($DURATION + 179) / 180;		
		// IDLE_MINUTES已用来表示三分钟数，用TIME_SEG区分闲时和忙时
		if(substr($START_TIME,8,2)<"07")
			$TIME_SEG = 1;
		else
			$TIME_SEG = 0;
	}	
	//铁通融合判断是否占被叫区间
	if ($ODN_LONG == 0 && $TDN_LONG == 0)
	{
		if ((st_OdnBureauCode == st_TdnBureauCode) || (st_TdnBureauCode == "LOCAL"))
			$LOCAL_TYPE	= 0;
		else
			$LOCAL_TYPE	= 2;
			
		$RESERVED1 = st_OdnBureauCode + "|" + st_TdnBureauCode;
	}	
	//铁通融合话单call_type 枚举值加50
	if (it_rail_ticket == 1)
		$CALL_TYPE = itoa(atoi($CALL_TYPE) + 50);	 	 
	// special dr filter
	l_md5_string = $CALL_TYPE;
	l_md5 = GetCondId(l_md5_string,strlen(l_md5_string));
	$COND_ID = l_md5;
	$TDN = st_TDN_ORI;
	$ODN = st_ODN_ORI;
	printStr("come here now "+$RESERVED3+" "+itoa($MSC_VENDOR));
	//爱立信话单需要剔除重单 (正常单回收无需查重)
	if (1 == $MSC_VENDOR && $RESERVED3 != "N") {
		s_md5_key = $RAW_TAG + "|" + $ADN + "|" + $START_TIME;
		t=CheckDupDb(s_md5_key,l_date,$DupDir+$REGION_CODE);
		//t=CheckDupDb(s_md5_key,l_date,"wjVoiceAna"+$REGION_CODE);
		if(t == -1 ) {
			$FILE_HEAD="ERR_DUP";
			$ERRCODE=130888;
			return;
		}
		else if(t<-1){
			$FILE_HEAD="ERR_CREATE_DUP";
			$ERRCODE=130889;
			return;
		}
	}
}
