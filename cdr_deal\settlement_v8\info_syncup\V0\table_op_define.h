#ifndef TABLE_OP_DEFINE_H
#define TABLE_OP_DEFINE_H

#include <vector>
#include <string>
#include <otl.h>
#include "table_struct_define.h"

using namespace otl_proxy;

// ============================================================================
// 基类定义
// ============================================================================

class OpTableBaseBase {
public:
    virtual ~OpTableBaseBase() {}
    virtual std::string get_querySql(const std::string condition=" 1=1") = 0;
    virtual std::string get_insertSql() = 0;
    virtual std::string get_updateSql(const std::string condition=" 1=1") = 0;
    virtual std::string get_deleteSql(const std::string condition=" 1=1") = 0;

    // 纯虚函数，用于创建具体的数据对象
    virtual CTabelStructBase* createDataObject() = 0;

    // 表名管理虚函数
    virtual void set_tableNameByMon(const std::string& dateStr) = 0;
    virtual void set_baseTable(const std::string& baseTable) = 0;
    virtual std::string get_baseTable() const = 0;
    virtual std::string get_tableName() const = 0;

    // 通过基类指针对象存放查询结果
    virtual int run_querySql(otl_connect &db_conn, const std::string& qrySql, 
                           std::vector<CTabelStructBase*>& resDataList, int32 iLimit=0);
    
    virtual int run_deleteSql(otl_connect& db_conn, const std::string& delSql);


    // 模板函数，可以指定数据对象类型，用于插入
    template <class T>
    int run_insertSql(otl_connect& db_conn, const std::vector<T>& dataList) {
        otl_stream db_stream;
        std::string insertSql = get_insertSql();

        std::cout << "insertSql: " << insertSql << std::endl;

        try {
            db_stream.open(100, insertSql.c_str(), db_conn);

            for (const auto& data : dataList) {
                db_stream << data; // 使用重载的 << 操作符
            }

            db_stream.close();
            return dataList.size();
        } catch (otl_exception& e) {
            if (err_msg.find("duplicate key") != std::string::npos ||
                err_msg.find("Duplicate entry") != std::string::npos) 
            {              
                db_stream.close();
                return 0;
            }
            else
            {
                std::cerr << "OTL Exception code: " << e.code << std::endl;
                std::cerr << "OTL Exception: " << e.msg << std::endl;
                std::cerr << "VAR_INFO:"<< e.var_info << std::endl;

                db_conn.rollback();
                return -1;
            }
        }
    }

    // 通过基类指针对象插入
    virtual int run_insertSql(otl_connect& db_conn, const std::vector<CTabelStructBase*>& dataList);
};

template<class T>
class OpTableBase : public OpTableBaseBase {
protected:
    std::string table_name;
    std::string m_strBaseTable;  // 基础表名（不含日期后缀）

public:
    OpTableBase() {}
    virtual ~OpTableBase() {}

    // 重写基类的虚函数
    void set_baseTable(const std::string& baseTable) override {
        m_strBaseTable = baseTable;
    }

    std::string get_baseTable() const override {
        return m_strBaseTable;
    }

    void set_tableNameByMon(const std::string& dateStr) override {
        if (!m_strBaseTable.empty()) {
            table_name = m_strBaseTable + "_" +dateStr;
        }
    }

    std::string get_tableName() const override {
        return table_name;
    }
};

// ============================================================================
// 操作类声明
// ============================================================================

class OpIDataIndex : public OpTableBase<CTabelStructBase> {
public:
    OpIDataIndex() {
        m_strBaseTable = "I_DATA_INDEX";
        table_name = m_strBaseTable;  // 默认使用基础表名
    }

    // 虚函数声明：实现在 table_op_define.cpp 中
    std::string get_querySql(const std::string condition=" 1=1") override;
    std::string get_insertSql() override;
    std::string get_deleteSql(const std::string condition=" 1=1") override;
    std::string get_updateSql(const std::string condition=" 1=1") override;
    CTabelStructBase* createDataObject() override;
};

class OpIDataIndexHis : public OpTableBase<CTabelStructBase> {
public:
    OpIDataIndexHis() {
        m_strBaseTable = "I_DATA_INDEX_HIS";
        table_name = m_strBaseTable;  // 默认使用基础表名
    }

    // 虚函数声明：实现在 table_op_define.cpp 中
    std::string get_querySql(const std::string condition=" 1=1") override;
    std::string get_insertSql() override;
    std::string get_deleteSql(const std::string condition=" 1=1") override;
    std::string get_updateSql(const std::string condition=" 1=1") override;
    CTabelStructBase* createDataObject() override;
};

class OpIDataIndexErr : public OpTableBase<CTabelStructBase> {
public:
    OpIDataIndexErr() {
        m_strBaseTable = "I_DATA_INDEX_ERR";
        table_name = m_strBaseTable;  // 默认使用基础表名
    }

    // 虚函数声明：实现在 table_op_define.cpp 中
    std::string get_querySql(const std::string condition=" 1=1") override;
    std::string get_insertSql() override;
    std::string get_deleteSql(const std::string condition=" 1=1") override;
    std::string get_updateSql(const std::string condition=" 1=1") override;
    CTabelStructBase* createDataObject() override;
};

class OpIMsisdnInfo : public OpTableBase<CTabelStructBase> {
public:
    OpIMsisdnInfo() {
        m_strBaseTable = "I_MSISDN_INFO";
        table_name = m_strBaseTable;  // 默认使用基础表名
    }

    // 虚函数声明：实现在 table_op_define.cpp 中
    std::string get_querySql(const std::string condition=" 1=1") override;
    std::string get_insertSql() override;
    std::string get_updateSql(const std::string condition=" 1=1") override;
    std::string get_deleteSql(const std::string condition=" 1=1") override;
    CTabelStructBase* createDataObject() override;
};

class OpINationalMnp : public OpTableBase<CTabelStructBase> {
public:
    OpINationalMnp() {
        m_strBaseTable = "I_NATIONAL_MNP";
        table_name = m_strBaseTable;  // 默认使用基础表名
    }

    // 虚函数声明：实现在 table_op_define.cpp 中
    std::string get_querySql(const std::string condition=" 1=1") override;
    std::string get_insertSql() override;
    std::string get_updateSql(const std::string condition=" 1=1") override;
    std::string get_deleteSql(const std::string condition=" 1=1") override;
    CTabelStructBase* createDataObject() override;
};

class OpBpsMsisdnInfo : public OpTableBase<CTabelStructBase> {
public:
    OpBpsMsisdnInfo() {
        m_strBaseTable = "BPS_MSISDN_INFO";
        table_name = m_strBaseTable;  // 默认使用基础表名
    }

    // 虚函数声明：实现在 table_op_define.cpp 中
    std::string get_querySql(const std::string condition=" 1=1") override;
    std::string get_insertSql() override;
    std::string get_updateSql(const std::string condition=" 1=1") override;
    std::string get_deleteSql(const std::string condition=" 1=1") override;
    CTabelStructBase* createDataObject() override;
};

class OpBpsNationMnp : public OpTableBase<CTabelStructBase> {
public:
    OpBpsNationMnp() {
        m_strBaseTable = "BPS_NATION_MNP";
        table_name = m_strBaseTable;  // 默认使用基础表名
    }

    // 虚函数声明：实现在 table_op_define.cpp 中
    std::string get_querySql(const std::string condition=" 1=1") override;
    std::string get_insertSql() override;
    std::string get_updateSql(const std::string condition=" 1=1") override;
    std::string get_deleteSql(const std::string condition=" 1=1") override;
    CTabelStructBase* createDataObject() override;
};

#endif // TABLE_OP_DEFINE_H
