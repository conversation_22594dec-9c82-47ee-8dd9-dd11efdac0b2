// 老模板: hb_sj_check_goldcoins_book_f.expr
// 文件名                                               业务类型                             old_cdr_type  new_cdr_type
// goldCoins_direct_rechargeUpdate_ZZZ_YYYYMMDD_NNN.txt 定向金币充值生效失效时间修改信息同步接口   1180       1265

FILE
{
	PROPERTY
	{
		PROPERTY_FILE_CHECK = NO
		PROPERTY_FILTER_CHECK = YES
		PROPERTY_FILE_ENCODE_TYPE = ASCII
		PROPERTY_STACK_ENABLE = NO
	}
	RECORD
    {
        RECORD_NAME = d_control_record
        RECORD_TYPE = CONTROL

        FIELD_NAME = BEGIN CASE 20 RECORD_NEXT_RECORD = body_record
        FIELD_NAME = BEGIN CASE 90 RECORD_NEXT_RECORD = other_record

        FIELD
        {
            FIELD_NAME = BEGIN
            FIELD_NEXT_FIELD = NULL
            FIELD_LEAF = YES
            DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
            DEAL
            {
                DEAL_FUNC_TYPE = str2str_byList
                DEAL_PARA1 = 20,other
                DEAL_PARA2 = 20,90
            }
        }
    }
    //tail
    RECORD
    {
        RECORD_NAME = other_record
        RECORD_TYPE = TAIL

		RECORD_XDR_OUTPUT FILTER_CODE = F9007
    }
    //body
	RECORD
	{
		RECORD_NAME = body_record
		RECORD_TYPE = BILL

		RECORD_XDR_OUTPUT DR_TYPE = 4
		RECORD_XDR_OUTPUT SERVICE_ID = 12
        RECORD_XDR_OUTPUT TENANT_ID = 0

		RECORD_CONVERT_FUNC = convert_get_tenantid
		RECORD_CONVERT_FUNC = convert_get_filecode

		FIELD	// 用户手机号
		{
			FIELD_NAME = BEGIN
			FIELD_NEXT_FIELD = jb_drct_rechg_updt_field_1
			FIELD_LEAF = YES
			FIELD_XDRKEY = PHONE

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 用户归属省
		{
			FIELD_NAME = jb_drct_rechg_updt_field_1
			FIELD_NEXT_FIELD = jb_drct_rechg_updt_field_2
			FIELD_LEAF = YES
			FIELD_XDRKEY = PROV_CODE

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 充值交易ID
		{
			FIELD_NAME = jb_drct_rechg_updt_field_2
			FIELD_NEXT_FIELD = jb_drct_rechg_updt_field_3
			FIELD_LEAF = YES
			FIELD_XDRKEY = ORDER_ID

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 充值交易时间
		{
			FIELD_NAME = jb_drct_rechg_updt_field_3
			FIELD_NEXT_FIELD = jb_drct_rechg_updt_field_4
			FIELD_LEAF = YES
			FIELD_XDRKEY = START_TIME

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 生效时间
		{
			FIELD_NAME = jb_drct_rechg_updt_field_4
			FIELD_NEXT_FIELD = jb_drct_rechg_updt_field_5
			FIELD_LEAF = YES
			FIELD_XDRKEY = RESERVED3

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 失效时间
		{
			FIELD_NAME = jb_drct_rechg_updt_field_5
			FIELD_NEXT_FIELD = jb_drct_rechg_updt_field_6
			FIELD_LEAF = YES
			FIELD_XDRKEY = RESERVED4

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 金币券活动编号
		{
			FIELD_NAME = jb_drct_rechg_updt_field_6
			FIELD_NEXT_FIELD = jb_drct_rechg_updt_field_7
			FIELD_LEAF = YES
			FIELD_XDRKEY = GOODS_CODE

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 用户金币券编号
		{
			FIELD_NAME = jb_drct_rechg_updt_field_7
			FIELD_NEXT_FIELD = jb_drct_rechg_updt_field_8
			FIELD_LEAF = YES
			FIELD_XDRKEY = SHOPCODE

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 备用字段1
		{
			FIELD_NAME = jb_drct_rechg_updt_field_8
			FIELD_NEXT_FIELD = jb_drct_rechg_updt_field_9
			FIELD_LEAF = YES
			FIELD_XDRKEY = RESERVED1

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 备用字段2
		{
			FIELD_NAME = jb_drct_rechg_updt_field_9
			FIELD_NEXT_FIELD = jb_drct_rechg_updt_field_10
			FIELD_LEAF = YES
			FIELD_XDRKEY = RESERVED2

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	//RESERVED
		{
			FIELD_NAME = jb_drct_rechg_updt_field_10
			FIELD_NEXT_FIELD = NULL
			FIELD_LEAF = YES
			DECODE
			{
				DECODE_FUNC_TYPE = decode_ignore_onerecord
			}
		}
	}
}