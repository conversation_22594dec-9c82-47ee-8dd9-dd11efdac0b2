﻿#ifndef __ODAC_CLIENT_H__
#define __ODAC_CLIENT_H__
#include <string.h>
#include "../../basenpquery/qryproxy.h"
#include "../../basenpquery/np_manager_client.h"
#include "xload_business.h"
#include "xquery_business.h"
#include "analysis_comm_global.h"

#define ODAC_BEGIN_NAMESPACE_DECLARE    namespace settle::ANALYSE_GROUP{
#define ODAC_END_NAMESPACE_DECLARE } \
using namespace settle::ANALYSE_GROUP;

ODAC_BEGIN_NAMESPACE_DECLARE

class CODACClient
{
public:
    CODACClient();
    ~CODACClient();
    
    /*  \功能：
     *        连接ODAC.
     *  \参数： 
     *        pszShmKey IPC_KEY
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */
    //int connect(const char *pszShmKey);
    
    /*  \功能：
     *        断开同ODAC的连接.
     *  \参数： 
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */
    //int disconnect();
    
    /*  \功能：
     *        锁定句柄.
     *  \参数： 
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */
    //int lock();
    /*  \功能：
     *        释放句柄.
     *  \参数： 
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */
    //int unlock();
    
    /*  \功能：
     *        读取当前错误信息.
     *  \参数： 
     *  \返回值：
     *        当前错误信息
     */
//    const string& getErrMsg();
    
    /*  \功能：
     *        根据MscId读取交换机信息.
     *  \参数： 
     *        pMscId      交换机编码
     *        cGsmMscData 交换机信息
     *        pDate       日期 
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */
    int findGsmMsc(const xc::CSnapshot& cSnapShot,
                   const char *pMscId,
                   CBpsGsmMsc &cGsmMscData,
                   const time_t &tm);
          
    /*  \功能：
     *        查询号码的服务类型.
     *  \参数： 
     *        iServiceId   服务ID
     *        pCenterArea  业务归属地
     *        pDigitalArea 业务开放地
     *        pNumber      号码
     *        cSpecialNumberData 号码服务类型信息
     *        pDate        日期 
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */         
    int findSpecialNumber(const xc::CSnapshot& cSnapShot,
                          int                iServiceId,
                          const char        *pCenterArea,
                          const char        *pDigitalArea,
                          const char        *pNumber,
                          CBpsSpecialNumber &cSpecialNumberData,
                          const time_t      &tm);
    int strictfindSpecialNumber(const xc::CSnapshot &cSnapShot,
                                int                iServiceId,
                                const char        *pCenterArea,
                                const char        *pDigitalArea,
                                const char        *pNumber,
                                CBpsSpecialNumber &cSpecialNumberData,
                                const time_t      &tm);
    /*  \功能：
     *        根据地市代码查询地市信息.
     *  \参数： 
     *        pAreaCode 地市代码
     *        CSysCity  地市信息
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */                      
    int findCityByAreaCode(const xc::CSnapshot &cSnapShot,
                           const char *pAreaCode,
                           CSysCity   &cCityData,
                           const time_t &tm);
      
    /*  \功能：
     *        根据省代码查询省信息.
     *  \参数： 
     *        pAreaCode 省代码
     *        cProvData 省信息
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */                      
    int findProv(const xc::CSnapshot &cSnapShot,
                 int       iProvCode,
                 CSysProv &cProvData,
                 const time_t &tm);
         
    /*  \功能：
     *        根据号码分析接入号.
     *  \参数： 
     *        pNumber       号码
     *        cAccessNumber 接入号信息
     *        pDate         日期
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */          
    int findAccessNumber(const xc::CSnapshot &cSnapShot,
                         const char       *pNumber,
                         CBpsAccessNumber &cAccessNumber,
                         const time_t        &tm);
       
    /*  \功能：
     *        根据号码分析国家信息.
     *  \参数： 
     *        pNumber       号码
     *        cAccessNumber 接入号信息
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */                   
    int findCountry(const xc::CSnapshot &cSnapShot,
                    const char  *pNumber,
                    CSysCountry &cCountryData,
                    const time_t        &tm);
                    
    /*  \功能：
     *        判断是否是手机号码.
     *  \参数： 
     *        pNumber        号码
     *        cMobileSegData 手机号头信息
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */                 
    int findMobileSeg(const xc::CSnapshot &cSnapShot,
                      const char       *pNumber,
                      CBpsAddMobileSeg &cMobileSegData,
                      const time_t        &tm);
        
    /*  \功能：
     *        分析hlr信息.
     *  \参数： 
     *        pNumber        号码
     *        cHlrData       hlr信息
     *        pDate          日期
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */                
    int findHlr(const xc::CSnapshot &cSnapShot,
                const char *pNumber,
                CBpsHlr    &cHlrData,
                const time_t &tm);
             
    /*  \功能：
     *        分析漫游号.
     *  \参数： 
     *        pNumber        号码
     *        cMmmCode       漫游号信息
     *        pDate          日期
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */        
    int findMmmCode(const xc::CSnapshot &cSnapShot,
                    const char     *pNumber,
                    CBpsAddMmmCode &cMmmCode,
                    const time_t        &tm);
               
    /*  \功能：
     *        分析固话信息.
     *  \参数： 
     *        pAreaCode       地区
     *        pNumber         号码
     *        cPstnNumsegData 固话信息
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */      
    int findPstnNumSeg(const xc::CSnapshot &cSnapShot,
                       const char     *pAreaCode,
                       const char     *pNumber,
                       CBpsPstnNumseg &cPstnNumsegData,
                       const time_t &tm);
                 
    /*  \功能：
     *        得到同一地区的两营业区间的关系.
     *  \参数： 
     *        pAreaCode            地区
     *        pBusiAreaCode1       地区1
     *        pBusiAreaCode2       地区2
     *        cBusinessAreaRelData 两营业区间的关系
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */       
    int findBusinessAreaRel(const xc::CSnapshot &cSnapShot,
                            const char          *pAreaCode,
                            const char          *pBusiAreaCode1,
                            const char          *pBusiAreaCode2,
                            CBpsBusinessAreaRel &cBusinessAreaRelData,
                            const time_t &tm);
                            
    /*  \功能：
     *        分析交换机信息.
     *  \参数： 
     *        pSwitchId       交换机代码
     *        cPstnSwitchData 中继信息
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */  
    int findPstnSwitch(const xc::CSnapshot &cSnapShot,
                       const char     *pSwitchId,
                       CBpsPstnSwitch &cPstnSwitchData,
                       const time_t &tm);
        
    /*  \功能：
     *        分析中继信息.
     *  \参数： 
     *        pSwitchId       交换机代码
     *        pTrunkId        中继代码
     *        cPstnRouterData 中继信息
     *        pDate           日期
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */               
    int findPstnRouter(const xc::CSnapshot &cSnapShot,
                       const char      *pSwitchId,
                       const char      *pTrunkId,
                       CBpsPstnRouter &cPstnRouterData,
                       const time_t        &tm);
                  
    /*  \功能：
     *        分析固话号段.
     *  \参数： 
     *        pAreaCode        地区
     *        pNumber          号码
     *        pDate            日期
     *        cSpecialUserData 号段信息
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */        
    int findSpecialUser(const xc::CSnapshot &cSnapShot,
                        const char      *pAreaCode,
                        const char      *pNumber,
                        CBpsSpecialUser &cSpecialUserData,
                        const time_t &tm);
                        
    int findSpecialUserByBureauCode(const xc::CSnapshot &cSnapShot,
                                    const char      *pAreaCode,
                                    const char      *pBureauCode,
                                    const char      *pNumber,
                                    CBpsSpecialNet  &cSpecialNetData,
                                    const time_t &tm);
                        
    /*  \功能：
     *        分析Misn.
     *  \参数： 
     *        pNumber          Misn
     *        cMisnNumsegData  Misn信息
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */                            
    int findMisnNumSeg(const xc::CSnapshot &cSnapShot,
                       const char     *pNumber,
                       CBpsMisnNumseg &cMisnNumsegData,
                       const time_t &tm);
            
    /*  \功能：
     *        分析ccm信息.
     *  \参数： 
     *        pCcmIp          CcmIp
     *        pSacpIp         SacpIp
     *        CcmData         ccm信息
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */              
    /*int findCCM(const xc::CSnapshot &cSnapShot,
                const char *pCcmIp,
                const char *pSacpIp,
                CBpsCcm    &cCcmData);*/

    /*  \功能：
     *        分析ChannelType.
     *  \参数： 
     *  \返回值：
     *        0   执行成功
     *       <0   执行失败
     */               
    int findChannelType(const xc::CSnapshot &cSnapShot,
                        const int        iService,
                        const int        iBandwidth,
                        const char      *pCodec,
                        const int        iDir,
                        CBpsChannelType &cChannelType,
                        const time_t &tm);
                      
                        
    int findRateType( const xc::CSnapshot &cSnapShot,
                      const char *pszAccessNumber,
                      const char *pszOriArea,
                      const char *pszTermArea,
                      const int   iTollGroup,
                      int&        iRateType,      //warning return value;
                      int&        iTollType,
                      const time_t &tm);
                      
    int findCarrierImsi(const xc::CSnapshot &cSnapShot,
                        const char      *pszImsi,
                        CBpsCarrierImsi &cCarrierImsiData,
                        const time_t &tm);
        
    int findGsmRouter(const xc::CSnapshot &cSnapShot,
                      const char    *pszMscId,
                      const char    *pszTrunkId,
                      CBpsGsmRouter &cBpsGsmRouter,
                      const time_t  &tm);    
    int findGsmRouterByAreaCode(const xc::CSnapshot &cSnapShot,
                                const char    *pszMscId,
                                const char    *pszTrunkId,
                                const char    *pszAreaCode,
                                CBpsGsmRouter &cBpsGsmRouter,
                                const time_t &tm);
    int findGsmRouterByTrunkFlag(const xc::CSnapshot &cSnapShot,
                                const char    *pszMscId,
                                 const char    *pszTrunkId,
                                 const char    *pszTrunkFlag,
                                 const char    *pszAreaCode,
                                 CBpsGsmRouter &cBpsGsmRouter,
                                 const time_t &tm);
    int findCardPrefix(const xc::CSnapshot &cSnapShot,
                                const char     *pszCardPrefix,
                                CBpsCardPrefix &cCardPrefixData,
                                const time_t &tm);
                                 
    int findCardPrefixByCardType( const xc::CSnapshot &cSnapShot,
                                  const char     *pszCardPrefix,
                                  const char     *pszCardCode,
                                  CBpsCardPrefix &cCardPrefixData,
                                  const time_t &tm);
    
    int findSpBusiDescByServCode2( const xc::CSnapshot &cSnapShot,
                                   const char        *pszServCode2,
                                   const int          iBusiType,
                                   CBpsAddSpBusiDesc &cSpBusiDescData,
                                   const time_t& tm);
    int findSpBusiDescByServCode2Ex( const xc::CSnapshot &cSnapShot,
                                     const char        *pszServCode2,
                                     const char        *pszAccArea,
                                     CBpsAddSpBusiDesc &cSpBusiDescData,
                                     const time_t& tm);
    int findSpBusiDescBySpCode( const xc::CSnapshot &cSnapShot,
                                const char        *pszSpCode,
                                const int          iBusiType,
                                CBpsAddSpBusiDesc &cSpBusiDescData,
                                const time_t& tm);
    int findSpBusiDescByBusiCode(const xc::CSnapshot &cSnapShot,
                                const char        *pszBusiCode,
                                CBpsAddSpBusiDesc &cSpBusiDescData,
                                const time_t& tm);
  int findPlatformBusiDesc( const xc::CSnapshot &cSnapShot,
                            const int                iAccCode,
                            const char              *pszOperCode,
                            CBpsAddPlatformBusiDesc &cPlatformBusiDesc,
                            const time_t &tm);
  int findOperator(const xc::CSnapshot &cSnapShot,
                   const char      *pszOperCode,
                   const char      *pszHomeArea,
                   CBpsAddOperator &cOperatorData,
                   const time_t &tm);
  int findOperatorByOperType(const xc::CSnapshot &cSnapShot,
                             const char      *pszOperCode,
                             const int        iOperType,
                             CBpsAddOperator &cOperatorData,
                             const time_t &tm);
  int findIsmg(const xc::CSnapshot &cSnapShot,
               const char *pszIsmgId,
               CBpsIsmg   &cIsmgData,
               const time_t &tm);
        
  int findHlrByImsi(const xc::CSnapshot &cSnapShot,
                          const char *pszImsiCode,
                          char *pszHlrCode,
                    const time_t& tm);
  int findSpSms(const xc::CSnapshot &cSnapShot,
                const char   *pszServCode,
                CBpsAddSpSms &cSpSms,
                const time_t &tm);
        
  int findMscByFileName(const xc::CSnapshot &cSnapShot,
                        const char           *pszFileName,
                        CBpsAddFilenametomsc &cFilenametomsc,
                        const time_t &tm);
  int findCardFeeCodeSeg(const xc::CSnapshot &cSnapShot,
                         const char            *pszCardNo,
                         CBpsAddCardFeeCodeSeg &cCardFeeCodeSeg,
                         const time_t &tm);
  int findGprsIpaddrInfo(const xc::CSnapshot &cSnapShot,
                         const char *pszProvCode,
                         const char *pszIpAddr,
                         const time_t& tm);
  int findKoreaRoamMsisdn(const xc::CSnapshot &cSnapShot,
                          const char          *pszRoamMsisdn,
                          CBpsKoreaRoamMsisdn &cKoreaRoamMsisdnData,
                          const time_t &tm);
  int findHlrTrademark(const xc::CSnapshot &cSnapShot,const char *pszHlrCode,const time_t &tm);
  int findSpUserInfo(const xc::CSnapshot &cSnapShot,
                     const char   *pszSpCode,
                     const char   *pszOperCode,
                     const char   *pszMsisdn,
                     CVSpUserInfo &cSpUserInfo,
                     const time_t& tm);
  int findSpProd(const xc::CSnapshot &cSnapShot,
                 const char    *pszBillingMonth,
                 CBpsAddSpProd &cAddSpProdData,
                 const time_t &tm);
  int findIrUserLimit(const xc::CSnapshot &cSnapShot,
                      const char      *pszPartnerId,
                      const char      *pszLimitType,
                      CBpsIrUserLimit &cIrUserLimit,
                      const time_t &tm);
    int findLacAreaRel(const xc::CSnapshot &cSnapShot,
                           const char      *pszLacId,
                       CVBpsLacAreaRel &cLacAreaRelData,
                       const time_t& tm);
    int findCiContentData(const xc::CSnapshot &cSnapShot,
                          const char *pszContentID,
                          CBpsCiContentData &cCiContentData,
                          const time_t& tm);
    int findFreeGsmRouter(const xc::CSnapshot &cSnapShot,
                          const char        *pszMscId,
                          const char        *pszTrunkId,
                          CBpsFreeGsmRouter &cFreeGsmRouter,
                          const time_t& tm);
    int findOperatorDesc(const xc::CSnapshot& cSnapShot,
                                  const int            iDrType,
                                  const int            iOperId,
                                  CBpsAddOperatorDescription &cOperatorDescData,
                                  const time_t& tm);
    int findMscCodeByTempMsc(const xc::CSnapshot &cSnapShot,
                             const char  *pszTrunkGroup,
                             const char  *pszTempMscCode,
                             CBpsMscCode &cMscCode,
                             const time_t &tm);
    int findLac(const xc::CSnapshot &cSnapShot,
                const char *pszLac,
                CBpsLac    &cLac,
                const time_t& tm);
    int findSpRatio(const xc::CSnapshot &cSnapShot,
                    const char *pszSpCode,
                    const char *pszOperatorCode,
                    const int   nType,
                    CVSpRatio  &cSpRatioData,
                    const time_t& tm);
    int findAccCodeBySpOperCode(const xc::CSnapshot &cSnapShot,
                                const char     *pszSpCode,
                                const char     *pszOperCode,
                                CBpsAcccodeRel &cAcccodeRel,
                                const time_t &tm);
                                
    int findBorderRoam(const xc::CSnapshot &cSnapShot,
                        const char        *pszMscID,
                        const char        *pszLacID,
                        const char        *pszCellID,
                        const char        *pszAreaCode,
                        CBpsBorderRoam &cBpsBorderRoam,
                        const time_t& tm);
    //zhoushang add
    int findBsmsServCode(const xc::CSnapshot &cSnapShot,
                const char *pszServType,
                const char        *pszServCode,
                const int32 iMacthType,
                CBpsBsmsServicecode &cBsmsServCodeData,
                const time_t &tm);

    //wanghy3 add  20161016
    //分析融创短信（serv_code）信息
int findRcdx(const xc::CSnapshot &cSnapShot,
                          const char *pszServiceCode,
                          CVBpsRcdxUserRel &cRcdxData,
                          const time_t &tm);
/*
    int NPUserAnalysis(const xc::CSnapshot &cSnapShot,
                     const char *pszUserNumber,
                     int        &nOperId,
                     int        &nNetType,
                     char       *pszAreaCode,
                     int        &nPortOutOperId,
                     int        &nHomeOperId,
                     const time_t &tm);

    //add by zhufd
    int NPUserAnalysisNew(const xc::CSnapshot &cSnapShot,
                     const char *pszUserNumber,
                     int        &nOperId,
                     int        &nNetType,
                     char       *pszAreaCode,
                     int        &nPortOutOperId,
                     int        &nHomeOperId,
                     const time_t &tm);
                     */
    int NPUserAnalysisWithTime(const char *pszUserNumber,
                     const char *pDate,
                     int        &nOperId,
                     int        &nNetType,
                     char       *pszAreaCode,
                     int        &nPortOutOperId,
                     int        &nHomeOperId,
                     const time_t &tm);
       //wanghy3 add 20161115
       int findCeilCountyRel(const xc::CSnapshot &cSnapShot,
                           const char  *pszLacId, const char  *pszCeilId,
                           CVBpsLacCeilCountyRel &cLacCeilCountyRelData,const time_t &tm);
    //added by gai 20200720
    int findIvrRatio(const xc::CSnapshot &cSnapShot,const char *pszOper,const char*pszSpCode,const char *pszServ,const int32 nfee,const int32 nKey, CVIvrRatio &cIvrRatio, const time_t &tm);
    int findBpsCardHomeProv(const xc::CSnapshot &cSnapShot,const char *szCardCode,CBpsCardHomeProv &cBpsCardHomeProv,const time_t &tm);
    int findBpsImsiOperInfo(const xc::CSnapshot &cSnapShot,const char* szImsiOperInfo, CBpsImsiOperInfo &cBpsImsiOperInfo,const time_t &tm);
    int findBpsImsiNumber(const xc::CSnapshot &cSnapShot,const char *pszImsi,CBpsImsiNumber &cBpsImsiNumber,const time_t &tm);
    int findCityByRegionCode(const xc::CSnapshot &cSnapShot,
                                      const char *cRegionCode,
                                      CSysCity   &cCityData,
                                      const time_t &tm);
    int findProvByAreaCode(const xc::CSnapshot &cSnapShot,
                           const char *pAreaCode,
                           CSysProv &cProvData,
                           const time_t &tm);
    int findCBpsMgCampInfoByCampaignId(const xc::CSnapshot &cSnapShot,
                                     const char *cszCampaignId,
                                     CBpsMgCampInfo &cBpsMgCampInfo,
                                     const time_t &tm);
    int findSpBusiDescByServCode2Only(const xc::CSnapshot &cSnapShot,
                                      const char *pszServCode2,
                                      CBpsAddSpBusiDesc &cSpBusiDescData,
                                      const time_t &tm);
    int FindNpInfo(const char *szNumber,const char *szDate,SNpInfo &p_pNpInfo);
    //拆分ip与port
    int SplitIpPort(const char *szPort,const char *szIp);

    int FindVBpsIpv4Address(const xc::CSnapshot &cSnapShot, const char* address, const time_t &tm, CVBpsIpv4Address &cVBpsIpv4Address);
    int FindBpsIpv4AddressRange(const xc::CSnapshot &cSnapShot, const char* address, const time_t &tm, CBpsIpv4AddressRange &cBpsIpv4AddressRange);
    int FindBpsIpv6AddressRange(const xc::CSnapshot &cSnapShot, const char* address, const time_t &tm, CBpsIpv6AddressRange &cBpsIpv6AddressRange);

    int findBpsSjDefaultPrice(const xc::CSnapshot &cSnapShot, const char *pszSettlementType, CBpsSjSmsDefaultPrice &cBpsSjSmsDefaultPriceData, const time_t &tm);
private:
    int parseHlrCode(const char *pszImsiCode, char *pszHlrCode);
    ::qryproxy::CQryProxy m_qryProxy;
    bool qryProxyInitFlag =false;
    ::MBasenpquery::SProxyList m_versSockStr;
};

ODAC_END_NAMESPACE_DECLARE

#endif // __ODAC_CLIENT_H__
