﻿#ifndef __EXPR_COMM_ANALYSE_FUNC_H__
#define __EXPR_COMM_ANALYSE_FUNC_H__
#include "analysis_comm_global.h"
//#include "analysis_global.h"
#include "xquery_business.h"

#include "impl_common.h"
#include "../rpl/rpl_initglobal.h"
#include "implement_base_define.h"
#include "wbass_analysis_public.h"
#include "wbass_analysis_zj.h"

#ifdef __cplusplus
extern "C" {
#endif

class CRplEngineMgr;
class CRplDataMgr;

int32 RegCommAnalyse(aiexpress_interface* pEi);

#ifdef __cplusplus
};
#endif

/***********************
***lua调用函数枚举******
***********************/

int32    LogAppend_lua(lua_State *L);

int32    SMSNumberAnalysis_lua(lua_State *L);//SMSNumberAnalyzeNew

int32     NumberAnalysis_lua(lua_State *L);//NumberAnalysisNew

int32    GetLongType_lua(lua_State *L);

int32    GetRoamType_lua(lua_State *L);

int32    GsmSwitchInfo_lua(lua_State *L);//GsmSwitchInfoNew

int32    GsmRouterInfo_lua(lua_State *L);//GsmRouterInfo

int32    GsmRouterInfoNew_lua(lua_State *L);//GsmRouterInfoNew

int32    GetRoamAreaCode_lua(lua_State *L);//GetRoamAreaCodeNew

int32    SPNumberAnalyze_lua(lua_State *L);//SPNumberAnalyze

int32    SPNumberSpAnalyze_lua(lua_State *L);//SPNumberSpAnalyze

int32    SPNumberServCodeAnalyze_lua(lua_State *L);//SPNumberServCodeAnalyze

int32    BsmsByServCode_lua(lua_State *L);//BsmsByServCode

int32    AccCodeAnalyze_lua(lua_State *L);//AccCodeAnalyze

int32    OperCodeAnalyze_lua(lua_State *L);//OperCodeAnalyze

int32    OperCodeMCCAnalyze_lua(lua_State *L);//OperCodeMCCAnalyze

int32    IsmgCodeAnalyze_lua(lua_State *L);//IsmgCodeAnalyze

int32    VisitAreaAnalyze_lua(lua_State *L);//VisitAreaAnalyze

int32    SpecialUserAnalyze_lua(lua_State *L);//SpecialUserAnalyze

int32    SpecialUserAnalyzeSN_lua(lua_State *L);//SpecialUserAnalyzeSN

int32    ImsiAreaCodeAnalysis_lua(lua_State *L);//ImsiAreaCodeAnalysisNew

int32    FindSpRatio_lua(lua_State *L);//FindSpRatio

int32    GetThirdRatioInfo_lua(lua_State *L);//GetThirdRatioInfo

int32    GetMscIdFromFileName_lua(lua_State *L);//GetMscIdFromFileName

int32    FindGprsIpaddr_lua(lua_State *L);//FindGprsIpaddr

int32    GetProvByAreaCode_lua(lua_State *L);//GetProvByAreaCode

int32    FindNpInfo_lua(lua_State *L);//FindNpInfo

int32    IvrRatio_lua(lua_State *L);//IvrRatio

int32    ImsiOperAnalysis_lua(lua_State *L);//ImsiOperAnalysis

/*zj-prov special*/
int32 GetSmsNewsAccFee_lua(lua_State *L);

int32 GetSpProdFeeInfo_lua(lua_State *L);

int32 GetFreeFeeWoffType_lua(lua_State *L);

int32 GetUserTradeMark_lua(lua_State *L);

int32 RCSMSNumberAnalyze_lua(lua_State *L);

int32 getAreaCodeByCeil_lua(lua_State *L);

int32 getUserLocation_lua(lua_State *L);

int32 getGridIdByUser_lua(lua_State *L);

int32 getGridIdByLacCeil_lua(lua_State *L);

int32 getCardHomeProv_lua(lua_State *L);

int32 getDateFromFileName_lua(lua_State *L);

int32 IpAddressAnalysis_lua(lua_State *L);

int32 GetSjDefaultPriceInfo_lua(lua_State *L);

/***********************
***LUA调用C实现函数*****
***********************/
time_t str2time (const AISTD string& dt);
char* ltoa(long n);
time_t StringToDatetime(std::string str);

int SMSNumberAnalysisNew(xc::CSnapshot & snapShot, 
                        const char    *p_pchNumber,
                        const char    *p_pArea,
                        const int    iStartTime,
                        SMSNumberInfo *p_pSNumber);

#endif

