require "dbfetcher_util"

function main(args)
    -- 获取数据库连接
    local conn = getConnect()
    -- 获取任务 ID
    local inTaskId = getCode(args, "TASK_ID")
    local inDay = getCode(args, "DAY")

    writeLog("开始处理"..inDay.."日的数据")

    -- 获取 BILL_MONTH 参数
    billMonth = getBillMonth(conn, inTaskId)
    
    -- 如果获取不到，返回错误
    if not billMonth then
        writeLog("未提供 BILL_MONTH 参数")
        return -1
    end

    -- 实际结算月份等于出账月份前两月，取YYYYMM
    settleMonth = string.sub(getAdjustDate(billMonth, -2), 1, 6)  

    writeLog("BILL_MONTH:" .. billMonth .. "  实际结算月份:" .. settleMonth)

    -- 拼接分表日期后缀 需要保证日期是两位不足补0 这个由sys_process_param_info表args_cmd保证
    local tableSuffix = settleMonth .. inDay

    -- 获取当前月份（两位数）
    local currentMonth = os.date("%m")
    
    -- 获取当前时间完整格式
    local currentTime = os.date("%Y%m%d%H%M%S")

    -- 获取当月最后一天
	local strSql = "select to_char(last_day(to_date(" .. settleMonth .. ",'YYYYMM')),'DD') from dual";
	local cursor = assert(conn:execute(strSql));
	local lastDay = cursor:fetch();
	cursor:close();

    if tonumber(lastDay) >= tonumber(inDay) then
        -- 插入MG业务05类型数据 咪咕短剧业务
        local strSql = [[INSERT INTO jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[
            (file_name, day_id, month_id, record_type, busi_type, audit_type,
            seq_id, msisdn, sp_code, oper_code, property_code, channel_code, 
            charge_type, use_time, audit_time, audit_fee, cdr_type, content_id,
            order_id, transaction_id, charge_seq, cdr_seq, reserved, out_flag, mrkt_ctvty_id)
            SELECT  'zxh-mgyx-dj',
                    ']] .. settleMonth .. [[01',
                    ']] .. currentMonth .. [[',
                    '20',
                    'MIGU',
                    '05',
                    substr(a.start_time,1,8)||LPAD(rownum,6,0),
                    a.user_number,
                    a.sp_code,
                    a.oper_code,
                    '',
                    a.channel_id,
                    '03',
                    a.finish_time,
                    ']] .. currentTime .. [[',
                    sum(a.month_fee),
                    '02',
                    '',
                    '',
                    '',
                    '',
                    a.msg_id,
                    '',
                    '0',
                    ''
            FROM jsdr.dr_sj_sp_]] .. tableSuffix .. [[ a
            WHERE EXISTS ( SELECT 1 from (select b.SETTLE_MONTH,b.BIZ_CODE,b.BIZ_NAME,b.BIZ_TYPE,b.SP_CODE,c.serial_number 
                                            from jsbd.bps_user_platsvc_stash b
                                            join jsbd.bps_tf_f_user_]] .. settleMonth ..[[ c 
                                            on b.settle_month = ]] .. settleMonth .. [[ and b.user_id = c.user_id) d
                            where d.biz_code = a.oper_code and d.sp_code = a.sp_code and a.user_number = d.serial_number)
            GROUP BY 
                substr(a.start_time,1,8)||LPAD(rownum,6,0),
                a.user_number,
                a.sp_code,
                a.oper_code,
                a.channel_id,
                a.charge_type,
                a.finish_time,
                a.msg_id
        ]]

        -- 执行SQL语句
        writeLog("执行咪咕短剧业务插入SQL")
        executeSQL(conn, strSql)
        conn:commit()
    else 
        writeLog("当前月份: " .. settleMonth .. ", 当前日期最后一天: " .. lastDay .. ", 当前inday: " .. inDay .. ", 不处理业务数据")
    end
    ------------------------------------------------------
    writeLog("处理完成")
    conn:close()
    return 0
end

environment = os.getenv("ENVIRONMENT")
if environment == "TEST" then
    main("TASK_ID=17990|DAY=01")
    os.exit()
end
