#!/bin/python
# -*- coding: utf-8 -*-
from cmi_common_util import get_instr_from_list, get_gs_connect, get_o_connect, process_leftjoin
from decimal import Decimal 
import logging
import os
import time
from datetime import datetime,timedelta
import sys
import pdb
import traceback
import cx_Oracle
import psycopg2

# 下面两个参数，可以批量测试时，看哪个耗时更少，生产就用哪个
updateBySqlFull = True   # 全阶资费记录更新：用SQL全量更新，还是通过循环查找部分记录更新
sumSheetCntBySql = False # 全阶资费汇总记录数用SQL查询，还是通过循环累加
calcCtledByTime = True  # 是否控制阶梯资费计算的时间(月末和当天才计算)
max_task_running = 8    # 最大同时运行任务数
get_task_num = 1        # 重批任务触发汇总时, 每次取几个任务, 建议1个

#LOG_LEVEL=logging.DEBUG  # 日志级别
LOG_LEVEL=logging.INFO    # 日志级别

# 字段在汇总记录查询结果中的索引
fieldIndex = {
    'in_rate2': 11,
    'out_rate2': 20,
    'in_charge_2': 31,
    'out_charge_2': 36,
    'finish_time': 25,
    'sheet_cnt': 26,
    'in_acc_settle_id': 29,
    'out_acc_settle_id': 34,
    'in_op_charge': 64,
    'out_op_charge': 65,
    'in_option_rate': 66,
    'out_option_rate': 67,
    'in_account_id': 68,
    'out_account_id': 69,
    'ctid': 70
}

def isLastDayOfMonth(strDay):
    # 将字符串日期转换为 datetime 对象
    date = datetime.strptime(strDay, '%Y%m%d')
    
    # 获取下一天的日期
    next_day = date + timedelta(days=1)
    
    # 如果下一天的月份不同，说明当前日期是月末最后一天
    return next_day.month != date.month

def isToday(strDay):
    # 将字符串日期转换为 datetime 对象
    date = datetime.strptime(strDay, '%Y%m%d')
    # 获取当前日期
    today = datetime.today().date()
    # 比较日期
    return date.date() == today

# 加载收入阶梯资费
def loadInTierRate(dbConn, workDay, strAcctId="-1", strDirFlag="1"):
    dictRate = {}
    uniqueIdSet = set()
    strWorkMonth = workDay[0:6]  # 加载整月有效的资费

    if strAcctId == "-1":  # 全量加载
        # V_BSR_A2P_CUSTOMER_ALL只有180天内有效的数据，所以用V_BSR_A2P_CUSTOMER_SWAP
        sqlRate = '''
            select /*+set(query_dop 8) */ product_id,currency,settle_rate, option_rate, start_val,end_val,destination,destination2,item_id,segment_id,unique_id,charge_mode,eff_date,exp_date
            from V_BSR_A2P_CUSTOMER_SWAP where charge_mode in (10, 20) and 
            substr(eff_date,1,6) <= '{0}' and substr(exp_date,1,6)  >= '{0}'
            order by unique_id, product_id, item_id, segment_id
        '''.format(strWorkMonth)
    else:   #按账户加载
        if strDirFlag == '1':    # 成本方向的重批，直接返回空
            return dictRate, uniqueIdSet
        else:
            sqlRate = '''
                select /*+set(query_dop 8) */ product_id,currency,settle_rate, option_rate, start_val,end_val,destination,destination2,item_id,segment_id,unique_id,charge_mode,eff_date,exp_date
                from V_BSR_A2P_CUSTOMER_SWAP where charge_mode in (10, 20) and 
                substr(eff_date,1,6) <= '{0}' and substr(exp_date,1,6)  >= '{0}' and unique_id in {1}
                order by unique_id, product_id, item_id, segment_id
                '''.format(strWorkMonth, strAcctId)

    # print("loadInTierRate sqlRate:", sqlRate.format(strWorkMonth))
    
    cur = dbConn.cursor()
    cur.execute(sqlRate)
    rows = cur.fetchall()

    if rows is not None:
        for oriRow in rows:
            # 删除Decimal
            row = tuple(  
                str(value) if isinstance(value, Decimal) else value   
                for value in oriRow  
            )
            
            rateInfo = {}
            productId = int(row[0])
            rateInfo['product_id'] = productId
            rateInfo['currency'] = row[1]
            rateInfo['settle_rate'] = int(row[2])
            rateInfo['option_rate'] = int(row[3])
            rateInfo['start_val'] = int(row[4])
            rateInfo['end_val'] = int(row[5])
            rateInfo['destination'] = row[6]
            rateInfo['destination2'] = row[7]
            rateInfo['item_id'] = int(row[8])
            rateInfo['segment_id'] = int(row[9])
            rateInfo['unique_id'] = row[10]
            rateInfo['charge_mode'] = int(row[11])
            rateInfo['eff_date'] = row[12]
            rateInfo['exp_date'] = row[13]
            uniqueId = row[10]
            uniqueIdSet.add(uniqueId)
            key = str(uniqueId) + "|" + str(rateInfo['item_id'])  # key=unique_id|科目ID
            if key not in dictRate:
                dictRate[key] = []
            dictRate[key].append(rateInfo)

    cur.close()

    return dictRate, uniqueIdSet

# 加载支出(成本)阶梯资费
def loadOutTierRate(dbConn, workDay, strAcctId="-1", strDirFlag="1"):
    dictRate = {}
    uniqueIdSet = set()
    strWorkMonth = workDay[0:6]  # 加载整月有效的资费

    if strAcctId == "-1": # 全量加载
        sqlRate = '''
            select /*+set(query_dop 8) */ product_id,currency,settle_rate, option_rate, start_val,end_val,destination,segment_id,unique_id,charge_mode,eff_date,exp_date
            from V_BSR_A2P_PROVIDER_SWAP where charge_mode in (10, 20) and 
            substr(eff_date,1,6) <= '{0}' and substr(exp_date,1,6)  >= '{0}'
            order by unique_id, product_id, segment_id
        '''.format(strWorkMonth)
    else:   #  指定账户加载
        if strDirFlag == "1":
            sqlRate = '''
                select /*+set(query_dop 8) */ product_id,currency,settle_rate, option_rate, start_val,end_val,destination,segment_id,unique_id,charge_mode,eff_date,exp_date
                from V_BSR_A2P_PROVIDER_SWAP where charge_mode in (10, 20) and 
                substr(eff_date,1,6) <= '{0}' and substr(exp_date,1,6)  >= '{0}' and unique_id in {1}
                order by unique_id, product_id, segment_id
            '''.format(strWorkMonth, strAcctId)
        else:   # 方向不一致，直接返回
            return  dictRate, uniqueIdSet
    # print("loadOutTierRate sqlRate:", sqlRate.format(strWorkMonth))
    
    cur = dbConn.cursor()
    cur.execute(sqlRate)
    rows = cur.fetchall()

    if rows is not None:
        for oriRow in rows:
            # 删除Decimal
            row = tuple(  
                str(value) if isinstance(value, Decimal) else value   
                for value in oriRow  
            )
            rateInfo = {}
            productId = int(row[0])
            rateInfo['product_id'] = productId
            rateInfo['currency'] = row[1]
            rateInfo['settle_rate'] = int(row[2])
            rateInfo['option_rate'] = int(row[3])
            rateInfo['start_val'] = int(row[4])
            rateInfo['end_val'] = int(row[5])
            rateInfo['destination'] = row[6]
            rateInfo['segment_id'] = int(row[7])
            rateInfo['unique_id'] = row[8]
            rateInfo['charge_mode'] = int(row[9])
            rateInfo['eff_date'] = row[10]
            rateInfo['exp_date'] = row[11]
            uniqueId = row[8]
            uniqueIdSet.add(uniqueId)
            key = str(uniqueId) + "|" + str(productId)  # key=unique_id|产品ID
            if key not in dictRate:
                dictRate[key] = []
            dictRate[key].append(rateInfo)

    cur.close()

    return dictRate, uniqueIdSet
# 加载阶梯资费
def loadTierRate(dbConn, workDay, strAcctId="-1", strDirFlag="1"):
    dictInRate, inUniqueIdSet = loadInTierRate(dbConn, workDay, strAcctId, strDirFlag)
    dictOutRate, outUniqueIdSet = loadOutTierRate(dbConn, workDay, strAcctId, strDirFlag)
    # print("loadTierRate dictInRate:", dictInRate)
    # print("loadTierRate dictOutRate:", dictOutRate)
    # print("loadTierRate dictInRate len:", len(dictInRate))
    # print("loadTierRate dictOutRate len:", len(dictOutRate))
    # print("loadTierRate inUniqueIdSet:", inUniqueIdSet)
    # print("loadTierRate outUniqueIdSet:", outUniqueIdSet)
    # print("loadTierRate inUniqueIdSet len:", len(inUniqueIdSet))
    # print("loadTierRate outUniqueIdSet len:", len(outUniqueIdSet))

    return dictInRate, dictOutRate, inUniqueIdSet, outUniqueIdSet

# 计算半阶梯资费
def calc_halfTier(row, rateItem, sheetCntLast, finishTime):
    calcItem = {
        "rate1": 0,  # 第1段资费
        "rate2": 0,  # 第2段资费
        "op_rate1": 0, # 第1段运营资费
        "op_rate2": 0, # 第2段运营资费
        "cal_charge1": 0,  # 第1段费用
        "cal_charge2": 0,  # 第2段费用
        "op_cal_charge1": 0,  # 第1段运营费用
        "op_cal_charge2": 0,  # 第2段运营费用
        "segment_id1": 0,  # 第1段阶梯
        "segment_id2": 0,  # 第2段阶梯
        "sheet_cnt_tier1": 0,  # 第1段条数
        "sheet_cnt_tier2": 0,  # 第2段条数
        "splite_flag": False   # 是否跨阶梯
    }
    rate1 = 0
    rate2 = 0
    opRate1 = 0
    opRate2 = 0
    calCharge1 = 0
    calCharge2 = 0
    opCalCharge1 = 0
    opCalCharge2 = 0
    segmentId1 = 0
    segmentId2 = 0
    sheetCntTier1 = 0
    sheetCntTier2 = 0
    endVal1 = 0
    spliteFlag = False

    sheetCnt = row[fieldIndex["sheet_cnt"]]
    sheetCntSum = sheetCntLast + sheetCnt

    # print("calc_halfTier row:", row)
    # print("calc_halfTier rateItem:", rateItem)
    # print("calc_halfTier sheetCnt:", sheetCnt)
    # print("calc_halfTier sheetCntLast:", sheetCntLast)
    # print("calc_halfTier sheetCntSum:", sheetCntSum)

    # pdb.set_trace()
    sheetCntLast = 1 if sheetCntLast == 0 else sheetCntLast  # 从1开始找资费段
    cdrTime = finishTime+'000000'  # 用finish_time判断资费生失效时间
    for rateInfo in rateItem:
        if cdrTime >= rateInfo['eff_date'] and cdrTime < rateInfo['exp_date']:
            if sheetCntLast >= rateInfo["start_val"] and (-1 == rateInfo['end_val'] or sheetCntLast <= rateInfo['end_val']):
                rate1 = rateInfo["settle_rate"]
                opRate1 = rateInfo["option_rate"]
                segmentId1 = rateInfo["segment_id"]
                endVal1 = rateInfo['end_val']

            if sheetCntSum >= rateInfo["start_val"] and (-1 == rateInfo['end_val'] or sheetCntSum <= rateInfo['end_val']):
                rate2 = rateInfo["settle_rate"]
                opRate2 = rateInfo["option_rate"]
                segmentId2 = rateInfo["segment_id"]
                break
    
    if segmentId1 == segmentId2:  # 同一个阶梯
        sheetCntTier1 = sheetCnt
        calCharge1 = sheetCntTier1 * rate1 
        opCalCharge1 = sheetCntTier1 * opRate1
    else:  # 跨阶梯, 因为汇总表数据粒度较细，不考虑跨多阶梯的情况
        print("calc_halfTier cross tier")
        sheetCntTier2 = sheetCntSum - endVal1   # 第2段的条数
        sheetCntTier1 = sheetCnt - sheetCntTier2   # 第1段的条数

        if sheetCntTier1 == 0: # 全部跨阶梯, 只返回1个费用
            rate1 = rate2
            opRate1 = opRate2
            sheetCntTier1 = sheetCntTier2
            calCharge1 = sheetCntTier1 * rate1 
            opCalCharge1 = sheetCntTier1 * opRate1
        else:  # 部分跨阶梯, 需要返回2个费用，用于记录拆分
            calCharge1 = sheetCntTier1 * rate1 
            calCharge2 = sheetCntTier2 * rate2
            opCalCharge1 = sheetCntTier1 * opRate1
            opCalCharge2 = sheetCntTier2 * opRate2
            spliteFlag = True
            print("calc_halfTier cross tier need splite")
    
    calcItem["rate1"] = rate1
    calcItem["rate2"] = rate2
    calcItem["op_rate1"] = opRate1
    calcItem["op_rate2"] = opRate2
    calcItem["cal_charge1"] = calCharge1
    calcItem["cal_charge2"] = calCharge2
    calcItem["op_cal_charge1"] = opCalCharge1
    calcItem["op_cal_charge2"] = opCalCharge2
    calcItem["segment_id1"] = segmentId1
    calcItem["segment_id2"] = segmentId2
    calcItem["sheet_cnt_tier1"] = sheetCntTier1
    calcItem["sheet_cnt_tier2"] = sheetCntTier2
    calcItem["splite_flag"] = spliteFlag

    print("calc_halfTier return calcItem:", calcItem)
    logging.debug("calc_halfTier return calcItem:{}".format(calcItem))

    return calcItem

# 计算全阶资费
def calc_fullfTier(sheetCntAll, rateItem, finishTime):
    calcItem = {
        "rate1": 0,  # 第1段资费
        "op_rate1": 0, # 第1段运营资费
        "segment_id1": 0  # 第1段阶梯
    }
    rate = 0
    opRate = 0
    segmentId = 0

    # print("calc_fullfTier rateItem:", rateItem)
    # print("calc_fullfTier sheetCntAll:", sheetCntAll)
    # print("calc_fullfTier finishTime:", finishTime)

    # pdb.set_trace()
    sheetCntAll = 1 if sheetCntAll == 0 else sheetCntAll  # 从1开始找资费段
    cdrTime = finishTime+'000000'  # 用finish_time判断资费生失效时间
    for rateInfo in rateItem:
        if cdrTime >= rateInfo['eff_date'] and cdrTime < rateInfo['exp_date']:
            if sheetCntAll >= rateInfo["start_val"] and (-1 == rateInfo['end_val'] or sheetCntAll <= rateInfo['end_val']):
                rate = rateInfo["settle_rate"]
                opRate = rateInfo["option_rate"]
                segmentId = rateInfo["segment_id"]
                break
    calcItem["rate1"] = rate
    calcItem["op_rate1"] = opRate
    calcItem["segment_id1"] = segmentId

    # print("calc_fullTier return calcItem:", calcItem)
    return calcItem

# 跨阶拆分汇总记录
def splite_row(row, calcItem, inOutFlag):
    newRow = list(row)
    firstRow = list(row)
    sheetCnt1 = calcItem["sheet_cnt_tier1"]    # 第1段条数
    sheetCnt2 = calcItem["sheet_cnt_tier2"]    # 第2段条数
    firstRow[fieldIndex["sheet_cnt"]] = sheetCnt1  
    newRow[fieldIndex["sheet_cnt"]] = sheetCnt2  

    if inOutFlag == "in":   # 收入资费跨阶拆分-重置收入费率in_rate2，重算各类费用
        # 根据条数重算新记录的资费（第2段）
        newRow[fieldIndex["in_rate2"]] = calcItem["rate2"]
        newRow[fieldIndex["in_option_rate"]] = calcItem["op_rate2"]

        newRow[fieldIndex["in_charge_2"]] = sheetCnt2 * newRow[fieldIndex["in_rate2"]]
        newRow[fieldIndex["out_charge_2"]] = sheetCnt2 * newRow[fieldIndex["out_rate2"]]
        newRow[fieldIndex["in_op_charge"]] = sheetCnt2 * newRow[fieldIndex["in_option_rate"]]
        newRow[fieldIndex["out_op_charge"]] = sheetCnt2 * newRow[fieldIndex["out_option_rate"]]
        
        # 根据条数重算原记录的资费（第1段）
        firstRow[fieldIndex["in_rate2"]] = calcItem["rate1"]
        firstRow[fieldIndex["in_option_rate"]] = calcItem["op_rate1"]

        firstRow[fieldIndex["in_charge_2"]] = sheetCnt1 * firstRow[fieldIndex["in_rate2"]]
        firstRow[fieldIndex["out_charge_2"]] = sheetCnt1 * firstRow[fieldIndex["out_rate2"]]
        firstRow[fieldIndex["in_op_charge"]] = sheetCnt1 * firstRow[fieldIndex["in_option_rate"]]
        firstRow[fieldIndex["out_op_charge"]] = sheetCnt1 * firstRow[fieldIndex["out_option_rate"]]
 
    elif inOutFlag == "out":  # 成本资费跨阶拆分-重置收入费率out_rate2，重算各类费用
        # 根据条数重算新记录的资费（第2段）
        newRow[fieldIndex["out_rate2"]] = calcItem["rate2"]
        newRow[fieldIndex["out_op_charge"]] = calcItem["op_rate2"]

        newRow[fieldIndex["in_charge_2"]] = sheetCnt2 *  newRow[fieldIndex["in_rate2"]]
        newRow[fieldIndex["out_charge_2"]] = sheetCnt2 * newRow[fieldIndex["out_rate2"]] 
        newRow[fieldIndex["in_op_charge"]] = sheetCnt2 * newRow[fieldIndex["in_option_rate"]]
        newRow[fieldIndex["out_op_charge"]] = sheetCnt2 * newRow[fieldIndex["out_option_rate"]]
        
        # 根据条数重算原记录的资费（第1段）
        firstRow[fieldIndex["out_rate2"]] = calcItem["rate1"]
        firstRow[fieldIndex["out_op_charge"]] = calcItem["op_rate1"]

        firstRow[fieldIndex["in_charge_2"]] = sheetCnt1 * firstRow[fieldIndex["in_rate2"]]
        firstRow[fieldIndex["out_charge_2"]] = sheetCnt1 * firstRow[fieldIndex["out_rate2"]]
        firstRow[fieldIndex["in_op_charge"]] = sheetCnt1 * firstRow[fieldIndex["in_option_rate"]]
        firstRow[fieldIndex["out_op_charge"]] = sheetCnt1 * firstRow[fieldIndex["out_option_rate"]]
    else:
        print("splite_row inOutFlag error:{}".format(inOutFlag))
        logging.info("splite_row inOutFlag error:{}".format(inOutFlag))

    newRow = newRow[:-1]  # 去掉ctid
    print("splite_row return firstRow:{} newRow:{}".format(firstRow, newRow))
    logging.debug("splite_row return firstRow:{} newRow:{}".format(firstRow, newRow))
    return firstRow, newRow

# 根据新费率，计算新的费用，生成更新记录
def gen_updaterow(row, calcItem, inOutFlag):
    upRow = list(row)
    sheetCnt = row[fieldIndex["sheet_cnt"]]
    
    if inOutFlag == "in":
        upRow[fieldIndex["in_rate2"]] = calcItem["rate1"]
        upRow[fieldIndex["in_option_rate"]] = calcItem["op_rate1"]
        upRow[fieldIndex["in_charge_2"]] = sheetCnt * upRow[fieldIndex["in_rate2"]]
        upRow[fieldIndex["in_op_charge"]] = sheetCnt * upRow[fieldIndex["in_option_rate"]]
    elif inOutFlag == "out": 
        upRow[fieldIndex["out_rate2"]] = calcItem["rate1"]
        upRow[fieldIndex["out_option_rate"]] = calcItem["op_rate1"]
        upRow[fieldIndex["out_charge_2"]] = sheetCnt * upRow[fieldIndex["out_rate2"]]
        upRow[fieldIndex["out_op_charge"]] = sheetCnt * upRow[fieldIndex["out_option_rate"]]
    else:
        print("gen_updaterow inOutFlag error:{}".format(inOutFlag))
        logging.info("gen_updaterow inOutFlag error:{}".format(inOutFlag))
    
    print("gen_updaterow return upRow:", upRow)
    logging.debug("gen_updaterow return upRow:{}".format(upRow))
    return upRow

'''UPDATE  stat_cmi_sms_daily_{0}
    SET sheet_cnt=%(sheet_cnt)s, in_rate2=%(in_rate2)s, in_charge_2=%(in_charge_2)s, out_charge_2=%(out_charge_2)s, 
        in_op_charge=%(in_op_charge)s, out_op_charge=%(out_op_charge)s
    WHERE ctid=%(rowkey)s
'''
def add_updateParam(updateListParam, updateRow, inOutFlag):
    if inOutFlag == "in":
        updateListParam.append({'sheet_cnt':updateRow[fieldIndex["sheet_cnt"]],
                                'in_rate2':updateRow[fieldIndex["in_rate2"]],
                                'in_charge_2':updateRow[fieldIndex["in_charge_2"]],
                                'out_charge_2':updateRow[fieldIndex["out_charge_2"]],
                                'in_option_rate':updateRow[fieldIndex["in_option_rate"]],
                                'in_op_charge':updateRow[fieldIndex["in_op_charge"]],
                                'out_op_charge':updateRow[fieldIndex["out_op_charge"]],
                                'rowkey':updateRow[fieldIndex["ctid"]]
                            })
    elif inOutFlag == "out":
        updateListParam.append({'sheet_cnt':updateRow[fieldIndex["sheet_cnt"]],
                                'out_rate2':updateRow[fieldIndex["out_rate2"]],
                                'out_charge_2':updateRow[fieldIndex["out_charge_2"]],
                                'in_charge_2':updateRow[fieldIndex["in_charge_2"]],
                                'out_option_rate':updateRow[fieldIndex["out_option_rate"]],
                                'in_op_charge':updateRow[fieldIndex["in_op_charge"]],
                                'out_op_charge':updateRow[fieldIndex["out_op_charge"]],
                                'rowkey':updateRow[fieldIndex["ctid"]]
                            })
    else:
        print("add_updateParam inOutFlag error:", inOutFlag)
        logging.info("add_updateParam inOutFlag error:{}".format(inOutFlag))
    
    print("add_updateParam return updateListParam:", updateListParam)
    logging.debug("add_updateParam return updateListParam:{}".format(updateListParam))


def isRateChanged(row, calcItem, inOutFlag):
    if inOutFlag == "in":
        return calcItem['rate1'] != row[fieldIndex["in_rate2"]]
    elif inOutFlag == "out":
        return calcItem['rate1'] != row[fieldIndex["out_rate2"]]
    else:
        return False

# 全量汇总(大部分是实时汇总)时，删除正在做汇总(其他并发任务)的UID  
def filter_uid_byRedoTask(dbConnect, strWorkDay, uniqueIdSet, strAcctId="-1"):
    strWorkMonth = strWorkDay[0:6]

    if strAcctId == "-1":   # 当前任务是全量汇总
        print("filter_uid_byRedoTask in uniqueIdSet {}:".format(uniqueIdSet) )
        logging.debug("filter_uid_byRedoTask in uniqueIdSet {}:".format(uniqueIdSet) )

        # 查找当月正在重批汇总任务的UID
        sql = """select distinct nvl(acct_id,'-1') as acct_id , redo_task_id
                 from dps_cmi_action_log 
                 where  action_code='sms_stat' and action_state in (1, 2) and substr(work_day, 0, 6) = '{0}' 
                """.format(strWorkMonth)

        rows = fetchAll(dbConnect, sql)

        if not rows is None:
            for row in rows:
                oriAcctId = row[0]
                redoTaskId = row[1]
                if oriAcctId == "-1": #有全量重批任务, 所以acctId都不做阶梯计算
                    uniqueIdSet=set()
                else: 
                    redoAcctIds=getrealAcctId(dbConnect, oriAcctId, redoTaskId)

                    # 需要将字符串 ('id1','id2',...) 转换为列表 ['id1', 'id2', ...]
                    acctIdList = [aid.strip().strip("'") for aid in redoAcctIds.strip("()").split(",") if aid.strip()]
                    for acctId in acctIdList:
                        uniqueIdSet.discard(acctId)
    
        print("filter_uid_byRedoTask return uniqueIdSet {}:".format(uniqueIdSet) )
        logging.debug("filter_uid_byRedoTask return uniqueIdSet {}:".format(uniqueIdSet) )

    return uniqueIdSet


def calc_tierRate(dbConnect, strWorkDay, dictRate, uniqueIdSet, inOutFlag, strAcctId="-1"):

    print("start calc_tierRate inOutFlag: {}".format(inOutFlag))
    logging.info("start calc_tierRate inOutFlag: {}".format(inOutFlag))

    uniqueIdSet = filter_uid_byRedoTask(dbConnect, strWorkDay, uniqueIdSet, strAcctId)

    # 查询需要计算阶梯资费的汇总记录
    strWorkMonth = strWorkDay[0:6]

    if inOutFlag == "in":
        # 查整月的正常A2P记录
        sqlInData = '''
            SELECT /*+set(query_dop 8) */
                cdr_type, original_file, bill_month, call_type, traffic_type, in_ref_number, in_dest_id, in_county_id, in_rate_plan_id, in_rate_plan_detail_id, 
                in_rate1, in_rate2, in_service_id, in_zone_id, out_ref_number, out_dest_id, out_county_id, out_rate_plan_id, out_rate_plan_detail_id, out_rate1, 
                out_rate2, out_service_id, out_zone_id, send_time, recv_time, finish_time, sheet_cnt, input_date, in_settle_side, in_acc_settle_id, 
                in_charge_1, in_charge_2, in_currency_id, out_settle_side, out_acc_settle_id, out_charge_1, out_charge_2, out_currency_id, in_g_type, out_g_type, 
                direction, mo_mt, src_oper_id, access_oper_id, out_oper_id, dest_oper_id, peer_name, original_realm, destination_realm, opc_stp_name, 
                dpc_stp_name, reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, status,
                in_error, out_error, resettle_dir, resettle_taskid, in_op_charge, out_op_charge, in_option_rate, out_option_rate, in_account_id, out_account_id, 
                ctid
            FROM stat_cmi_sms_daily_%(work_month)s
            WHERE cdr_type in (501, 502) AND in_error = 0 AND status = 1 and in_account_id in %(unique_id_list)s
            ORDER BY ctid
        '''
        sqlUpdate = '''
            UPDATE /*+set(query_dop 8) */ stat_cmi_sms_daily_{0}
            SET sheet_cnt=%(sheet_cnt)s, in_rate2=%(in_rate2)s, in_charge_2=%(in_charge_2)s, out_charge_2=%(out_charge_2)s, 
                in_option_rate=%(in_option_rate)s, in_op_charge=%(in_op_charge)s, out_op_charge=%(out_op_charge)s
            WHERE ctid=%(rowkey)s
        '''.format(strWorkMonth)
    elif inOutFlag == "out":
        sqlInData = '''
            SELECT /*+set(query_dop 8) */
                cdr_type, original_file, bill_month, call_type, traffic_type, in_ref_number, in_dest_id, in_county_id, in_rate_plan_id, in_rate_plan_detail_id, 
                in_rate1, in_rate2, in_service_id, in_zone_id, out_ref_number, out_dest_id, out_county_id, out_rate_plan_id, out_rate_plan_detail_id, out_rate1, 
                out_rate2, out_service_id, out_zone_id, send_time, recv_time, finish_time, sheet_cnt, input_date, in_settle_side, in_acc_settle_id, 
                in_charge_1, in_charge_2, in_currency_id, out_settle_side, out_acc_settle_id, out_charge_1, out_charge_2, out_currency_id, in_g_type, out_g_type, 
                direction, mo_mt, src_oper_id, access_oper_id, out_oper_id, dest_oper_id, peer_name, original_realm, destination_realm, opc_stp_name, 
                dpc_stp_name, reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, status,
                in_error, out_error, resettle_dir, resettle_taskid, in_op_charge, out_op_charge, in_option_rate, out_option_rate, in_account_id, out_account_id, 
                ctid
            FROM stat_cmi_sms_daily_%(work_month)s
            WHERE cdr_type in (501, 502) AND out_error = 0 AND status = 1 and out_account_id in %(unique_id_list)s
            ORDER BY ctid
        '''
        sqlUpdate = '''
            UPDATE /*+set(query_dop 8) */ stat_cmi_sms_daily_{0}
            SET sheet_cnt=%(sheet_cnt)s, out_rate2=%(out_rate2)s, in_charge_2=%(in_charge_2)s, out_charge_2=%(out_charge_2)s, 
                out_option_rate=%(out_option_rate)s, in_op_charge=%(in_op_charge)s, out_op_charge=%(out_op_charge)s
            WHERE ctid=%(rowkey)s
        '''.format(strWorkMonth)
    else:
        print("calc_tierRate inOutFlag error: {}".format(inOutFlag))
        logging.info("calc_tierRate inOutFlag error: {}".format(inOutFlag))
    
    sqlInsert = '''INSERT /*+set(query_dop 8) */ INTO stat_cmi_sms_daily_{0} 
        (cdr_type, original_file, bill_month, call_type, traffic_type, in_ref_number, in_dest_id, in_county_id, in_rate_plan_id, in_rate_plan_detail_id, 
            in_rate1, in_rate2, in_service_id, in_zone_id, out_ref_number, out_dest_id, out_county_id, out_rate_plan_id, out_rate_plan_detail_id, out_rate1, 
            out_rate2, out_service_id, out_zone_id, send_time, recv_time, finish_time, sheet_cnt, input_date, in_settle_side, in_acc_settle_id, 
            in_charge_1, in_charge_2, in_currency_id, out_settle_side, out_acc_settle_id, out_charge_1, out_charge_2, out_currency_id, in_g_type, out_g_type, 
            direction, mo_mt, src_oper_id, access_oper_id, out_oper_id, dest_oper_id, peer_name, original_realm, destination_realm, opc_stp_name, 
            dpc_stp_name, reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, status,
            in_error, out_error, resettle_dir, resettle_taskid, in_op_charge, out_op_charge, in_option_rate, out_option_rate, in_account_id, out_account_id
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
    '''.format(strWorkMonth)

    uniqueIdList = list(uniqueIdSet)
    uniqueIdStr = get_instr_from_list(uniqueIdList)
    sqlInData = sqlInData % {"work_month": strWorkMonth, "unique_id_list": uniqueIdStr}
    # print("calc_tierRate  query all data sql:", sqlInData)
    
    start_time = datetime.now()
    cur = dbConnect.cursor()
    cur.execute(sqlInData)
    rows = cur.fetchall()
    cur.close()
    # print("calc_tierRate rows:", rows)
    print("calc_tierRate rows len:{}".format(len(rows)))

    # 计算耗时
    end_time = datetime.now()  
    total_time = end_time - start_time  
    total_seconds = total_time.total_seconds() 
    print("getInData cost time: {}s".format(total_seconds)) 

    # 生成字典数据
    dictData = genDictData(rows, inOutFlag)

    # for key, dictDateRows in dictData.items():
    #     print("calc_tierRate key:", key)
    #     for finishTime, rows in dictDateRows.items():
    #         print("calc_tierRate finishTime:{} len:{}".format(finishTime, len(rows)))
    updateListParam = []
    insertListRow = []
    for key, rateItem in dictRate.items():
        # print("calc_tierRate key:", key)
        # print("calc_tierRate rateItem:", rateItem)

        uniqueId = rateItem[0]['unique_id']
        if inOutFlag == "in":
            itemId = rateItem[0]['item_id']
        elif inOutFlag == "out":
            itemId = rateItem[0]['product_id']
        
        # 忽略被过滤掉的uniqueId
        if uniqueId not in uniqueIdList:
            continue

        # key = str(uniqueId) + "|" + str(itemId)
        chargeMode = rateItem[0]['charge_mode']

        updateCost = 0   # 更新费率费用耗时

        if chargeMode == 10: #半阶梯
            if key not in dictData:
                # print("calc_tierRate key not in dictData:", key)
                continue
            else:
                print("calc_tierRate key:{} in dictData have data, half tier".format(key))
                logging.info("calc_tierRate key:{} in dictData have data, half tier".format(key))
            dictDateRows = dictData[key]

            # 把日期排序，按排好序的日期处理汇总记录
            sortedDates = sorted(dictDateRows.keys())
            
            sheetCntAll = 0  # 话单累积量
            # for finishTime, rows in dictDateRows.items():
            for finishTime in sortedDates:
                rows = dictDateRows[finishTime]
                print("calc_tierRate finishTime:{} len:{}".format(finishTime, len(rows)))
                logging.debug("calc_tierRate key:{} in dictData have data, half tier".format(key))
                for row in rows:  # 对每条话单算费
                    sheetCnt = row[fieldIndex["sheet_cnt"]]
                    calcItem = calc_halfTier(row, rateItem, sheetCntAll, finishTime) # 计算半阶梯资费
                    if calcItem["splite_flag"]:
                        updateRow, newRow = splite_row(row, calcItem, inOutFlag)  # 跨阶拆分
                        add_updateParam(updateListParam, updateRow, inOutFlag)
                        insertListRow.append(newRow)
                    elif isRateChanged(row, calcItem, inOutFlag):  # 未跨阶梯，费率和原记录不同，则要更新
                        updateRow = gen_updaterow(row, calcItem, inOutFlag)
                        add_updateParam(updateListParam, updateRow, inOutFlag)
                    else:  # 未跨阶梯，费率和原记录相同，不更新
                        pass

                    sheetCntAll += sheetCnt
        elif chargeMode == 20: #全阶梯
            if key not in dictData:
                # print("calc_tierRate key not in dictData:", key)
                continue
            else:
                print("calc_tierRate key:{} in dictData have data, full tier".format(key))
                logging.info("calc_tierRate key:{} in dictData have data, full tier".format(key))
            start_time = datetime.now()
            dictDateRows = dictData[key]
            sheetCntAll = 0  # 话单累积量

            # 把日期排序，按排好序的日期处理汇总记录
            sortedDates = sorted(dictDateRows.keys())

            if sumSheetCntBySql == False:
                for finishTime, rows in dictDateRows.items():
                    print("calc_tierRate finishTime:{} len:{}".format(finishTime, len(rows)))
                    for row in rows:  # 计算总量
                        sheetCnt = row[fieldIndex["sheet_cnt"]]
                        sheetCntAll+=sheetCnt
            
                end_time = datetime.now()  

                # 计算耗时
                total_time = end_time - start_time  
                total_seconds = total_time.total_seconds() 
                print("sum sheet_cnt={} by loop dictData cost time: {}s".format(sheetCntAll, total_seconds)) 
                logging.info("sum sheet_cnt={} by loop dictData cost time: {}s".format(sheetCntAll, total_seconds))
            else:
                # 查询总条数
                if inOutFlag == "in":
                    sqlInSumData = '''
                        SELECT /*+set(query_dop 8) */
                            sum(sheet_cnt)
                    FROM stat_cmi_sms_daily_%(work_month)s
                    WHERE cdr_type in (501, 502) AND in_error = 0 AND status = 1 and in_account_id = '%(unique_id)s' and in_acc_settle_id = %(item_id)s
                        GROUP BY cdr_type, in_account_id, in_acc_settle_id
                    '''
                elif inOutFlag == "out":
                    sqlInSumData = '''
                        SELECT /*+set(query_dop 8) */
                            sum(sheet_cnt)
                        FROM stat_cmi_sms_daily_%(work_month)s
                        WHERE cdr_type in (501, 502) AND out_error = 0 AND status = 1 and out_account_id = '%(unique_id)s' and out_acc_settle_id = %(item_id)s
                        GROUP BY cdr_type, out_account_id, out_acc_settle_id
                    '''
                
                sqlInSumData = sqlInSumData % {"work_month": strWorkMonth, "unique_id":uniqueId, "item_id": itemId}
                # print("full tier calc_tierRate sql:", sqlInSumData)

                cur = dbConnect.cursor()
                cur.execute(sqlInSumData)
                rows = cur.fetchall()
                cur.close()
                print("full tier calc_tierRate sqlInSumData rows:", rows)
                sheetCntAll = rows[0][0]

                end_time = datetime.now()  
                # 计算耗时
                total_time = end_time - start_time  
                total_seconds = total_time.total_seconds() 
                print("sum sheet_cnt={} by sql cost time: {}s".format(sheetCntAll, total_seconds)) 
                logging.info("sum sheet_cnt={} by sql cost time: {}s".format(sheetCntAll, total_seconds))

            # 用最后一个日期查找资费
            calcItem = calc_fullfTier(sheetCntAll, rateItem, sortedDates[-1])

            start_time = datetime.now()
            if updateBySqlFull == False:
                # 筛选需要更新的记录后，再更新
                for finishTime, rows in dictDateRows.items():
                    print("calc_tierRate finishTime:{} len:{}".format(finishTime, len(rows)))
                    for row in rows:  
                        if isRateChanged(row, calcItem, inOutFlag):  # 费率和原记录不同，则要更新
                            updateRow = gen_updaterow(row, calcItem, inOutFlag)
                            add_updateParam(updateListParam, updateRow, inOutFlag)
                        else:  # 费率和原记录相同，不更新
                            pass
            
                # 计算耗时
                end_time = datetime.now()  
                total_time = end_time - start_time  
                total_seconds = total_time.total_seconds() 
                updateCost += total_seconds
                print("update fee by loop find update rows cost time: {}s".format(total_seconds)) 
                logging.info("update fee by loop find update rows cost time: {}s".format(total_seconds))
            else:
                #  通过sql全量更新费率和费用
                start_time = datetime.now()
                if inOutFlag == "in":
                    sqlUpdateFullRate = '''
                        UPDATE /*+set(query_dop 8) */ stat_cmi_sms_daily_{month}
                        SET in_rate2={in_rate2}, in_charge_2=sheet_cnt*{in_rate2}, 
                            in_option_rate={in_option_rate}, in_op_charge=sheet_cnt*{in_option_rate} 
                        WHERE in_account_id='{unique_id}' and in_acc_settle_id={item_id}
                    '''.format(month=strWorkMonth, in_rate2=calcItem['rate1'], in_option_rate=calcItem['op_rate1'], unique_id=uniqueId, item_id=itemId)
                elif inOutFlag == "out":
                    sqlUpdateFullRate = '''
                        UPDATE /*+set(query_dop 8) */ stat_cmi_sms_daily_{month}
                        SET out_rate2={out_rate2}, out_charge_2=sheet_cnt*{out_rate2}, 
                            out_option_rate={out_option_rate}, out_op_charge=sheet_cnt*{out_option_rate} 
                        WHERE out_account_id='{unique_id}' and out_acc_settle_id={item_id}
                    '''.format(month=strWorkMonth, out_rate2=calcItem['rate1'], out_option_rate=calcItem['op_rate1'], unique_id=uniqueId, item_id=itemId)
                else:
                    print("calc_tierRate inOutFlag error: {}".format(inOutFlag))
                    logging.info("calc_tierRate inOutFlag error: {}".format(inOutFlag))
                    return
                # print("calc_tierRate sqlUpdateFullRate:", sqlUpdateFullRate)

                cur = dbConnect.cursor()
                cur.execute(sqlUpdateFullRate)
                cur.close()
                dbConnect.commit()
                # 计算耗时
                end_time = datetime.now()
                total_time = end_time - start_time  
                total_seconds = total_time.total_seconds() 
                print("update fee by sql cost1 time: {}s".format(total_seconds)) 
                logging.info("update fee by sql cost1 time: {}s".format(total_seconds))
        else:
            print("calc_tierRate chargeMode error:{}".format(chargeMode))
            logging.info("calc_tierRate chargeMode error:{}".format(chargeMode))

        if len(updateListParam) > 10000:  # 批量更新
            cur = dbConnect.cursor()
            cur.executemany(sqlUpdate, updateListParam)
            cur.close()
            dbConnect.commit()
            print("calc_tierRate commit updateListParam len:", len(updateListParam))
            updateListParam = []
        if len(insertListRow) > 100:  # 批量插入
            cur = dbConnect.cursor()
            cur.executemany(sqlInsert, insertListRow)
            cur.close()
            dbConnect.commit()
            print("calc_tierRate commit insertListRow len:", len(insertListRow))
            insertListRow = []

    if len(updateListParam) > 0:  # 最后一次更新
        start_time = datetime.now()  
        cur = dbConnect.cursor()
        cur.executemany(sqlUpdate, updateListParam)
        cur.close()
        dbConnect.commit()
        # 计算耗时
        end_time = datetime.now()  
        total_time = end_time - start_time  
        total_seconds = total_time.total_seconds() 
        updateCost += total_seconds
        print("update fee by loop cost time: {}s".format(updateCost)) 
        logging.info("update fee by loop cost time: {}s".format(updateCost))

        print("calc_tierRate commit updateListParam len:", len(updateListParam))
        updateListParam = []
    if len(insertListRow) > 0:  # 最后一次插入
        cur = dbConnect.cursor()
        cur.executemany(sqlInsert, insertListRow)
        cur.close()
        dbConnect.commit()
        print("calc_tierRate commit insertListRow len:", len(insertListRow))
        insertListRow = []

    print("end calc_tierRate inOutFlag: {}".format(inOutFlag))
    logging.info("end calc_tierRate inOutFlag: {}".format(inOutFlag))

    return

# keyIndex1 对应in_account_id 或 out_account_id 的位置
# keyIndex2 对应in_acc_settle_id 或 out_acc_settle_id 的位置
def genDictData(rows, inOutFlag):
    if inOutFlag == "in":
        keyIndex1 = fieldIndex["in_account_id"]
        keyIndex2 = fieldIndex["in_acc_settle_id"]
    elif inOutFlag == "out":
        keyIndex1 = fieldIndex["out_account_id"]
        keyIndex2 = fieldIndex["out_acc_settle_id"]
    else:
        print("genDictData inOutFlag error:{}".format(inOutFlag))
        logging.info("genDictData inOutFlag error:{}".format(inOutFlag))
        return None

    dictData = {}
    for row in rows:
        uniqueId = row[keyIndex1]
        accSettleId = row[keyIndex2]
        finishTime = row[fieldIndex["finish_time"]]
        key = str(uniqueId) + "|" + str(accSettleId)
        if key not in dictData:
            dictDateRows={}
            dictDateRows[finishTime]=[]
            dictDateRows[finishTime].append(row)
            dictData[key] = dictDateRows
        else:
            if finishTime not in dictData[key]:
                dictData[key][finishTime]=[]
            dictData[key][finishTime].append(row)

    return dictData


# 计算阶梯资费
def calcTierRate(dbConnect, strWorkDay, strAcctId="-1", strDirFlag="1"):
    if calcCtledByTime:
        # 月末最后1天或当日才计算阶梯资费
        if isLastDayOfMonth( strWorkDay) or isToday(strWorkDay):
            do_calcTierRate(dbConnect, strWorkDay, strAcctId, strDirFlag)
        else:
            print("calcTierRate strWorkDay:{} is not last day of month or today".format(strWorkDay))
            logging.info("calcTierRate strWorkDay:{} is not last day of month or today".format(strWorkDay))
    else:  # 不控制计算时间
        do_calcTierRate(dbConnect, strWorkDay, strAcctId, strDirFlag)

    return

def do_calcTierRate(dbConnect, strWorkDay, strAcctId="-1", strDirFlag="1"):
    # 加载资费
    dictInRate, dictOutRate, inUniqueIdSet, outUniqueIdSet = loadTierRate(dbConnect, strWorkDay, strAcctId, strDirFlag)

    if (strAcctId == "-1"):
        # 计算收入的阶梯资费
        calc_tierRate(dbConnect, strWorkDay, dictInRate, inUniqueIdSet, "in", strAcctId)
        # 计算成本的阶梯资费
        calc_tierRate(dbConnect, strWorkDay, dictOutRate, outUniqueIdSet, "out", strAcctId)
    else: #  指定账户
        if strDirFlag == "1": #  成本
            # 计算成本的阶梯资费
            calc_tierRate(dbConnect, strWorkDay, dictOutRate, outUniqueIdSet, "out", strAcctId)
        else:
            # 计算收入的阶梯资费 
            calc_tierRate(dbConnect, strWorkDay, dictInRate, inUniqueIdSet, "in", strAcctId)

def fetchAll(dbConnect, sql, parameters=None, arraysize=500):
    try:
        cur = dbConnect.cursor()
        if parameters is None:
            cur.execute(sql)
        else:
            cur.execute(sql, parameters)
        rows = cur.fetchall()
        cur.close()
        return rows
    except Exception as e:
        if hasattr(e, 'pgcode') and hasattr(e, 'pgerror'):
            print("PostgreSQL Execute exception, ErrCode: %s, ErrMsg: %s" % (e.pgcode, e.pgerror))
        else:
            print("Execute exception, ErrMsg: %s" % str(e))
    return None

def getLastTime(dbConnect):
    lastTime = ""
    sql = "select to_char(now() - interval '2 hour', 'YYYYMMDDHH24MISS')"
    rows = fetchAll(dbConnect, sql)

    if not rows is None:
        for row in rows:
             lastTime = row[0]

    return lastTime

def geTable4Collect(dbConnect, lastTime):
    sql = "select /*+set(query_dop 8) */ distinct table_name from dps_dataldr_info where module_id in (15030,15031,35030,152051) and load_time >= to_timestamp('" + lastTime + "', 'YYYYMMDDHH24MISS') order by table_name asc"
    return fetchAll(dbConnect, sql)

def getrealAcctId(dbConnect, strAcctId, redoTaskId):
    if len(strAcctId) < 3:  # 默认为-1
        strAcctId = "-1"
    elif strAcctId == "ACCT_ID_TOO_LONG": # 从参数表取  
        strAcctId = strAcctId[1:]      
        sql = "select task_args_value from sys_task_args_detail where task_id = {0} and task_args_code = 'ACCT_ID'".format(redoTaskId)
        rows = fetchAll(dbConnect, sql)
        if not rows is None:
            for row in rows:
                strAcctId = str(row[0])
                break
    print("getrealAcctId strAcctId:{}".format(strAcctId))
    return strAcctId

# 每次取N条数据
def getWorkDayk4Collect(dbConnect):
    sql = "select work_day, nvl(acct_id,'-1') as acct_id, dir_flag, redo_task_id from dps_cmi_action_log where action_code='sms_stat' and data_type=0 and action_state=1 order by begin_time, work_day limit {0}".format(get_task_num)
    rows = fetchAll(dbConnect, sql)
    haveData = False
    strAcctId = "-1"

    lstTaskInfo = []

    if not rows is None:
        for row in rows:
            taskInfo = {}
            taskInfo["work_day"] = str(row[0])
            oriStrAcctId = str(row[1])
            redoTaskId = int(row[3])

            strAcctId = getrealAcctId(dbConnect, oriStrAcctId, redoTaskId)
            taskInfo["ori_acct_id"] = oriStrAcctId
            taskInfo["acct_id"] = strAcctId
            taskInfo["dir_flag"] = str(row[2])
            taskInfo["redo_task_id"] = redoTaskId
            
            haveData = True
            lstTaskInfo.append(taskInfo)
            
    if not haveData:
        sql = "select work_day, nvl(acct_id,'-1') as acct_id, dir_flag, redo_task_id from dps_cmi_action_log where action_code='sms_stat' and data_type=1 and action_state=1 order by begin_time, work_day limit {0}".format(get_task_num)
        rows = fetchAll(dbConnect, sql)

        if not rows is None:
            for row in rows:
                taskInfo = {}
                taskInfo["work_day"] = str(row[0])
                oriStrAcctId = str(row[1])
                redoTaskId = int(row[3])

                strAcctId = getrealAcctId(dbConnect, oriStrAcctId, redoTaskId)
                taskInfo["ori_acct_id"] = oriStrAcctId
                taskInfo["acct_id"] = strAcctId
                taskInfo["dir_flag"] = str(row[2])
                taskInfo["redo_task_id"] = redoTaskId

                lstTaskInfo.append(taskInfo)

    return lstTaskInfo

def startCollect(dbConnect, listTaskInfo):
    # 生成 in (taskid1, taskid2, taskid3)的string
    lisTaskId = [taskInfo["redo_task_id"] for taskInfo in listTaskInfo]
    strTaskId = get_instr_from_list(lisTaskId)
    
    sql = "update dps_cmi_action_log set action_state=2 where action_code='sms_stat' and redo_task_id in {0} and action_state=1".format(strTaskId)
    cur = dbConnect.cursor()
    cur.execute(sql)
    cur.close()
    
    dbConnect.commit()

def updateTaskState(dbConnect, redoTaskId, state):
    sql = "update dps_cmi_action_log set action_state={0}, end_time=now() where action_code='sms_stat' and redo_task_id = {1} and action_state=2".format(state, redoTaskId)
    cur = dbConnect.cursor()
    cur.execute(sql)
    cur.close()
    dbConnect.commit()  

def endCollect(dbConnect, listTaskInfo): 
    # 生成 in (taskid1, taskid2, taskid3)的string
    lisTaskId = [taskInfo["redo_task_id"] for taskInfo in listTaskInfo]
    strTaskId = get_instr_from_list(lisTaskId)

    sql = "update dps_cmi_action_log set action_state=0, end_time=now() where action_code='sms_stat' and redo_task_id in {0} and action_state=2".format(strTaskId)
    cur = dbConnect.cursor()
    cur.execute(sql)
    cur.close()
    dbConnect.commit()

def getCntOfTask(dbConnect):
    cnt = -1
    sql = "select count(1) from (select distinct work_day from dps_cmi_action_log where action_code='sms_stat' and action_state=2)"
    rows = fetchAll(dbConnect, sql)

    if not rows is None:
        for row in rows:
            cnt = row[0]

    return cnt

def doCollect(dbConnect, workDay, strAcctId="-1", strDirFlag="1"):
    strWorkMonth = workDay[0:6]
    strQryCondition = ""     # 详单表的账号条件
    strStatCondition  = ""   # 汇总表的账号条件

    if strAcctId != "-1":
        if strDirFlag == "1":  # out 成本
            strQryCondition = "where OUT_ACCT_ID in {0}".format(strAcctId)
            strStatCondition = " AND OUT_ACCOUNT_ID in {0}".format(strAcctId)
        else:  # in 收入
            strQryCondition = "where IN_ACCT_ID in {0}".format(strAcctId)
            strStatCondition = " AND IN_ACCOUNT_ID in {0}".format(strAcctId)
    

    sqlDel = "DELETE /*+set(query_dop 8) */ FROM stat_cmi_sms_daily_%(work_month)s WHERE cdr_type IN (500,501,502) AND finish_time=%(work_day)s"+strStatCondition
    sqlInsert = '''INSERT /*+set(query_dop 8) */ INTO stat_cmi_sms_daily_%(work_month)s
(CDR_TYPE, ORIGINAL_FILE, BILL_MONTH, CALL_TYPE, TRAFFIC_TYPE, IN_ACCOUNT_ID,
 IN_REF_NUMBER, IN_DEST_ID, IN_COUNTY_ID, IN_RATE_PLAN_ID, IN_RATE_PLAN_DETAIL_ID,
 IN_RATE1, IN_RATE2, IN_SERVICE_ID, IN_ZONE_ID, OUT_ACCOUNT_ID, OUT_REF_NUMBER,
 OUT_DEST_ID, OUT_COUNTY_ID, OUT_RATE_PLAN_ID, OUT_RATE_PLAN_DETAIL_ID,
 OUT_RATE1, OUT_RATE2, OUT_SERVICE_ID, OUT_ZONE_ID, SEND_TIME, RECV_TIME,
 FINISH_TIME, SHEET_CNT, INPUT_DATE, IN_SETTLE_SIDE, IN_ACC_SETTLE_ID, IN_CHARGE_1,
 IN_CHARGE_2, IN_CURRENCY_ID, OUT_SETTLE_SIDE, OUT_ACC_SETTLE_ID, OUT_CHARGE_1,
 OUT_CHARGE_2, OUT_CURRENCY_ID, IN_G_TYPE, OUT_G_TYPE, DIRECTION, MO_MT, SRC_OPER_ID,
 ACCESS_OPER_ID, OUT_OPER_ID, DEST_OPER_ID, PEER_NAME, ORIGINAL_REALM, DESTINATION_REALM,
 OPC_STP_NAME, DPC_STP_NAME, STATUS, IN_ERROR, OUT_ERROR, RESERVED1, RESERVED2, RESERVED3,
 RESERVED4, RESERVED5, RESERVED6, RESERVED7, RESERVED8, RESETTLE_DIR, RESETTLE_TASKID, IN_OP_CHARGE, OUT_OP_CHARGE,
 IN_OPTION_RATE, OUT_OPTION_RATE)
(
SELECT 
 CDR_TYPE, '0', substring(FINISH_TIME from 1 for 6), CHARING_TYPE, TRAFFIC_TYPE, IN_ACCT_ID,
 0, IN_DEST_ID, SEND_COUNTRY_ID, IN_RATE_PLAN_ID, IN_RATE_PLAN_DETAIL_ID,
 IN_RATE1, IN_RATE2, IN_SERVICE_ID, IN_ZONE_ID, OUT_ACCT_ID, '0',
 OUT_DEST_ID, DEST_COUNTRY_ID, OUT_RATE_PLAN_ID, OUT_RATE_PLAN_DETAIL_ID,
 OUT_RATE1, OUT_RATE2, OUT_SERVICE_ID, OUT_ZONE_ID, substring(SEND_TIME from 1 for 8), 
 CASE WHEN cdr_type = 501 THEN substring(FINISH_TIME from 1 for 8) ELSE '0' END,
 substring(FINISH_TIME from 1 for 8), sum(COALESCE(billing_items_cnt, 1)), '0', IN_SETTLE_SIDE, IN_ACC_SETTLE_ID, sum(IN_CHARGE_1),
 sum(IN_CHARGE_2), CURRENCY_ID, OUT_SETTLE_SIDE, OUT_ACC_SETTLE_ID, sum(OUT_CHARGE_1),
 sum(OUT_CHARGE_2), CURRENCY_ID, IN_G_TYPE, OUT_G_TYPE, DIRECTION, MO_MT, SRC_OPER_ID,
 ACCESS_OPER_ID, OUT_OPER_ID, DEST_OPER_ID, '0', '0', '0',
 OPC_STP_NAME, DPC_STP_NAME, STATUS, IN_ERROR, OUT_ERROR, RESERVED1, RESERVED2, RESERVED3,
 0 AS RESERVED4, RESERVED5, RESERVED6, RESERVED7, CASE WHEN COALESCE(file_source, 0) IN (2, 3) THEN 'CLOUD' ELSE '' END AS RESERVED8, RESETTLE_DIR, RESETTLE_TASKID, sum(IN_OP_CHARGE), sum(OUT_OP_CHARGE),
 IN_OP_CHARGE, OUT_OP_CHARGE
FROM dr_cmi_sms_%(work_day)s %(qry_cond)s
GROUP BY 
 CDR_TYPE, substring(FINISH_TIME from 1 for 6), CHARING_TYPE, TRAFFIC_TYPE, IN_ACCT_ID,
 IN_DEST_ID, SEND_COUNTRY_ID, IN_RATE_PLAN_ID, IN_RATE_PLAN_DETAIL_ID,
 IN_RATE1, IN_RATE2, IN_SERVICE_ID, IN_ZONE_ID, OUT_ACCT_ID,
 OUT_DEST_ID, DEST_COUNTRY_ID, OUT_RATE_PLAN_ID, OUT_RATE_PLAN_DETAIL_ID,
 OUT_RATE1, OUT_RATE2, OUT_SERVICE_ID, OUT_ZONE_ID,
 substring(FINISH_TIME from 1 for 8), IN_SETTLE_SIDE, IN_ACC_SETTLE_ID,
 CURRENCY_ID, OUT_SETTLE_SIDE, OUT_ACC_SETTLE_ID,
 CURRENCY_ID, IN_G_TYPE, OUT_G_TYPE, DIRECTION, MO_MT, SRC_OPER_ID,
 ACCESS_OPER_ID, OUT_OPER_ID, DEST_OPER_ID,
 OPC_STP_NAME, DPC_STP_NAME, STATUS, IN_ERROR, OUT_ERROR, RESERVED1, RESERVED2, RESERVED3,
 RESERVED5, RESERVED6, RESERVED7, CASE WHEN COALESCE(file_source, 0) IN (2, 3) THEN 'CLOUD' ELSE '' END,
 RESETTLE_DIR, RESETTLE_TASKID,
 substring(SEND_TIME from 1 for 8), CASE WHEN cdr_type = 501 THEN substring(FINISH_TIME from 1 for 8) ELSE '0' END, IN_OP_CHARGE, OUT_OP_CHARGE
)
    '''
    sqlInsertCloud = '''INSERT /*+set(query_dop 8) */ INTO stat_cmi_sms_daily_%(work_month)s
(
cdr_type, original_file, bill_month, call_type, traffic_type, in_ref_number,
in_dest_id, in_county_id, in_rate_plan_id, in_rate_plan_detail_id, in_rate1,
in_rate2, in_service_id, in_zone_id, out_ref_number, out_dest_id, out_county_id,
out_rate_plan_id, out_rate_plan_detail_id, out_rate1, out_rate2, out_service_id,
out_zone_id, send_time, recv_time, finish_time, sheet_cnt, input_date, in_settle_side,
in_acc_settle_id, in_charge_1, in_charge_2, in_currency_id, out_settle_side,
out_acc_settle_id, out_charge_1, out_charge_2, out_currency_id, in_g_type,
out_g_type, direction, mo_mt, src_oper_id, access_oper_id, out_oper_id, dest_oper_id,
peer_name, original_realm, destination_realm, opc_stp_name, dpc_stp_name, reserved1,
reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, status,
in_error, out_error, resettle_dir, resettle_taskid, in_op_charge, out_op_charge,
in_option_rate, out_option_rate, in_account_id, out_account_id
)
(
SELECT 
502, original_file, bill_month, call_type, traffic_type, in_ref_number,
in_dest_id, in_county_id, in_rate_plan_id, in_rate_plan_detail_id, in_rate1,
in_rate2, in_service_id, in_zone_id, out_ref_number, out_dest_id, out_county_id,
out_rate_plan_id, out_rate_plan_detail_id, out_rate1, out_rate2, out_service_id,
out_zone_id, send_time, recv_time, finish_time, sheet_cnt, input_date, in_settle_side,
in_acc_settle_id, in_charge_1, in_charge_2, in_currency_id, out_settle_side,
out_acc_settle_id, out_charge_1, out_charge_2, out_currency_id, in_g_type,
out_g_type, direction, mo_mt, src_oper_id, access_oper_id, out_oper_id, dest_oper_id,
peer_name, original_realm, destination_realm, opc_stp_name, dpc_stp_name, reserved1,
reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, status,
in_error, out_error, resettle_dir, resettle_taskid, in_op_charge, out_op_charge,
in_option_rate, out_option_rate, in_account_id, out_account_id
FROM stat_cmi_sms_daily_%(work_month)s 
WHERE cdr_type = 501 AND RESERVED8='CLOUD' AND finish_time=%(work_day)s %(stat_cond)s
)
'''
    sqlupdate = '''UPDATE /*+set(query_dop 8) */ stat_cmi_sms_daily_%(work_month)s SET OUT_ERROR=93, out_charge_2=0, out_acc_settle_id=0, out_op_charge=0
WHERE cdr_type = 501 AND out_account_id ~ '^[[:digit:]]+$' AND RESERVED8='CLOUD' AND finish_time=%(work_day)s %(stat_cond)s
'''
    cur = dbConnect.cursor()
    cur.execute(sqlDel % {"work_month": strWorkMonth, "work_day": workDay})
    cur.execute(sqlInsert % {"work_month": strWorkMonth, "work_day": workDay, "qry_cond": strQryCondition})
    cur.execute(sqlInsertCloud % {"work_month": strWorkMonth, "work_day": workDay, "stat_cond": strStatCondition})
    cur.execute(sqlupdate % {"work_month": strWorkMonth, "work_day": workDay, "stat_cond": strStatCondition})
    cur.close()
    dbConnect.commit()

def collectProfitDate(dbConnect, workDay):
    strWorkMonth = workDay[0:6]
    sqlInset = '''
    INSERT /*+set(query_dop 8) */ INTO srdsms.r_stat_a2p_profit
    (call_date, in_account_id, out_account_id, std_dest_id, out_country, route_class_id, in_charge, out_charge, in_fee, out_fee,
     csr_type, in_currency, out_currency, sm_cnt, dr_cnt, in_settle_minutes, out_settle_minutes,
     in_sm_dr, out_sm_dr, in_settle_rate, out_settle_rate, in_option_rate, out_option_rate, status)
    (
    SELECT stat.data_date, stat.cust_network_id, stat.supplier_network_id, dst.destination_id, stat.dest_country_id,
        rcs.route_class_id, stat.in_total_fee, stat.out_total_fee, stat.in_total_fee, stat.out_total_fee,
        CASE WHEN csr.csr_commercial_id IS NULL THEN -1 ELSE 1 END AS csr_type, stat.in_currency, stat.out_currency, stat.sm_cnt, stat.dr_cnt, stat.in_cnt, stat.out_cnt,
        stat.in_sm_dr, stat.out_sm_dr, stat.in_rate, stat.out_rate, stat.in_rate, stat.out_rate, 1 AS status
    FROM stat_sms_cross_monthly_%(work_month)s stat
    LEFT JOIN srdsms.r_pm_destination dst ON dst.number_plan_id = -1 AND dst.internal_code = stat.dest_oper_id
        AND to_char(dst.eff_date, 'YYYYMMDD') <= to_char(stat.data_date)
        AND COALESCE(to_char(dst.exp_date, 'YYYYMMDD'), '********') >= to_char(stat.data_date)
    LEFT JOIN srdsms.r_pm_source src ON src.unique_id = to_char(stat.cust_network_id)
    LEFT JOIN srdsms.r_cm_account_route_class rcs ON src.source_id = rcs.source_id
        AND to_char(rcs.eff_date, 'YYYYMMDD') <= to_char(stat.data_date)
        AND COALESCE(to_char(rcs.exp_date, 'YYYYMMDD'), '********') >= to_char(stat.data_date)
    LEFT JOIN srdsms.r_pm_product prd ON prd.destination_id = dst.destination_id
    LEFT JOIN srdsms.r_pm_csr_commercial csr ON csr.customer_id = src.source_id AND csr.product_id = prd.product_id AND csr.eff_flag = 1
        AND to_char(csr.begin_date, 'YYYYMMDD') <= to_char(stat.data_date)
        AND COALESCE(to_char(csr.end_date, 'YYYYMMDD'), '********') >= to_char(stat.data_date)
    WHERE stat.data_date = %(work_day)s
    )
    '''
    cur = dbConnect.cursor()
    sqlDel = 'DELETE /*+set(query_dop 8) */ FROM srdsms.r_stat_a2p_profit WHERE call_date = %(work_day)s'
    cur.execute(sqlDel % {"work_day": workDay})
    cur.execute(sqlInset % {"work_month": strWorkMonth, "work_day": workDay})
    print("collectProfitDate insert count:{}".format(cur.rowcount))
    cur.close()
    dbConnect.commit()


def collectProfitDateCrossDB(dbConnect, oraConnect, workDay):
    """
    跨库处理:
    1. 从GS库查询stat_sms_cross_monthly表数据
    2. 从Oracle库查询维表数据
    3. 在内存中关联数据
    4. 将结果插入Oracle库的r_stat_a2p_profit表
    """
    strWorkMonth = workDay[0:6]
    
    # 1. 从G库查询stat_sms_cross_monthly数据, 查出17个字段
    sqlStatData = '''
    SELECT /*+set(query_dop 8) */ 
        data_date, cust_network_id, supplier_network_id, dest_country_id, dest_oper_id,
        in_total_fee, out_total_fee, in_currency, out_currency, 
        sm_cnt, dr_cnt, in_cnt, out_cnt,
        in_sm_dr, out_sm_dr, in_rate, out_rate
    FROM stat_sms_cross_monthly_%(work_month)s 
    WHERE data_date = %(work_day)s
    '''
    cur = dbConnect.cursor()
    cur.execute(sqlStatData % {"work_month": strWorkMonth, "work_day": workDay})
    statRows = cur.fetchall()
    cur.close()

    if not statRows:
        return

    # 2. 从Oracle库查询关联表数据
    # 2.1 关联落地表
    sqlDest = '''
        SELECT destination_id, internal_code
        FROM srdsms.r_pm_destination 
        WHERE number_plan_id = -1 
        AND to_char(eff_date, 'YYYYMMDD') <= '{work_day}'
        AND COALESCE(to_char(exp_date, 'YYYYMMDD'), '********') >= '{work_day}'
    '''.format(work_day=workDay)
    destRows = fetch_oracle_data(oraConnect, sqlDest)
    # 合并后，增加2个字段，变成19个
    statMergeDstData =process_leftjoin(statRows, destRows, [4], [1])
    # print("statMergeDstData len={}, row1={}".format(len(statMergeDstData), statMergeDstData[0]))

    # 2.2 关联source表
    sqlSource = '''
        SELECT source_id, unique_id 
        FROM srdsms.r_pm_source
    '''
    srcRows = fetch_oracle_data(oraConnect, sqlSource)
    
    # ON src.unique_id = to_char(stat.cust_network_id) 合并后，增加2个字段，变成21个
    statMergeSrcData = process_leftjoin(statMergeDstData, srcRows, [1], [1])
    # print("statMergeSrcData len={}, row1={}".format(len(statMergeSrcData), statMergeSrcData[0]))

    # 2.3 关联路由类别信息
    sqlRouteClass = '''
        SELECT source_id, route_class_id
        FROM srdsms.r_cm_account_route_class        
        WHERE to_char(eff_date, 'YYYYMMDD') <= '{work_day}'
        AND COALESCE(to_char(exp_date, 'YYYYMMDD'), '********') >= '{work_day}'
    '''.format(work_day=workDay)
    rcsRows = fetch_oracle_data(oraConnect, sqlRouteClass)
    
    # ON src.source_id = rcs.source_id  合并后，增加2个字段，变成23个   
    statMergeRcsData = process_leftjoin(statMergeSrcData, rcsRows, [19], [0])
    # print("statMergeRcsData len={}, row1={}".format(len(statMergeRcsData), statMergeRcsData[0]) )

    # 2.4 关联产品信息
    sqlProduct = '''
        SELECT product_id, destination_id 
        FROM srdsms.r_pm_product
    '''
    prdRows = fetch_oracle_data(oraConnect, sqlProduct)

    # ON dst.destination_id = prd.destination_id 合并后，增加1个字段，变成25个
    statMergePrdData = process_leftjoin(statMergeRcsData, prdRows, [17], [1])
    # print("statMergePrdData len={}, row1={}".format(len(statMergePrdData), statMergePrdData[0]))

    # 2.5 关联CSR信息
    sqlCsr = '''
        SELECT customer_id, product_id, csr_commercial_id 
        FROM srdsms.r_pm_csr_commercial 
        WHERE eff_flag = 1
        AND to_char(begin_date, 'YYYYMMDD') <= '{work_day}'
        AND COALESCE(to_char(end_date, 'YYYYMMDD'), '********') >= '{work_day}'
    '''.format(work_day=workDay)
    csrRows = fetch_oracle_data(oraConnect, sqlCsr)

    # ON csr.customer_id = src.source_id AND csr.product_id = prd.product_id 合并后，增加3个字段，变成28个
    statMergeCsrData = process_leftjoin(statMergePrdData, csrRows, [19, 23], [0, 1])
    # print("statMergeCsrData len={}, row1={}".format(len(statMergeCsrData), statMergeCsrData[0]))

    # 3 生成插入记录
    insertRows = []
    for row in statMergeCsrData:
        if len(row) != 28:
            print("statMergeCsrData row len={}, row={}".format(len(row), row))
            continue
        # 关联表的相关字段的位置索引
        # destination_id --17
        # route_class_id --22
        # csr_commercial_id --27
        csrType = -1 if row[27] is None else 1   # CASE WHEN csr.csr_commercial_id IS NULL THEN -1 ELSE 1 END AS csr_type
        insertRow = (
            row[0], row[1], row[2], row[17], row[3], 
            row[22], row[5], row[6], row[5], row[6],
            csrType, row[7], row[8], row[9], row[10], 
            row[11], row[12], row[13], row[14], row[15], 
            row[16], row[15], row[16], 1
        )
        insertRows.append(insertRow)

    # 4. 插入数据到Oracle库
    if insertRows:
        sqlDel = 'DELETE FROM srdsms.r_stat_a2p_profit WHERE call_date = %(work_day)s'
        sqlInsert = '''
        INSERT INTO srdsms.r_stat_a2p_profit
        (call_date, in_account_id, out_account_id, std_dest_id, out_country, 
        route_class_id, in_charge, out_charge, in_fee, out_fee,
        csr_type, in_currency, out_currency, sm_cnt, dr_cnt, 
        in_settle_minutes, out_settle_minutes, in_sm_dr, out_sm_dr, 
        in_settle_rate, out_settle_rate, in_option_rate, out_option_rate, status)
        VALUES (:1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, :15, 
                :16, :17, :18, :19, :20, :21, :22, :23, :24)
        '''
        cur = oraConnect.cursor()
        cur.execute(sqlDel % {"work_day": workDay})
        cur.executemany(sqlInsert, insertRows)
        cur.close()
        dbConnect.commit()
        print("{workDay} r_stat_a2p_profit insert {len} records".format(workDay=workDay, len=len(insertRows)))
        logging.info("{workDay} r_stat_a2p_profit insert {len} records".format(workDay=workDay, len=len(insertRows)))


def fetch_oracle_data(oraConnect, sql):
    """从Oracle数据库查询数据"""
    cur = oraConnect.cursor()
    cur.execute(sql)
    rows = cur.fetchall()
    cur.close()
    return rows

def is_date_valid(check_date, start_date, end_date):
    """检查日期是否在有效期内"""
    if not start_date or not end_date:
        return False
    check_date = str(check_date)
    start_date = start_date.strftime('%Y%m%d') if start_date else '19700101'
    end_date = end_date.strftime('%Y%m%d') if end_date else '********'
    return start_date <= check_date <= end_date

if __name__ == '__main__':
    strProdPa = os.getenv('PROD_PA')

    if not strProdPa or len(strProdPa) < 2:
        print("no env: PROD_PA")
        sys.exit(0)

    strLogDir = os.path.join(strProdPa, "center/log/python")
    strLogFile = os.path.join(strLogDir, "doStat4Sms.log")
    strCfgDir = os.path.join(strProdPa, "center/config/python")
    strCfgFile = os.path.join(strCfgDir, "doStat4Sms.cfg")

    if not os.path.exists(strLogDir):
        os.makedirs(strLogDir)

    if not os.path.exists(strCfgDir):
        os.makedirs(strCfgDir)

    # 日志信息里增加进程ID
    # LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
    LOG_FORMAT = '%(asctime)s pid:%(process)d - %(levelname)s - %(message)s'
    DATE_FORMAT = "%m/%d/%Y %H:%M:%S %p"

    logging.basicConfig(filename=strLogFile, level=LOG_LEVEL, format=LOG_FORMAT, datefmt=DATE_FORMAT)

    try:
        dbConnect = get_gs_connect()
        oraConnect = get_o_connect()

        if len(sys.argv) > 1 and "history" == sys.argv[1]:
            cnt = getCntOfTask(dbConnect)

            if (cnt >= 0 and cnt < max_task_running):
                logging.info("history: start to run ...")
                listTaskInfo = getWorkDayk4Collect(dbConnect)
                logging.info("history: get {} tasks ...".format(len(listTaskInfo)))

                # 多个任务需要一起更新状态, 避免并发重复处理
                startCollect(dbConnect, listTaskInfo)

                # 取多个任务执行
                for taskInfo in listTaskInfo:
                    strWorkDay = taskInfo["work_day"]
                    oriStrAcctId = taskInfo["ori_acct_id"]
                    strAcctId =  taskInfo["acct_id"]
                    strDirFlag = taskInfo["dir_flag"]
                    redoTaskId = taskInfo["redo_task_id"]

                    exceptFlag = False

                    if len(strWorkDay) == 8:
                        try:
                            logging.info("history: begin to collect data on " + strWorkDay + " for acct: " + strAcctId)
                            doCollect(dbConnect, strWorkDay, strAcctId, strDirFlag)
                            logging.info("history: end to collect data on " + strWorkDay + " for acct: " + strAcctId)

                            # 计算阶梯资费
                            logging.info("history: begin to calc tier rate on " + strWorkDay + " for acct: " + strAcctId)
                            calcTierRate(dbConnect, strWorkDay, strAcctId, strDirFlag)
                            logging.info("history: end to calc tier rate on " + strWorkDay + " for acct: " + strAcctId)

                            # logging.info("history: begin to collect Profit on " + strWorkDay)
                            # # collectProfitDate(dbConnect, strWorkDay)
                            # collectProfitDateCrossDB(dbConnect,oraConnect, strWorkDay)
                            # logging.info("history: end to collect Profit on " + strWorkDay)
                        
                        except cx_Oracle.Error as exc:
                            error, = exc.args
                            print("type:", type(traceback.format_exc()))
                            print(traceback.format_exc())
                            logging.error(traceback.format_exc())
                            exceptFlag = True
                        except  psycopg2.Error as exc:
                            print("Gauss-Error-Code:", exc.pgcode)
                            print("Gauss-Error-Message:", exc.pgerror)
                            logging.error(traceback.format_exc())
                            exceptFlag = True
                        except Exception as err:
                            print(traceback.format_exc())
                            logging.error(traceback.format_exc())
                            exceptFlag = True
                        # 异常设置工单状态为-1
                        if  exceptFlag:
                            updateTaskState(dbConnect, redoTaskId, -1)
                            continue

                endCollect(dbConnect, listTaskInfo)
                
                logging.info("history: end")
        elif len(sys.argv) > 1 and "history" != sys.argv[1]:
            strWorkDay = sys.argv[1]

            logging.info("byday: start to run ...")

            # startCollect(dbConnect, strWorkDay)
            logging.info("byday: begin to collect data on " + strWorkDay)
            doCollect(dbConnect, strWorkDay)
            logging.info("byday: end to collect data on " + strWorkDay)
            # 计算阶梯资费
            logging.info("byday: begin to calc tier rate on " + strWorkDay)
            calcTierRate(dbConnect, strWorkDay)
            logging.info("byday: end to calc tier rate on " + strWorkDay)

            # logging.info("byday: begin to collect Profit on " + strWorkDay)
            # # collectProfitDate(dbConnect, strWorkDay)
            # collectProfitDateCrossDB(dbConnect,oraConnect, strWorkDay)
            # logging.info("byday: end to collect Profit on " + strWorkDay)

            logging.info("byday: end")
        else:
            logging.info("dayly: start to run ...")
            strLastTime = ""

            # read last time when do collect
            if os.path.exists(strCfgFile):
                with open(strCfgFile, 'r') as fileCfg:
                    strLastTime = fileCfg.read()

            if not strLastTime or len(strLastTime) != 14:
                strLastTime = time.strftime("%Y%m%d000000", time.localtime()) 

            strNow = getLastTime(dbConnect)

            # read date which need to collect
            rows = geTable4Collect(dbConnect, strLastTime)

            if not rows is None:
                for row in rows:
                    tableName = row[0]
                    strWorkDay = tableName[-8:]
                    logging.info("dayly: begin to collect data on " + strWorkDay)
                    doCollect(dbConnect, strWorkDay)
                    logging.info("dayly: end to collect data on " + strWorkDay)
                    # 计算阶梯资费
                    logging.info("dayly: begin to calc tier rate on " + strWorkDay)
                    calcTierRate(dbConnect, strWorkDay)
                    logging.info("dayly: end to calc tier rate on " + strWorkDay)

                    # logging.info("dayly: begin to collect Profit on " + strWorkDay)
                    # # collectProfitDate(dbConnect, strWorkDay)
                    # collectProfitDateCrossDB(dbConnect,oraConnect, strWorkDay)
                    # logging.info("dayly: end to collect Profit on " + strWorkDay)
            logging.info("dayly: end")

            # save last time -- 结束后保存当前时间
            with open(strCfgFile, 'w') as fileCfg:
                fileCfg.write(strNow)

    except cx_Oracle.Error as exc:
        error, = exc.args
        print("type:", type(traceback.format_exc()))
        print(traceback.format_exc())
        logging.error(traceback.format_exc())
    except  psycopg2.Error as exc:
        print("Gauss-Error-Code:", exc.pgcode)
        print("Gauss-Error-Message:", exc.pgerror)
        logging.error(traceback.format_exc())
    except Exception as err:
        print(traceback.format_exc())
        logging.error(traceback.format_exc())

    dbConnect.commit()
    oraConnect.commit()
    dbConnect.close()
    oraConnect.close()
    logging.info("do_stat_4_sms end")
