require "libluabaseD"
require "libluadebugD"
<% use("settle_Xdr_app.MXdr::SXdr") %>
<% use("settle_RplVarType.MRplVarType::SErrorInfo") %>
local logtrace = libluadebugD.report_trace
local logerror = libluadebugD.report_error

pSubXdr = nil;
pGsmInfo 	= nil;
pSubCommon	= nil;
pReserved	= nil;

-- 常量定义（从expr文件提取）
c_oper_mobile	= 2;	--中国移动
c_oper_rail	= 8;    --铁通
c_oper_special  = 9999; --特殊运营商
c_call_type_nodeal1 = "11"; --关口局转接局内话单
c_call_type_in = "1";    --入局
c_call_type_out = "2";   --出局
c_call_type_nodeal2 = "22";  --关口局转接局外话单
c_net_type_gsm = 2;     --网络类型为GSM
c_number_type_blank = 1;
c_number_type_zero  = 2;
c_number_type_comm  = 3;
c_number_type_error = 4;

function isNumber(words)
  if string.len(words) < 1 then
    return false
  end
  for i=1,string.len(words) do
    if string.byte(string.sub(words,i,i)) < 48 or string.byte(string.sub(words,i,i)) > 57 then
      return false
    end
  end
  return true
end

function bitwise_and(a, b)
    local result = 0
    local shift = 1
    while a > 0 or b > 0 do
        local bit_a = a % 2
        local bit_b = b % 2
        if bit_a == 1 and bit_b == 1 then
            result = result + shift
        end
        a = math.floor(a / 2)
        b = math.floor(b / 2)
        shift = shift * 2
    end
    return result
end
-- 国际号码处理
function fmtnbr(strUserNumber,type)
	local retrunValue=strUserNumber;
	local headNumber3=string.sub(strUserNumber,1,3);
	local headNumber2=string.sub(strUserNumber,1,2);
	local headNumber1=string.sub(strUserNumber,1,1);
	if type == 0 then 
		return strUserNumber;
	elseif type ==1 then 
		if headNumber3 =="179" 
			or headNumber3 == "193" 
			or headNumber2 == "12" 
			or headNumber1 =="0" then
		  return strUserNumber;
		 else
			return  "0"..strUserNumber;
		end
	elseif type ==2 then 
		if headNumber3 =="179" 
			or headNumber3 == "193" 
			or headNumber2 == "00" then 
			return strUserNumber;
		end
		if headNumber1 =="0" then 
			return "0"..strUserNumber;
		else
			return "00"..strUserNumber;
		end
			
	end;
	return retrunValue;
end;
	--处理msc
function error_deal(pSubCommon,sErrorCode,sErrorInfo)
	if iDoPrintInfo == 1    then
		logtrace('----------error_deal() begin-------------');
	end
	local pFileOpInfo = <%get_struct_value('pSubCommon',"MXdr::SSubCommon.FILE_OP_INFO") %>
	local iTreatFlag = <%get_struct_value('pFileOpInfo',"MFileOp::SFileOp.TREAT_FLAG") %>
	if iDoPrintInfo == 1    then
		logtrace('--TREAT_FLAG:'.. iTreatFlag );
	end

	-- 2012-09-11 modified by nijj for TRAC#56851-业务分析处理前,若TREAT_FLAG为1,则直接返回
	if iTreatFlag == 1	then
		return;
	else
		<%set_struct_value('pFileOpInfo', "MFileOp::SFileOp.TREAT_FLAG",1)%>
		<%set_struct_value('pFileOpInfo',"MFileOp::SFileOp.ERROR_CODE",sErrorCode) %>
		<%set_struct_value('pSubCommon',"MXdr::SSubCommon.ERROR_MESSAGE",sErrorInfo) %>
	end;

	if iDoPrintInfo == 1    then
		logtrace('----------error_deal() end-------------');
	end
end

function do_main()
	-- 变量初始化保持不变...
	
	-- 新增业务规则处理
	local l_date = string.sub(t_sSTART_TIME, 1, 8)
	local l_area = "10"  -- 默认值，需要根据实际MSC信息更新
	
	-- 处理MSC信息
	local l_switch_info = GsmSwitchInfo(t_sMSC)
	l_area = GsmSwitchInfoAreaCode(l_switch_info) or "10"
	t_sODN_REGION_CODE = l_area
	
	-- 处理原始文件名前缀（新增）
	if string.sub(s_original_file,1,10) == "N_GSM_PPYT" then
		temp_prov = 701
	elseif string.sub(s_original_file,1,10) == "N_GSM_PPXY" then
		temp_prov = 790
	-- ...其他省份前缀处理...
	end
	
	-- 呼叫类型判断（从expr移植）
	if t_sRAW_TAG == "2" or t_sRAW_TAG == "11" or t_sRAW_TAG == "A3" then
		t_sCALL_TYPE = c_call_type_in
	elseif t_sRAW_TAG == "10" or t_sRAW_TAG == "12" or t_sRAW_TAG == "A4" then
		t_sCALL_TYPE = c_call_type_out
	end
	
	-- 入中继处理（关键业务逻辑）
	if t_sTRUNK_IN ~= "" then
		local bGetInfo, trunkInfo = GsmRouterInfo(t_sMSC, t_sTRUNK_IN, l_date)
		if not bGetInfo then
			if t_sRAW_TAG == "2" or t_sRAW_TAG == "11" or t_sRAW_TAG == "A3" then
				error_deal(pSubCommon, "E4303", "NO CONFIG TRUNKID"..t_sTRUNK_IN)
			else
				t_iTRUNK_IN_OPER = c_oper_mobile
				t_sTRUNK_IN_AREA = l_area
				t_iTRUNK_IN_SERV = 0
			end
		else
			t_iTRUNK_IN_OPER = trunkInfo.SettlerId
			t_sTRUNK_IN_AREA = trunkInfo.AreaCode
			t_iTRUNK_IN_SERV = trunkInfo.InTrunkBusiId
		end
	end
	
	-- 出中继处理（关键业务逻辑）
	if t_sTRUNK_OUT ~= "" then
		local bGetInfo, trunkInfo = GsmRouterInfo(t_sMSC, t_sTRUNK_OUT, l_date)
		if not bGetInfo then
			if t_sRAW_TAG == "10" or t_sRAW_TAG == "12" or t_sRAW_TAG == "A4" then
				error_deal(pSubCommon, "E4304", "NO CONFIG TRUNKID"..t_sTRUNK_OUT)
			else
				t_iTRUNK_OUT_OPER = c_oper_mobile
				t_sTRUNK_OUT_AREA = l_area
				t_iTRUNK_OUT_SERV = 0
			end
		else
			t_iTRUNK_OUT_OPER = trunkInfo.SettlerId
			t_sTRUNK_OUT_AREA = trunkInfo.AreaCode
			t_iTRUNK_OUT_SERV = trunkInfo.OutTrunkBusiId
		end
	end
	
	-- 主被叫号码分析（核心逻辑）
	-- ...此处需要移植expr中的NPUserAnalysis/NumberAnalysisNew完整逻辑...
	
	-- 特殊用户处理（示例）
	if t_iODN_NET ~= 3 and string.sub(t_sODN_HOME_AREA,1,2) ~= "00" then
		local bGetInfo, numType, operId, areaCode = SpecialUserAnalyze(t_sODN_FIXED, t_sODN_HOME_AREA, l_date)
		if bGetInfo == 1 then
			t_iODN_NET = numType
			t_iODN_OPER = operId
		end
	end
	
	-- 资费计算（关键修改）
	t_iSIX_SEC = math.ceil(t_iDURATION / 6)
	t_iMINUTES = math.ceil(t_iDURATION / 60)
	t_iFIVE_MIN = math.ceil(t_iDURATION / 300)
	t_iAFTER_MINS = t_iMINUTES > 3 and t_iMINUTES - 3 or 0
	
	-- 保留字段处理...
	-- 结果设置保持不变...
	
	-- 新增融合地市特殊处理（示例）
	if tonumber(t_sODN_REGION_CODE) >= 701 and tonumber(t_sODN_REGION_CODE) <= 799 then
		-- 处理移动固话转铁通场景...
		-- 处理联通业务台资费...
	end
	
	return 0
end