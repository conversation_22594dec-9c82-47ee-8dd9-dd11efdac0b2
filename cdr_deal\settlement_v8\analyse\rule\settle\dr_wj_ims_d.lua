--移动拨打非移动

	require "libluabaseD"
   	require "libluadebugD"
	<% use("settle_Xdr_app.MXdr::SXdr") %>
	<% use("settle_RplVarType.MRplVarType::SErrorInfo") %>
    local logtrace = libluadebugD.report_trace
	local logerror = libluadebugD.report_error

	pSubXdr = nil;
	pGsmInfo 	= nil;
	pSubCommon	= nil;
	pReserved	= nil;

	--SXdr.SSubXdr.SGsmInfo
	--SXdr.SSubXdr.SSubCommon.SRatingResList
	local t_sErrMsg = "";
	local RplFileIdx 	= -1;
	local iDoPrintInfo	= 0;	--1:打印调试信息到log,	0:不打印调试信
	pFileOpInfo = nil;
	pOriCharge = nil;
	c_oper_mobile	= 2;	--中国移动
	c_oper_mobile_rail = 28;
	c_oper_rail = 8;
	c_oper_special  = 9999; --	特殊运营

	c_call_type_nodeal1 = "11"; --关口局转接局内话
	c_call_type_in = "01";    --入局
	c_call_type_out = "02";   --出局
	c_call_type_in_v = "3";    --入局视频
	c_call_type_out_v = "4";   --出局视频
	c_call_type_nodeal2 = "22";  --关口局转接局外话

	c_net_type_gsm = 2;     --       网络类型为GSM
	c_net_type_cdma = 3;    --	 网络类型为CDMA
	c_net_type_phs=4;
	c_vpn_trunk = 7;				--虚拟网中继类

	c_number_type_blank = 1;
	c_number_type_zero  = 2;
	c_number_type_comm  = 3;
	c_number_type_error = 4;


function isNumber(words)
  if string.len(words) < 1 then
    return false
  end
  for i=1,string.len(words) do
    if string.byte(string.sub(words,i,i)) < 48 or string.byte(string.sub(words,i,i)) > 57 then
      return false
    end
  end
  return true
end

function error_deal(pSubCommon,sErrorCode,sErrorInfo)
	if iDoPrintInfo == 1    then
		logtrace('----------error_deal() begin-------------');
	end
	local pFileOpInfo = <%get_struct_value('pSubCommon',"MXdr::SSubCommon.FILE_OP_INFO") %>
	local iTreatFlag = <%get_struct_value('pFileOpInfo',"MFileOp::SFileOp.TREAT_FLAG") %>
	if iDoPrintInfo == 1    then
		logtrace('--TREAT_FLAG:'.. iTreatFlag );
	end

	-- 2012-09-11 modified by nijj for TRAC#56851-业务分析处理前,若TREAT_FLAG为1,则直接返回
	if iTreatFlag == 1	then
		return;
	else
		<%set_struct_value('pFileOpInfo', "MFileOp::SFileOp.TREAT_FLAG",1)%>
		<%set_struct_value('pFileOpInfo',"MFileOp::SFileOp.ERROR_CODE",sErrorCode) %>
		<%set_struct_value('pSubCommon',"MXdr::SSubCommon.ERROR_MESSAGE",sErrorInfo) %>
	end;

	if iDoPrintInfo == 1    then
		logtrace('----------error_deal() end-------------');
	end
end

function do_main()

	pSubXdr = <%get_struct_value('PXdr',"MXdr::SXdr.TOTAL_XDR")%>
	pSubCommon	= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SUB_COMMON") %>
	pReserved   = <%get_sdl_ref('pSubXdr',"MXdr::SSubXdr.SUB_COMMON.RESERVE_FIELDS") %>
	pGsmInfo 	= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.GSM_INFO45") %>
--getfieldValue
	local t_settle_side1 = "0";
	local t_settle_side  = "0";
    local tenantId = <%get_struct_value('PXdr', "MXdr::SXdr.TENANT_ID")%>;
	local t_iORI_FILE_TYPE= <%get_struct_value('PXdr', "MXdr::SXdr.ORI_FILE_TYPE")%>;
	if t_iORI_FILE_TYPE == nil or  t_iORI_FILE_TYPE == "" then
		t_iORI_FILE_TYPE =0;
	end
	t_sDIST_FEE_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_REGION_CODE") %>
	if t_sDIST_FEE_CODE == nil or  t_sDIST_FEE_CODE == "" then
		t_sDIST_FEE_CODE =tenantId;
	end
	t_sODN_REGION_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_REGION_CODE") %>
	if t_sODN_REGION_CODE == nil or  t_sODN_REGION_CODE == "" then
		t_sODN_REGION_CODE ="0";
	end
	t_sTDN_REGION_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_REGION_CODE") %>
	if t_sTDN_REGION_CODE == nil or  t_sTDN_REGION_CODE == "" then
		t_sTDN_REGION_CODE ="0";
	end
	t_sODN_COUNTRY_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_COUNTRY_CODE") %>
	if t_sODN_COUNTRY_CODE == nil or  t_sODN_COUNTRY_CODE == "" then
		t_sODN_COUNTRY_CODE ="0";
	end
	t_sTDN_COUNTRY_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_COUNTRY_CODE") %>
	if t_sTDN_COUNTRY_CODE == nil or  t_sTDN_COUNTRY_CODE == "" then
		t_sTDN_COUNTRY_CODE ="0";
	end
	t_sODN_COUNTY_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_COUNTY_CODE") %>
	if t_sODN_COUNTY_CODE == nil or  t_sODN_COUNTY_CODE == "" then
		t_sODN_COUNTY_CODE ="0";
	end
	t_sTDN_COUNTY_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_COUNTY_CODE") %>
	if t_sTDN_COUNTY_CODE == nil or  t_sTDN_COUNTY_CODE == "" then
		t_sTDN_COUNTY_CODE ="0";
	end
	t_sCALL_TYPE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.CALL_TYPE") %>
	if t_sCALL_TYPE == nil or  t_sCALL_TYPE == "" then
		t_sCALL_TYPE = "0";
	end
	t_iDURATION=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.DURATION") %>
	if t_iDURATION == nil  or  t_iDURATION == "" then
		t_iDURATION = 0;
	end
	t_sODN=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN") %>
	if t_sODN == nil  or  t_sODN == ""  then
		t_sODN = "";
	end
	t_sTDN=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN") %>
	if t_sTDN == nil or  t_sTDN == ""  then
		t_sTDN = "";
	end
	t_sADN=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ADN") %>
	if t_sADN == nil or  t_sADN == ""  then
		t_sADN = "";
	end
	t_sODN_FIXED=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_FIXED") %>
	if t_sODN_FIXED == nil or  t_sODN_FIXED == ""  then
		t_sODN_FIXED = "";
	end
	t_sTDN_FIXED=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_FIXED") %>
	if t_sTDN_FIXED == nil or  t_sTDN_FIXED == ""  then
		t_sTDN_FIXED = "";
	end
	t_sADN_FIXED=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ADN_FIXED") %>
	if t_sADN_FIXED == nil or  t_sADN_FIXED == ""  then
		t_sADN_FIXED = "";
	end
	t_sSTART_TIME=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.START_TIME") %>
	if t_sSTART_TIME == nil or  t_sSTART_TIME == ""  then
		t_sSTART_TIME = "";
	end
	t_sFINISH_TIME=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.FINISH_TIME") %>
	if t_sFINISH_TIME == nil or  t_sFINISH_TIME == ""  then
		t_sFINISH_TIME = "";
	end

	t_iODN_ACC_TYPE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_TYPE") %>
	if t_iODN_ACC_TYPE == nil or  t_iODN_ACC_TYPE == ""  then
		t_iODN_ACC_TYPE = 0;
	end

	t_sODN_ACC_NO=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_NO") %>
	if t_sODN_ACC_NO == nil or  t_sODN_ACC_NO == ""  then
		t_sODN_ACC_NO = "";
	end
	t_iODN_ACC_OPER=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_OPER") %>
	if t_iODN_ACC_OPER == nil or  t_iODN_ACC_OPER == ""  then
		t_iODN_ACC_OPER = 0;
	end
	t_sODN_HOME_AREA=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_HOME_AREA") %>
	if t_sODN_HOME_AREA == nil or  t_sODN_HOME_AREA == ""  then
		t_sODN_HOME_AREA = "";
	end
	t_sODN_VISIT_AREA=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_VISIT_AREA") %>
	if t_sODN_VISIT_AREA == nil or  t_sODN_VISIT_AREA == ""  then
		t_sODN_VISIT_AREA = "";
	end
	t_iODN_OPER=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_OPER") %>
	if t_iODN_OPER == nil or  t_iODN_OPER == ""  then
		t_iODN_OPER = 0;
	end
	t_iODN_SERV=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_SERV") %>
	if t_iODN_SERV == nil or  t_iODN_SERV == "" then
		t_iODN_SERV = 0;
	end
	t_iODN_NET=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_NET") %>
	if t_iODN_NET == nil or  t_iODN_NET == "" then
		t_iODN_NET = 0;
	end
	t_iODN_ROAM=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ROAM") %>
	if t_iODN_ROAM == nil or  t_iODN_ROAM == "" then
		t_iODN_ROAM = 0;
	end
	t_iODN_LONG=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_LONG") %>
	if t_iODN_LONG == nil or  t_iODN_LONG == "" then
		t_iODN_LONG = 0;
	end
	t_iODN_TRADEMARK=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_TRADEMARK") %>
	if t_iODN_TRADEMARK == nil or  t_iODN_TRADEMARK == "" then
		t_iODN_TRADEMARK = 0;
	end
	t_iTDN_ACC_TYPE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_TYPE") %>
	if t_iTDN_ACC_TYPE == nil or  t_iTDN_ACC_TYPE == ""  then
		t_iTDN_ACC_TYPE = 0;
	end
	t_sTDN_ACC_NO=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_NO") %>
	if t_sTDN_ACC_NO == nil or  t_sTDN_ACC_NO == "" then
		t_sTDN_ACC_NO = 0;
	end
	t_iTDN_ACC_OPER=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_OPER") %>
	if t_iTDN_ACC_OPER == nil or  t_sODN == "" then
		t_iTDN_ACC_OPER = 0;
	end
	t_sTDN_HOME_AREA=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_HOME_AREA") %>
	if t_sTDN_HOME_AREA == nil or  t_sTDN_HOME_AREA == "" then
		t_sTDN_HOME_AREA = "";
	end
	t_sTDN_VISIT_AREA=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_VISIT_AREA") %>
	if t_sTDN_VISIT_AREA == nil or  t_sTDN_VISIT_AREA == "" then
		t_sTDN_VISIT_AREA = "";
	end
	t_iTDN_OPER=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_OPER") %>
	if t_iTDN_OPER == nil or  t_iTDN_OPER == "" then
		t_iTDN_OPER = 0;
	end
	t_iTDN_SERV=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_SERV") %>
	if t_iTDN_SERV == nil or  t_iTDN_SERV == "" then
		t_iTDN_SERV = 0;
	end
	t_iTDN_NET=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_NET") %>
	if t_iTDN_NET == nil or  t_iTDN_NET == "" then
		t_iTDN_NET = 0;
	end
	t_iTDN_ROAM=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ROAM") %>
	if t_iTDN_ROAM == nil or  t_iTDN_ROAM == "" then
		t_iTDN_ROAM = 0;
	end
	t_iTDN_LONG=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_LONG") %>
	if t_iTDN_LONG == nil or  t_iTDN_LONG == "" then
		t_iTDN_LONG = 0;
	end
	t_iTDN_TRADEMARK=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_TRADEMARK") %>
	if t_iTDN_TRADEMARK == nil or  t_iTDN_TRADEMARK == "" then
		t_iTDN_TRADEMARK = 0;
	end
	t_sDEAL_DATE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.DEAL_DATE") %>
	if t_sDEAL_DATE == nil or  t_sDEAL_DATE == "" then
		t_sDEAL_DATE = "";
	end
	t_sINPUT_DATE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.INPUT_DATE") %>
	if t_sINPUT_DATE == nil or  t_sINPUT_DATE == "" then
		t_sINPUT_DATE = "";
	end
	t_sRAW_TAG= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.RAW_TAG") %>
	if t_sRAW_TAG == nil or  t_sRAW_TAG == "" then
		t_sRAW_TAG = "";
	end
	t_iSEQ= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.SEQ") %>
	if t_iSEQ == nil or  t_iSEQ == "" then
		t_iSEQ = 0;
	end
	t_iTIME_SEG= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TIME_SEG") %>
	if t_iTIME_SEG == nil or  t_iTIME_SEG == "" then
		t_iTIME_SEG = 0;
	end
	t_sMSRN= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSRN") %>
	if t_sMSRN == nil or  t_sMSRN == "" then
		t_sMSRN = "";
	end
	t_sMSC= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSC") %>
	if t_sMSC == nil or  t_sMSC == "" then
		t_sMSC = "";
	end
	t_sLAC= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.LAC") %>
	if t_sLAC == nil or  t_sLAC == "" then
		t_sLAC = "";
	end
	t_sCELL= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.CELL") %>
	if t_sCELL == nil or  t_sCELL == "" then
		t_sCELL = "";
	end
	t_sTRUNK_IN= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN") %>
	if t_sTRUNK_IN == nil or  t_sTRUNK_IN == "" then
		t_sTRUNK_IN = "";
	end
	t_sTRUNK_OUT= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT") %>
	if t_sTRUNK_OUT == nil or  t_sTRUNK_OUT == "" then
		t_sTRUNK_OUT = "";
	end
	--t_iMSC_VENDOR= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSC_VENDOR") %>
	t_iMSC_VENDOR=2;
	t_iTRUNK_IN_OPER= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_OPER") %>
	if t_iTRUNK_IN_OPER == nil or  t_iTRUNK_IN_OPER == "" then
		t_iTRUNK_IN_OPER = 0;
	end
	t_sTRUNK_IN_AREA= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_AREA") %>
	if t_sTRUNK_IN_AREA == nil or  t_sTRUNK_IN_AREA == "" then
		t_sTRUNK_IN_AREA = "";
	end
	t_iTRUNK_IN_SERV= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_SERV") %>
	if t_iTRUNK_IN_SERV == nil or  t_iTRUNK_IN_SERV == "" then
		t_iTRUNK_IN_SERV = 0;
	end
	t_iTRUNK_OUT_OPER= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_OPER") %>
	if t_iTRUNK_OUT_OPER == nil or  t_iTRUNK_OUT_OPER == "" then
		t_iTRUNK_OUT_OPER = 0;
	end
	t_sTRUNK_OUT_AREA= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_AREA") %>
	if t_sTRUNK_OUT_AREA == nil or  t_sTRUNK_OUT_AREA == "" then
		t_sTRUNK_OUT_AREA = "";
	end
	t_iTRUNK_OUT_SERV= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_SERV") %>
	if t_iTRUNK_OUT_SERV == nil or  t_iTRUNK_OUT_SERV == ""  then
		t_iTRUNK_OUT_SERV = 0;
	end
	t_iSIX_SEC= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.SIX_SEC") %>
	if t_iSIX_SEC == nil or  t_iSIX_SEC == "" then
		t_iSIX_SEC = 0;
	end
	t_iMINUTES= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.MINUTES") %>
	if t_iMINUTES == nil or  t_iMINUTES == "" then
		t_iMINUTES = 0;
	end
	t_iIDLE_SIX_SECS= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.IDLE_SIX_SECS") %>
	if t_iIDLE_SIX_SECS == nil or  t_iIDLE_SIX_SECS == "" then
		t_iIDLE_SIX_SECS = 0;
	end
	t_iIDLE_MINUTES= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.IDLE_MINUTES") %>
	if t_iIDLE_MINUTES == nil or  t_iIDLE_MINUTES == "" then
		t_iIDLE_MINUTES = 0;
	end
	t_iFIVE_MIN= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.FIVE_MIN") %>
	if t_iFIVE_MIN == nil or  t_iFIVE_MIN == "" then
		t_iFIVE_MIN = 0;
	end
	t_iAFTER_MINS= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.AFTER_MINS") %>
	if t_iAFTER_MINS == nil or  t_iAFTER_MINS == "" then
		t_iAFTER_MINS = 0;
	end
	t_iLOCAL_TYPE= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.LOCAL_TYPE") %>
	if t_iLOCAL_TYPE == nil or  t_iLOCAL_TYPE == "" then
		t_iLOCAL_TYPE = 0;
	end
	t_iSERVICE_ID = <%get_struct_value('PXdr', "MXdr::SXdr.SERVICE_ID")%>
	if t_iSERVICE_ID == nil or  t_iSERVICE_ID == "" then
		t_iSERVICE_ID = 0;
	end
	t_iDR_TYPE = <%get_struct_value('PXdr', "MXdr::SXdr.DR_TYPE")%>
	if t_iDR_TYPE == nil or  t_iDR_TYPE == "" then
		t_iDR_TYPE = 0;
	end

	st_RESERVED1 = getsdlval(pReserved,"RESERVED1");
	if(st_RESERVED1 == nil or st_RESERVED1 == "")
	then
		st_RESERVED1 = "";
	end
    st_RESERVED2 = getsdlval(pReserved,"RESERVED2");
	if(st_RESERVED2 == nil or st_RESERVED2 == "")
	then
		st_RESERVED2 = "";
	end

	t_iSERVICE_ID = 45;
	if (t_iDR_TYPE <= 0)
	then
		t_iDR_TYPE = 4;
	end

    l_date = string.sub(t_sSTART_TIME, 0, 8);
    l_area = tenantId;
    t_sODN_REGION_CODE = l_area;
    prov_code = tenantId;
    
    --IBCF号码分析
   temp_tdn_prefix = "";
    if(string.sub(t_sTDN,1,5) == "12583") then
        temp_tdn_prefix = string.sub(t_sTDN,1,6);
        t_sTDN = string.sub(t_sTDN,7,string.len(t_sTDN));
    end
    l_Rn = string.sub(t_sTDN,1,4);
    if(l_Rn == "1241" or l_Rn == "1242" or l_Rn == "1243")
    then
        t_sTDN = string.sub(t_sTDN,5,string.len(t_sTDN));
    end
	tdnHead2=string.sub(t_sTDN,1,2)
	tdnHead4=string.sub(t_sTDN,1,4)	
	if tdnHead4 =="0086" then
		temp_tdn_prefix=temp_tdn_prefix..tdnHead4;
		t_sTDN=string.sub(t_sTDN,5,string.len(t_sTDN));
	end;
	if tdnHead2 =="86" then
		temp_tdn_prefix=temp_tdn_prefix..tdnHead2;
		t_sTDN=string.sub(t_sTDN,3,string.len(t_sTDN));
	end
    if t_iORI_FILE_TYPE == 691 then
        hw_number_b = st_RESERVED1;
        hw_number_c = st_RESERVED2;
        if string.len(hw_number_c) > 0 and string.len(hw_number_b) > 0 and hw_number_c ~= t_sTDN then
            t_sODN = hw_number_c;
        elseif string.len(hw_number_c) > 0 and string.len(hw_number_b) > 0 and hw_number_c == t_sTDN  then
            t_sODN = hw_number_b;
        elseif string.len(hw_number_c) == 0 and string.len(hw_number_b) > 0 and hw_number_b ~= t_sTDN then
            t_sODN = hw_number_b;
        end
    end

    --入中继分析
    if(t_sTRUNK_IN ~= "")
    then
        m_bGetInfo,m_iInTrunkBusiId,m_iOutTrunkBusiId,m_iSettlerId,m_iTollType,m_szAreaCode,m_szExpireDate,m_szSwitchId,m_szTrunkId,m_szValidDate = GsmRouterInfo("00000000",t_sTRUNK_IN, l_date);
        if(m_bGetInfo == 0)
        then
            t_sErrorNo  = "E4601";
            t_sErrMsg = "NO CONFIG TRUNKID, TRUNK_IN="..t_sTRUNK_IN;
            error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
        else
            t_iTRUNK_IN_OPER = m_iSettlerId;
            t_sTRUNK_IN_AREA = m_szAreaCode;
            t_iTRUNK_IN_SERV = m_iInTrunkBusiId;
            
            if(t_iTRUNK_IN_OPER ~= c_oper_mobile) then
                t_sErrorNo  = "E4602";
                t_sErrMsg = "TRUNK_IN_OPER IS ERROR,TRUNK_IN_OPER="..t_iTRUNK_IN_OPER;
                error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
            end
        end
    else
        t_sErrorNo  = "E4603";
        t_sErrMsg = "TRUNK_IN IS NULL";
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    --出中继
    if(t_sTRUNK_OUT ~= "")
    then
        m_bGetInfo,m_iInTrunkBusiId,m_iOutTrunkBusiId,m_iSettlerId,m_iTollType,m_szAreaCode,m_szExpireDate,m_szSwitchId,m_szTrunkId,m_szValidDate  = GsmRouterInfo("00000000",t_sTRUNK_OUT, l_date);
        if(m_bGetInfo == 0)
        then
            t_sErrorNo  = "E4604";
            t_sErrMsg = "NO CONFIG TRUNKID, TRUNK_OUT="..t_sTRUNK_OUT;
            error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
        else
            t_iTRUNK_OUT_OPER = m_iSettlerId;
            t_sTRUNK_OUT_AREA = m_szAreaCode;
            t_iTRUNK_OUT_SERV = m_iOutTrunkBusiId;
            
            if(t_iTRUNK_OUT_OPER == c_oper_mobile) then
                t_sErrorNo  = "E4605";
                t_sErrMsg = "TRUNK_OUT_OPER IS ERROR,TRUNK_OUT_OPER="..t_iTRUNK_OUT_OPER;
                error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
            end
        end
    else
        t_sErrorNo  = "E4606";
        t_sErrMsg = "TRUNK_OUT IS NULL";
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    
    --主叫号码分析
    if(t_sODN == "")
    then
        t_sODN = "0";
    end
    l_Rn = string.sub(t_sODN,1,4);
    if(l_Rn == "1241" or l_Rn == "1242" or l_Rn == "1243")
    then
        t_sODN = string.sub(t_sODN,5,string.len(t_sODN));
    end
    
    m_iNumberType,m_iAccessFlag,m_iAccessOperator,m_szAccessNumber,m_iNetWork,
    m_iSpecialType,m_iSpecialId,m_szSpecialNumber,m_szHomeCountryCode,m_iCountryType,
    m_szProvId,m_iProvDefaultOp,m_szHomeAreaCode,m_szHomeBureauCode,m_szHomeBusinessCode,
    m_szHomeRegionCode,m_szOriginalNumber,m_iOperatorParty = NumberAnalysis(t_sODN, 1, "", t_sSTART_TIME, l_area,45);
    if(m_iNumberType ~= c_number_type_error) then
        l_prov = m_szProvId;
        if tonumber(l_prov) == tonumber(prov_code) then
            t_sErrorNo  = "E4607";
            t_sErrMsg = "ODN PROV IS ERROR";
            error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
        end
        
        t_iODN_OPER = m_iOperatorParty;
        t_sODN_FIXED = m_szOriginalNumber;
        
        l_country_code = m_szHomeCountryCode;
        if(l_country_code == "") then
            l_country_code = "86";
        end
        if(l_country_code == "86") then
            t_sODN_HOME_AREA = m_szHomeAreaCode;
            if(t_sODN_HOME_AREA == "") then
                t_sODN_HOME_AREA = "10";
            end
        else
            t_sODN_HOME_AREA = "00"..l_country_code;
        end
        
        t_sODN_VISIT_AREA = t_sODN_HOME_AREA;
        t_iODN_NET = m_iNetWork;
        
       -- isFind,operId,netType,areaCode,portOutOperId,homeOperId = NpUserAnalyze(t_sODN,l_date);
		local isFind,t_iTODN_NET,t_iTODN_OPER,szPhoneId = FindNpInfo(t_sODN,l_date);
        --isFind=0;
        if isFind == 1 then
            t_iODN_OPER = t_iTODN_OPER;
            t_iODN_NET = t_iTODN_NET;
            --lo_port_out_oper_id = portOutOperId;
            --lo_home_oper_id = homeOperId;
        end
        
        if(t_iODN_NET == 1 and t_sODN_HOME_AREA == prov_code) then
            t_iODN_OPER = t_iTRUNK_IN_OPER;
        end
        
        if(t_iODN_OPER ~= c_oper_mobile) then
            t_sErrorNo  = "E4608";
            t_sErrMsg = "ODN OPER IS NOT MOBILE";
            error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
        end
        
        t_iODN_ACC_TYPE = m_iAccessFlag;
        
    else
        t_sErrorNo  = "E4609";
        t_sErrMsg = "ODN ERROR,ODN="..t_sODN;
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    
    --被叫号码分析
    if(t_sTDN == "") then
        t_sErrorNo  = "E4610";
        t_sErrMsg = "TDN IS NULL";
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    
    m_iNumberType,m_iAccessFlag,m_iAccessOperator,m_szAccessNumber,m_iNetWork,
    m_iSpecialType,m_iSpecialId,m_szSpecialNumber,m_szHomeCountryCode,m_iCountryType,
    m_szProvId,m_iProvDefaultOp,m_szHomeAreaCode,m_szHomeBureauCode,m_szHomeBusinessCode,
    m_szHomeRegionCode,m_szOriginalNumber,m_iOperatorParty = NumberAnalysis(t_sTDN, 2, "", t_sSTART_TIME, l_area,45);
    if(m_iNumberType ~= c_number_type_error) then
        l_prov = m_szProvId;
        if tonumber(l_prov) ~= tonumber(prov_code) then
            t_sErrorNo  = "E4611";
            t_sErrMsg = "TDN PROV IS ERROR,PROV_CODE="..l_prov;
            error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
        end
        
        t_iTDN_OPER = m_iOperatorParty;
        t_iTDN_ACC_OPER = m_iAccessOperator;
        t_iTDN_ACC_TYPE = m_iAccessFlag;
        t_sTDN_ACC_NO = m_szAccessNumber;
        t_sTDN_FIXED = m_szOriginalNumber;
        t_iTDN_NET = m_iNetWork;
        t_iTDN_SERV = m_iSpecialType;
        
        l_country_code = m_szHomeCountryCode;
        if(l_country_code == "") then
            l_country_code = "86";
        end
        if(l_country_code == "86") then
            t_sTDN_HOME_AREA = m_szHomeAreaCode;
            if(t_sTDN_HOME_AREA == "") then
                t_sTDN_HOME_AREA = "10";
            end
        else
            t_sTDN_HOME_AREA = "00"..l_country_code;
        end
        
        if(t_sTDN_HOME_AREA == "NNN" and (t_iTDN_OPER == 2 or t_iTDN_OPER == 3)) then
            t_sTDN_HOME_AREA = l_area;
        end
        if(t_sTDN_HOME_AREA == "NNN") then
            t_sErrorNo  = "E4612";
            t_sErrMsg = "TDN_HOME_AREA IS ERROR,TDN_HOME_AREA="..t_sTDN_HOME_AREA;
            error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
        end
        
        if(t_sTDN_VISIT_AREA == "") then
            t_sTDN_VISIT_AREA = t_sTDN_HOME_AREA;
        end
        
        t_iTDN_LONG = GetLongType(t_sTDN_HOME_AREA ,l_area,l_date);
        
        --isFind,operId,netType,areaCode,portOutOperId,homeOperId = NpUserAnalyze(t_sTDN,l_date);
		local isFind,t_iTTDN_NET,t_iTTDN_OPER,szPhoneId = FindNpInfo(t_sTDN,l_date);
        --isFind=0;
        if isFind == 1 then
            t_iTDN_OPER = t_iTTDN_OPER;
            t_iTDN_NET = t_iTTDN_NET;
            --lo_port_out_oper_id = portOutOperId;
            --lo_home_oper_id = homeOperId;
        end
        
        if(t_iTDN_NET == 1 and t_sTDN_HOME_AREA == prov_code) then
            t_iTDN_OPER = t_iTRUNK_OUT_OPER;
        end
        
        if(t_iTDN_ACC_TYPE == 0) then
            t_iTDN_ACC_TYPE = 1;
        end
        
        if(t_iTDN_ACC_OPER == 0) then
            t_iTDN_ACC_OPER = t_iTDN_OPER;
        end
        
        if((t_iTDN_OPER == 7 and t_iTDN_SERV ~= c_oper_mobile) 
            or t_iTDN_ACC_OPER == 6 or t_iTDN_ACC_OPER == 10
            or (t_iTDN_ACC_OPER == 7 and t_iTDN_ACC_OPER ~= c_oper_mobile)) then
            t_sErrorNo  = "E4613";
            t_sErrMsg = "ERR_IGNORE";
            error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
        end
        
        if(t_iTDN_OPER == c_oper_mobile) then
            t_sErrorNo  = "E4614";
            t_sErrMsg = "TDN OPER SHOULD NOT BE MOBILE";
            error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
        end
        
    else
        t_sErrorNo  = "E4615";
        t_sErrMsg = "TDN ERROR,TDN="..t_sTDN;
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    
    if(string.sub(t_sODN,1,1) == "0" and string.len(t_sODN) ~= 11) then
        t_sODN = t_sODN_FIXED;
    end
    
    t_sTDN = temp_tdn_prefix..t_sTDN
    
--set ananlysResultfieldValue
	if(t_sDIST_FEE_CODE ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.DIST_FEE_CODE",t_sDIST_FEE_CODE) %>
	end
	if(t_sODN_REGION_CODE ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_REGION_CODE",t_sODN_REGION_CODE) %>
	end
	if(t_sTDN_REGION_CODE ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_REGION_CODE",t_sTDN_REGION_CODE) %>
	end
	if(t_sODN_COUNTRY_CODE ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_COUNTRY_CODE",t_sODN_COUNTRY_CODE) %>
	end
	if(t_sTDN_COUNTRY_CODE ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_COUNTRY_CODE",t_sTDN_COUNTRY_CODE) %>
	end
	if(t_sODN_COUNTY_CODE ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_COUNTY_CODE",t_sODN_COUNTY_CODE) %>
	end
	if(t_sTDN_COUNTY_CODE ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_COUNTY_CODE",t_sTDN_COUNTY_CODE) %>
	end
	if(t_sCALL_TYPE ~= nil) then
	t_sCALL_TYPE=2
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.CALL_TYPE",t_sCALL_TYPE) %>
	end
	if(t_iDURATION ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.DURATION",t_iDURATION) %>
	end
	if(t_sODN ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN",t_sODN) %>
	end
	if(t_sTDN ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN",t_sTDN) %>
	end
	if(t_sADN ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ADN",t_sADN) %>
	end
	if(t_sODN_FIXED ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_FIXED",t_sODN_FIXED) %>
	end
	if(t_sTDN_FIXED ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_FIXED",t_sTDN_FIXED) %>
	end
	if(t_sADN_FIXED ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ADN_FIXED",t_sADN_FIXED) %>
	end
	if(t_sSTART_TIME ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.START_TIME",t_sSTART_TIME) %>
	end
	if(t_sFINISH_TIME ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.FINISH_TIME",t_sFINISH_TIME) %>
	end
	if(t_iODN_ACC_TYPE ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_TYPE",t_iODN_ACC_TYPE) %>
	end
	if(t_sODN_ACC_NO ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_NO",t_sODN_ACC_NO) %>
	end
	if(t_iODN_ACC_OPER ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_OPER",t_iODN_ACC_OPER) %>
	end
	if(t_sODN_HOME_AREA ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_HOME_AREA",t_sODN_HOME_AREA) %>
	end
	if(t_sODN_VISIT_AREA ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_VISIT_AREA",t_sODN_VISIT_AREA) %>
	end
	if(t_iODN_OPER ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_OPER",t_iODN_OPER) %>
	end
	if(t_iODN_SERV ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_SERV",t_iODN_SERV) %>
	end
	if(t_iODN_NET ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_NET",t_iODN_NET) %>
	end
	if(t_iODN_ROAM ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ROAM",t_iODN_ROAM) %>
	end
	if(t_iODN_LONG ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_LONG",t_iODN_LONG) %>
	end
	if(t_iODN_TRADEMARK ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_TRADEMARK",t_iODN_TRADEMARK) %>
	end
	if(t_iTDN_ACC_TYPE ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_TYPE",t_iTDN_ACC_TYPE) %>
	end
	if(t_sTDN_ACC_NO ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_NO",t_sTDN_ACC_NO) %>
	end
	if(t_iTDN_ACC_OPER ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_OPER",t_iTDN_ACC_OPER) %>
	end
	if(t_sTDN_HOME_AREA ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_HOME_AREA",t_sTDN_HOME_AREA) %>
	end
	if(t_sTDN_VISIT_AREA ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_VISIT_AREA",t_sTDN_VISIT_AREA) %>
	end
	if(t_iTDN_OPER ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_OPER",t_iTDN_OPER) %>
	end
	if(t_iTDN_SERV ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_SERV",t_iTDN_SERV) %>
	end
	if(t_iTDN_NET ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_NET",t_iTDN_NET) %>
	end
	if(t_iTDN_ROAM ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ROAM",t_iTDN_ROAM) %>
	end
	if(t_iTDN_LONG ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_LONG",t_iTDN_LONG) %>
	end
	if(t_iTDN_TRADEMARK ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_TRADEMARK",t_iTDN_TRADEMARK) %>
	end
	if(t_sDEAL_DATE ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.DEAL_DATE",t_sDEAL_DATE) %>
	end
	if(t_sINPUT_DATE ~= nil) then
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.INPUT_DATE",t_sINPUT_DATE) %>
	end

	if(t_sRAW_TAG ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.RAW_TAG",t_sRAW_TAG) %>
	end
	if(t_iSEQ ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.SEQ",t_iSEQ) %>
	end
	if(t_iTIME_SEG ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TIME_SEG",t_iTIME_SEG) %>
	end
	if(t_sMSRN ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSRN",t_sMSRN) %>
	end
	if(t_sMSC ~= nil) then
	t_sMSC = ""
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSC",t_sMSC) %>
	end
	if(t_sLAC ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.LAC",t_sLAC) %>
	end
	if(t_sCELL ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.CELL",t_sCELL) %>
	end
	if(t_sTRUNK_IN ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN",t_sTRUNK_IN) %>
	end
	if(t_sTRUNK_OUT ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT",t_sTRUNK_OUT) %>
	end
	if(t_iMSC_VENDOR ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSC_VENDOR",t_iMSC_VENDOR) %>
	end
	if(t_iTRUNK_IN_OPER ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_OPER",t_iTRUNK_IN_OPER) %>
	end
	if(t_sTRUNK_IN_AREA ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_AREA",t_sTRUNK_IN_AREA) %>
	end
	if(t_iTRUNK_IN_SERV ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_SERV",t_iTRUNK_IN_SERV) %>
	end
	if(t_iTRUNK_OUT_OPER ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_OPER",t_iTRUNK_OUT_OPER) %>
	end
	if(t_sTRUNK_OUT_AREA ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_AREA",t_sTRUNK_OUT_AREA) %>
	end
	if(t_iTRUNK_OUT_SERV ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_SERV",t_iTRUNK_OUT_SERV) %>
	end
	if(t_iSIX_SEC ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.SIX_SEC",t_iSIX_SEC) %>
	end
	if(t_iMINUTES ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.MINUTES",t_iMINUTES) %>
	end
	if(t_iIDLE_SIX_SECS ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.IDLE_SIX_SECS",t_iIDLE_SIX_SECS) %>
	end
	if(t_iIDLE_MINUTES ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.IDLE_MINUTES",t_iIDLE_MINUTES) %>
	end
	if(t_iFIVE_MIN ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.FIVE_MIN",t_iFIVE_MIN) %>
	end
	if(t_iAFTER_MINS ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.AFTER_MINS",t_iAFTER_MINS) %>
	end
	if(t_iLOCAL_TYPE ~= nil) then
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.LOCAL_TYPE",t_iLOCAL_TYPE) %>
	end
	if(t_settle_side ~= nil) then
	<%set_struct_value('PXdr', "MXdr::SXdr.SETTLE_SIDE",t_settle_side)%>
	end
	if(t_iSERVICE_ID ~= nil) then
	<%set_struct_value('PXdr', "MXdr::SXdr.SERVICE_ID",t_iSERVICE_ID)%>
	end
	if(t_iDR_TYPE ~= nil) then
	<%set_struct_value('PXdr', "MXdr::SXdr.DR_TYPE",t_iDR_TYPE)%>
	end

    if st_RESERVED1 ~=	nil then
		setsdlval(pReserved,"RESERVED2",st_RESERVED1)
	end
	if st_RESERVED2 ~=	nil then
		setsdlval(pReserved,"RESERVED2",st_RESERVED2)
	end
	if t_settle_side  ~= 8 then
		if t_settle_side1 ~=nil or t_settle_side1~="" or t_settle_side1 ~="0" or t_settle_side1~=0
		then
			local pSubSettleSideList 	= <%get_sdl_ref('PXdr',"MXdr::SXdr.SUB_SETTLE_SIDE")%>;
			setsdllen(pSubSettleSideList,1);
			local pSettleSide  = getsdl(pSubSettleSideList, 1);
			<%set_struct_value('pSettleSide', "MXdr::SSubSettleSide.settle_side", t_settle_side1)%>;
		end
	end
	return 0;
end
