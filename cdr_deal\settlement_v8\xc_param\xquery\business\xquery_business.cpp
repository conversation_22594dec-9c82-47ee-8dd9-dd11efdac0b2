﻿#include "xquery_business.h"
#include "cdk_lock.h"
#include "cdk_threadlocal.h"
#include "cdk/foundation/cdk_aitime.h"
#include "cdk_string.h"
#include<sstream>
#include "cdk_os.h"

void to_upper(char *szDst, const char *szSrc)
{
    if(szDst==NULL||szSrc==NULL) return;
    
    while(*szSrc!='\0')
    {
        *szDst=toupper(*szSrc);
        ++szSrc;
        ++szDst;
    }
    *szDst=*szSrc;
}

void to_lower(char *szDst, const char *szSrc)
{
    if(szDst==NULL||szSrc==NULL) return;
    
    while(*szSrc!='\0')
    {
        *szDst=tolower(*szSrc);
        ++szSrc;
        ++szDst;
    }
    *szDst=*szSrc;
}

int32 odac_app_lookup_CBpsAcccodeRel(
    const xc::CSnapshot& cSnapShot,
    const char * cszSpCode,
    const char * cszOperatorCode,
    CBpsAcccodeRel &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszSpCode << "!"
                << cszOperatorCode;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsAcccodeRel::Type> cQueryHolder(cSnapShot,CBpsAcccodeRel::GetContainerName());
        CBpsAcccodeRel::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAcccodeRel::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAccessNumber(
    const xc::CSnapshot& cSnapShot,
    const char * cszAccessNumber,
    const time_t& tm,
    CBpsAccessNumber &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< "0" << cszAccessNumber;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsAccessNumber::Type> cQueryHolder(cSnapShot,CBpsAccessNumber::GetContainerName());
        CBpsAccessNumber::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAccessNumber::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsAccessNumber>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddCardFeeCodeSeg(
    const xc::CSnapshot& cSnapShot,
    const char * cszStartMsisdn,
    AISTD list<CBpsAddCardFeeCodeSeg> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszStartMsisdn;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsAddCardFeeCodeSeg::Type> cQueryHolder(cSnapShot,CBpsAddCardFeeCodeSeg::GetContainerName());
        CBpsAddCardFeeCodeSeg::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddCardFeeCodeSeg::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                for(;!iter.eof();++iter)
                {
                    lst.push_back(iter.value());
                }
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddFilenametomsc(
    const xc::CSnapshot& cSnapShot,
    const char * cszFileName,
    const time_t &tm,
    CBpsAddFilenametomsc &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszFileName;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsAddFilenametomsc::Type> cQueryHolder(cSnapShot,CBpsAddFilenametomsc::GetContainerName());
        CBpsAddFilenametomsc::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddFilenametomsc::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal=iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddISmsRate(
    const xc::CSnapshot& cSnapShot,
    const char * cszCountryCode,
    const time_t& tm,
    CBpsAddISmsRate &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszCountryCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsAddISmsRate::Type> cQueryHolder(cSnapShot,CBpsAddISmsRate::GetContainerName());
        CBpsAddISmsRate::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddISmsRate::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsAddISmsRate>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddJudgeZone(
    const xc::CSnapshot& cSnapShot,
    const char * cszNumberPrefix,
    const time_t& tm,
    CBpsAddJudgeZone &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszNumberPrefix;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsAddJudgeZone::Type> cQueryHolder(cSnapShot,CBpsAddJudgeZone::GetContainerName());
        CBpsAddJudgeZone::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddJudgeZone::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsAddJudgeZone>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddMmmCode(
    const xc::CSnapshot& cSnapShot,
    const char * cszMmmCode,
    const time_t& tm,
    CBpsAddMmmCode &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszMmmCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsAddMmmCode::Type> cQueryHolder(cSnapShot,CBpsAddMmmCode::GetContainerName());
        CBpsAddMmmCode::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddMmmCode::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsAddMmmCode>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddMobileSeg(
    const xc::CSnapshot& cSnapShot,
    const char * cszMCodeSeg,
    const time_t& tm,
    CBpsAddMobileSeg &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszMCodeSeg;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsAddMobileSeg::Type> cQueryHolder(cSnapShot,CBpsAddMobileSeg::GetContainerName());
        CBpsAddMobileSeg::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddMobileSeg::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsAddMobileSeg>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddOperatorDescription(
    const xc::CSnapshot& cSnapShot,
    const int32& iDrType,
    const int32& iOperId,
    const time_t& tm,
    CBpsAddOperatorDescription &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< iDrType << "|"
                <<iOperId;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsAddOperatorDescription::Type> cQueryHolder(cSnapShot,CBpsAddOperatorDescription::GetContainerName());
        CBpsAddOperatorDescription::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddOperatorDescription::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsAddOperatorDescription>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddOperator(
    const xc::CSnapshot& cSnapShot,
    const char * cszOperCode,
    const time_t& tm,
    AISTD list<CBpsAddOperator> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszOperCode;
        xc::CQueryHolder<CBpsAddOperator::Type> cQueryHolder(cSnapShot,CBpsAddOperator::GetContainerName());
        CBpsAddOperator::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddOperator::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsAddOperator>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                for(;!iter.eof();++iter)
                {
                    lst.push_back(iter.value());
                }
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }    
}

int32 odac_app_lookup_CBpsAddPlatformBusiDesc(
    const xc::CSnapshot& cSnapShot,
    const int32& iAccCode,
    const char * cszOperCode,
    CBpsAddPlatformBusiDesc &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< iAccCode << "|"
                << cszOperCode;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsAddPlatformBusiDesc::Type> cQueryHolder(cSnapShot,CBpsAddPlatformBusiDesc::GetContainerName());
        CBpsAddPlatformBusiDesc::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddPlatformBusiDesc::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddPlatformBusiDescByAccCode(
    const xc::CSnapshot& cSnapShot,
    const int32& iAccCode,
    CBpsAddPlatformBusiDesc &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< iAccCode;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsAddPlatformBusiDesc::Type> cQueryHolder(cSnapShot,CBpsAddPlatformBusiDesc::GetContainerName());
        CBpsAddPlatformBusiDesc::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddPlatformBusiDesc::KEY2,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddSpBusiDescBySpCode(
    const xc::CSnapshot& cSnapShot,
    const char * cszSpCode,
    const time_t& tm,
    AISTD list<CBpsAddSpBusiDesc> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszSpCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsAddSpBusiDesc::Type> cQueryHolder(cSnapShot,CBpsAddSpBusiDesc::GetContainerName());
        CBpsAddSpBusiDesc::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddSpBusiDesc::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsAddSpBusiDesc>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                for(;!iter.eof();++iter)
                {
                    lst.push_back(iter.value());
                }
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddSpBusiDescByServCode2(
    const xc::CSnapshot& cSnapShot,
    const char * cszServCode,
    const time_t& tm,
    AISTD list<CBpsAddSpBusiDesc> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszServCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsAddSpBusiDesc::Type> cQueryHolder(cSnapShot,CBpsAddSpBusiDesc::GetContainerName());
        CBpsAddSpBusiDesc::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddSpBusiDesc::KEY2,sstrKey.str().c_str(),xload::base_cmp_func<CBpsAddSpBusiDesc>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                for(;!iter.eof();++iter)
                {
                    lst.push_back(iter.value());
                }
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddSpBusiDescByBusiCode(
    const xc::CSnapshot& cSnapShot,
    const char * cszBusiCode,
    const time_t& tm,
    CBpsAddSpBusiDesc &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszBusiCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsAddSpBusiDesc::Type> cQueryHolder(cSnapShot,CBpsAddSpBusiDesc::GetContainerName());
        CBpsAddSpBusiDesc::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddSpBusiDesc::KEY3,sstrKey.str().c_str(),xload::base_cmp_func<CBpsAddSpBusiDesc>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddSpProd(
    const xc::CSnapshot& cSnapShot,
    const char * cszBillMonth,
    CBpsAddSpProd &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszBillMonth;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsAddSpProd::Type> cQueryHolder(cSnapShot,CBpsAddSpProd::GetContainerName());
        CBpsAddSpProd::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddSpProd::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsAddSpSms(
    const xc::CSnapshot& cSnapShot,
    const char * cszServCode,
    CBpsAddSpSms &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszServCode;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsAddSpSms::Type> cQueryHolder(cSnapShot,CBpsAddSpSms::GetContainerName());
        CBpsAddSpSms::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsAddSpSms::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsBorderRoam(
    const xc::CSnapshot &cSnapShot,
    const char *cszMscId,
    const char *cszLacId,
    const char *cszCellId,
    const char *cszAreaCode,
    const time_t &tm,
    CBpsBorderRoam &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszMscId << ":"
                << cszLacId << ":"
                << cszCellId << ":"
                << cszAreaCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsBorderRoam::Type> cQueryHolder(cSnapShot,CBpsBorderRoam::GetContainerName());
        CBpsBorderRoam::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsBorderRoam::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsBorderRoam>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsBsmsServicecode(
    const xc::CSnapshot& cSnapShot,
    const char *cszServType,
    const char *cszServCode,
    const time_t &tm,
    AISTD list<CBpsBsmsServicecode> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszServType << ":"
                << cszServCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsBsmsServicecode::Type> cQueryHolder(cSnapShot,CBpsBsmsServicecode::GetContainerName());
        CBpsBsmsServicecode::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsBsmsServicecode::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsBsmsServicecode>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
            for(;!iter.eof();++iter)
            {
                lst.push_back(iter.value());
            }
            LOG_TRACE("Success!");
            return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsBusinessAreaRel(
    const xc::CSnapshot &cSnapShot,
    const char *cszAreaCode,
    const char *cszBusinessAreaCode1,
    const char *cszBusinessAreaCode2,
    CBpsBusinessAreaRel &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszAreaCode << "!"
                << cszBusinessAreaCode1 << "!"
                <<cszBusinessAreaCode2;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsBusinessAreaRel::Type> cQueryHolder(cSnapShot,CBpsBusinessAreaRel::GetContainerName());
        CBpsBusinessAreaRel::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsBusinessAreaRel::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsCardHomeProv(
    const xc::CSnapshot &cSnapShot,
    const char *cszCardCode,
    const time_t &tm,
    CBpsCardHomeProv &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszCardCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsCardHomeProv::Type> cQueryHolder(cSnapShot,CBpsCardHomeProv::GetContainerName());
        CBpsCardHomeProv::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsCardHomeProv::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsCardHomeProv>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsCardPrefixByCardPrefix(
    const xc::CSnapshot &cSnapShot,
    const char *cszCardPrefix,
    CBpsCardPrefix &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszCardPrefix;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsCardPrefix::Type> cQueryHolder(cSnapShot,CBpsCardPrefix::GetContainerName());
        CBpsCardPrefix::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsCardPrefix::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsCardPrefix(
    const xc::CSnapshot &cSnapShot,
    const char *cszCardPrefix,
    const char *cszCardType,
    CBpsCardPrefix &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszCardPrefix << "!"
                << cszCardType;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsCardPrefix::Type> cQueryHolder(cSnapShot,CBpsCardPrefix::GetContainerName());
        CBpsCardPrefix::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsCardPrefix::KEY2,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsCarrierImsi(
    const xc::CSnapshot &cSnapShot,
    const char *cszImsiCode,
    CBpsCarrierImsi &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszImsiCode;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsCarrierImsi::Type> cQueryHolder(cSnapShot,CBpsCarrierImsi::GetContainerName());
        CBpsCarrierImsi::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsCarrierImsi::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsCcm(
    const xc::CSnapshot &cSnapShot,
    const char *cszCcmIp,
    CBpsCcm &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszCcmIp;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsCcm::Type> cQueryHolder(cSnapShot,CBpsCcm::GetContainerName());
        CBpsCcm::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsCcm::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsChannelType(
    const xc::CSnapshot &cSnapShot,
    const int32 &iService,
    const char *cszCodec,
    const int32 &iDirection,
    CBpsChannelType &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< iService << "!"
                << cszCodec
                << iDirection;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsChannelType::Type> cQueryHolder(cSnapShot,CBpsChannelType::GetContainerName());
        CBpsChannelType::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsChannelType::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsCiContentData(
    const xc::CSnapshot &cSnapShot,
    const char *cszContent,
    const time_t &tm,
    CBpsCiContentData &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszContent;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsCiContentData::Type> cQueryHolder(cSnapShot,CBpsCiContentData::GetContainerName());
        CBpsCiContentData::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsCiContentData::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsCiContentData>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsFreeGsmRouter(
    const xc::CSnapshot &cSnapShot,
    const char *cszMscId,
    const char *cszTrunkId,
    const time_t &tm,
    CBpsFreeGsmRouter &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszMscId << "!"
                << cszTrunkId;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsFreeGsmRouter::Type> cQueryHolder(cSnapShot,CBpsFreeGsmRouter::GetContainerName());
        CBpsFreeGsmRouter::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsFreeGsmRouter::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsFreeGsmRouter>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsGprsIpaddrInfo(
    const xc::CSnapshot &cSnapShot,
    const char *cszProvCode,
    const time_t &tm,
    AISTD list<CBpsGprsIpaddrInfo> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszProvCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsGprsIpaddrInfo::Type> cQueryHolder(cSnapShot,CBpsGprsIpaddrInfo::GetContainerName());
        CBpsGprsIpaddrInfo::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsGprsIpaddrInfo::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsGprsIpaddrInfo>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                for(;!iter.eof();++iter)
                {
                    lst.push_back(iter.value());
                }
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsGsmMsc(
    const xc::CSnapshot &cSnapShot,
    const char *cszMscId,
    const time_t &tm,
    CBpsGsmMsc &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszMscId;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsGsmMsc::Type> cQueryHolder(cSnapShot,CBpsGsmMsc::GetContainerName());
        CBpsGsmMsc::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsGsmMsc::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsGsmMsc>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsGsmRouterByMscIdAndTrunkId(
    const xc::CSnapshot &cSnapShot,
    const char *cszMscId,
    const char *cszTrunkId,
    const time_t &tm,
    CBpsGsmRouter &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszMscId << "!"
                << cszTrunkId;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsGsmRouter::Type> cQueryHolder(cSnapShot,CBpsGsmRouter::GetContainerName());
        CBpsGsmRouter::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsGsmRouter::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsGsmRouter>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsGsmRouter(
    const xc::CSnapshot &cSnapShot,
    const char *cszMscId,
    const char *cszTrunkId,
    const char *cszAreaCode,
    const time_t &tm,
    AISTD list<CBpsGsmRouter> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszMscId << "!"
                << cszTrunkId << "!"
                << cszAreaCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsGsmRouter::Type> cQueryHolder(cSnapShot,CBpsGsmRouter::GetContainerName());
        CBpsGsmRouter::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsGsmRouter::KEY2,sstrKey.str().c_str(),xload::base_cmp_func<CBpsGsmRouter>(tm));
        if(iter.eof())
        {
            LOG_TRACE("No record!");
            return -1;
        }else
        {
            for(;!iter.eof();++iter)
            {
                lst.push_back(iter.value());
                LOG_TRACE("Success! %d", iter.value().get_settlerId());
            }
            LOG_TRACE("Success! %d", lst.size());
            
            
            return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
        

}
int32 odac_app_lookup_CBpsHlrTrademark(
    const xc::CSnapshot& cSnapShot,
    const char * cszHlrCode,
    CBpsHlrTrademark &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszHlrCode;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsHlrTrademark::Type> cQueryHolder(cSnapShot,CBpsHlrTrademark::GetContainerName());
        CBpsHlrTrademark::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsHlrTrademark::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsHlr(
    const xc::CSnapshot& cSnapShot,
    const char * cszHlrCode,
    const time_t& tm,
    CBpsHlr &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszHlrCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsHlr::Type> cQueryHolder(cSnapShot,CBpsHlr::GetContainerName());
        CBpsHlr::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsHlr::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsHlr>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsImsiHlrRegular(
    const xc::CSnapshot& cSnapShot,
    const char * cszImsiHead,
    const time_t& tm,
//    AISTD list<settle::ANALYSE_GROUP::CBpsImsiHlrRegular> &lst
    AISTD list<CBpsImsiHlrRegular> &lst
)
{

    AISTD stringstream sstrKey;
    sstrKey<< cszImsiHead;
    LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
    xc::CQueryHolder<CBpsImsiHlrRegular::Type> cQueryHolder(cSnapShot,CBpsImsiHlrRegular::GetContainerName());
    CBpsImsiHlrRegular::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsImsiHlrRegular::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsImsiHlrRegular>(tm));
    if(iter.eof())
    {
        LOG_TRACE("No record!");
        return -1;
    }else
    {
        for(;!iter.eof();++iter)
        {
            lst.push_back(iter.value());
        }
        LOG_TRACE("Success! %d", lst.size());
    }
    return 0;
}

int32 odac_app_lookup_CBpsImsiNumber(
    const xc::CSnapshot &cSnapShot,
    const char *cszStartImsi,
    const time_t &tm,
    CBpsImsiNumber &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszStartImsi;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsImsiNumber::Type> cQueryHolder(cSnapShot,CBpsImsiNumber::GetContainerName());
        CBpsImsiNumber::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsImsiNumber::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsImsiNumber>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsImsiOperInfo(
    const xc::CSnapshot &cSnapShot,
    const char *cszImsiCode,
    const time_t &tm,
    CBpsImsiOperInfo &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszImsiCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsImsiOperInfo::Type> cQueryHolder(cSnapShot,CBpsImsiOperInfo::GetContainerName());
        CBpsImsiOperInfo::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsImsiOperInfo::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsImsiOperInfo>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsIrUserLimit(
    const xc::CSnapshot &cSnapShot,
    const char* pPartnerId,
    const char *cszLimitType,
    CBpsIrUserLimit &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< pPartnerId << "!"
                << cszLimitType;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsIrUserLimit::Type> cQueryHolder(cSnapShot,CBpsIrUserLimit::GetContainerName());
        CBpsIrUserLimit::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsIrUserLimit::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsIsmg(
    const xc::CSnapshot &cSnapShot,
    const char *cszIsmgId,
    CBpsIsmg &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszIsmgId;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsIsmg::Type> cQueryHolder(cSnapShot,CBpsIsmg::GetContainerName());
        CBpsIsmg::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsIsmg::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsKoreaRoamMsisdn(
    const xc::CSnapshot &cSnapShot,
    const char *cszRoamMsisdn,
    CBpsKoreaRoamMsisdn &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszRoamMsisdn;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsKoreaRoamMsisdn::Type> cQueryHolder(cSnapShot,CBpsKoreaRoamMsisdn::GetContainerName());
        CBpsKoreaRoamMsisdn::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsKoreaRoamMsisdn::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsLac(
    const xc::CSnapshot &cSnapShot,
    const char *cszLac,
    const time_t &tm,
    CBpsLac &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszLac;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsLac::Type> cQueryHolder(cSnapShot,CBpsLac::GetContainerName());
        CBpsLac::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsLac::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsLac>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsMisnNumseg(
    const xc::CSnapshot &cSnapShot,
    const char *szNumberHead,
    CBpsMisnNumseg &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< szNumberHead;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsMisnNumseg::Type> cQueryHolder(cSnapShot,CBpsMisnNumseg::GetContainerName());
        CBpsMisnNumseg::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsMisnNumseg::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsMscCode(
    const xc::CSnapshot &cSnapShot,
    const char *cszTrunkGroup,
    const char *cszTmpMscCode,
    CBpsMscCode &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszTrunkGroup << "!"
                << cszTmpMscCode;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsMscCode::Type> cQueryHolder(cSnapShot,CBpsMscCode::GetContainerName());
        CBpsMscCode::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsMscCode::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

/*
	// delete by zhangxj7 bps_np_use change to MDB SERVER
	int32 odac_app_lookup_CBpsNpUser(
    const xc::CSnapshot &cSnapShot,
    const char *cszSerialNumber,
    const time_t &tm,
    CBpsNpUser &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszSerialNumber;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsNpUser::Type> cQueryHolder(cSnapShot,CBpsNpUser::GetContainerName());
        CBpsNpUser::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsNpUser::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsNpUser>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}*/

int32 odac_app_lookup_CBpsPstnNumseg(
    const xc::CSnapshot &cSnapShot,
    const char *cszAreaCode,
    const char *cszNumberHead,
    const time_t &tm,
    CBpsPstnNumseg &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszAreaCode
                << cszNumberHead;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsPstnNumseg::Type> cQueryHolder(cSnapShot,CBpsPstnNumseg::GetContainerName());
        CBpsPstnNumseg::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsPstnNumseg::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsPstnRouter(
    const xc::CSnapshot &cSnapShot,
    const char *cszSwitchId,
    const char *cszTrunkId,
    const time_t &tm,
    CBpsPstnRouter &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszSwitchId << "!"
                << cszTrunkId;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsPstnRouter::Type> cQueryHolder(cSnapShot,CBpsPstnRouter::GetContainerName());
        CBpsPstnRouter::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsPstnRouter::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsPstnRouter>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsPstnSwitch(
    const xc::CSnapshot &cSnapShot,
    const char *cszSwitchId,
    CBpsPstnSwitch &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszSwitchId;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsPstnSwitch::Type> cQueryHolder(cSnapShot,CBpsPstnSwitch::GetContainerName());
        CBpsPstnSwitch::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsPstnSwitch::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsRateType(
    const xc::CSnapshot &cSnapShot,
    const char *cszAccessNumber,
    const char *cszOriZoneCode,
    const char *cszDestZoneCode,
    const int32 &iTollGroupId,
    const time_t &tm,
    CBpsRateType &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszAccessNumber << "!"
                << cszOriZoneCode << "!"
                << cszDestZoneCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsRateType::Type> cQueryHolder(cSnapShot,CBpsRateType::GetContainerName());
        CBpsRateType::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsRateType::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsRateType>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsSpecialNumber(
    const xc::CSnapshot &cSnapShot,
    const int32 &iServiceId,
    const char *cszSpecialNumber,
    const time_t &tm,
    AISTD list<CBpsSpecialNumber> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< iServiceId
                << cszSpecialNumber;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsSpecialNumber::Type> cQueryHolder(cSnapShot,CBpsSpecialNumber::GetContainerName());
        CBpsSpecialNumber::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsSpecialNumber::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsSpecialNumber>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
            for(;!iter.eof();++iter)
            {
                lst.push_back(iter.value());
            }
            LOG_TRACE("Success!");
            return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBsrCondElement(
    const xc::CSnapshot &cSnapShot,
    const int32 &iServiceId,
    const int32 &iBusiType,
    const time_t &tm,
    CBsrSettleElement &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< iServiceId << "!"
                << iBusiType;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBsrSettleElement::Type> cQueryHolder(cSnapShot,CBsrSettleElement::GetContainerName());
        CBsrSettleElement::Type::iterator iter = cQueryHolder.GetContainer().find(CBsrSettleElement::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBsrSettleElement>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsMgCampInfo(
    const xc::CSnapshot &cSnapShot,
    const char * cszKey,
    // const int32 &iBusiType,
    const time_t &tm,
    CBpsMgCampInfo &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszKey;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsMgCampInfo::Type> cQueryHolder(cSnapShot,CBpsMgCampInfo::GetContainerName());
        CBpsMgCampInfo::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsMgCampInfo::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsMgCampInfo>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CSysCityByAreaCode(
    const xc::CSnapshot &cSnapShot,
    const char *cszAreaCode,
    const time_t &tm,
    CSysCity &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszAreaCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CSysCity::Type> cQueryHolder(cSnapShot,CSysCity::GetContainerName());
        CSysCity::Type::iterator iter = cQueryHolder.GetContainer().find(CSysCity::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CSysCity>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CSysCityByRegionCode(
    const xc::CSnapshot &cSnapShot,
    const char *cRegionCode,
    const time_t &tm,
    CSysCity &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cRegionCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CSysCity::Type> cQueryHolder(cSnapShot,CSysCity::GetContainerName());
        CSysCity::Type::iterator iter = cQueryHolder.GetContainer().find(CSysCity::KEY2,sstrKey.str().c_str(),xload::base_cmp_func<CSysCity>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CSysCountry(
    const xc::CSnapshot &cSnapShot,
    const char *cszCountryCode,
    const time_t &tm,
    CSysCountry &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszCountryCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CSysCountry::Type> cQueryHolder(cSnapShot,CSysCountry::GetContainerName());
        CSysCountry::Type::iterator iter = cQueryHolder.GetContainer().find(CSysCountry::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CSysCountry>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CSysProv(
    const xc::CSnapshot &cSnapShot,
    const char *cszProvCode,
    const time_t &tm,
    CSysProv &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszProvCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CSysProv::Type> cQueryHolder(cSnapShot,CSysProv::GetContainerName());
        CSysProv::Type::iterator iter = cQueryHolder.GetContainer().find(CSysProv::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CSysProv>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CVBpsLacAreaRel(
    const xc::CSnapshot &cSnapShot,
    const char *cszLacId,
    const time_t &tm,
    CVBpsLacAreaRel &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszLacId;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CVBpsLacAreaRel::Type> cQueryHolder(cSnapShot,CVBpsLacAreaRel::GetContainerName());
        CVBpsLacAreaRel::Type::iterator iter = cQueryHolder.GetContainer().find(CVBpsLacAreaRel::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CVBpsLacAreaRel>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CVBpsLacCeilCountyRel(
    const xc::CSnapshot &cSnapShot,
    const char *cszLacId,
    const char *cszCeilId,
    const time_t &tm,
    CVBpsLacCeilCountyRel &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszLacId << ","
                <<cszCeilId;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CVBpsLacCeilCountyRel::Type> cQueryHolder(cSnapShot,CVBpsLacCeilCountyRel::GetContainerName());
        CVBpsLacCeilCountyRel::Type::iterator iter = cQueryHolder.GetContainer().find(CVBpsLacCeilCountyRel::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CVBpsLacCeilCountyRel>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CVBpsRcdxUserRel(
    const xc::CSnapshot &cSnapShot,
    const char *cszServiceCode,
    CVBpsRcdxUserRel &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszServiceCode;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CVBpsRcdxUserRel::Type> cQueryHolder(cSnapShot,CVBpsRcdxUserRel::GetContainerName());
        CVBpsRcdxUserRel::Type::iterator iter = cQueryHolder.GetContainer().find(CVBpsRcdxUserRel::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

extern int32 odac_app_lookup_CVBsrCondition(
        const xc::CSnapshot &cSnapShot,
        const int32 &iServiceId,
        const int32 &iDrType,
        const int32 &iSettleSide,
        AISTD list<CVBsrCondition> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< iServiceId << "|"
                << iDrType << "|"
                << iSettleSide;
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CVBsrCondition::Type> cQueryHolder(cSnapShot,CVBsrCondition::GetContainerName());
        CVBsrCondition::Type::iterator iter = cQueryHolder.GetContainer().find(CVBsrCondition::KEY1,sstrKey.str().c_str());
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
            for(;!iter.eof();++iter)
            {
                lst.push_back(iter.value());
            }
            LOG_TRACE("Success!");
            return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CVBsrCondExpr(
    const xc::CSnapshot &cSnapShot,
    const int32 &iCondId,
    const time_t &tm,
    CVBsrCondExpr &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< iCondId;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CVBsrCondExpr::Type> cQueryHolder(cSnapShot,CVBsrCondExpr::GetContainerName());
        CVBsrCondExpr::Type::iterator iter = cQueryHolder.GetContainer().find(CVBsrCondExpr::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CVBsrCondExpr>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CVBsrCurveSegments(
    const xc::CSnapshot &cSnapShot,
    const int32 &iCondId,
    const time_t &tm,
    CVBsrCurveSegments &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< iCondId;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CVBsrCurveSegments::Type> cQueryHolder(cSnapShot,CVBsrCurveSegments::GetContainerName());
        CVBsrCurveSegments::Type::iterator iter = cQueryHolder.GetContainer().find(CVBsrCurveSegments::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CVBsrCurveSegments>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CVIvrRatio(
    const xc::CSnapshot &cSnapShot,
    const char *cszSpCode,
    const char *cszOperCode,
    const time_t &tm,
    AISTD list<CVIvrRatio> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszSpCode << "|"
                <<cszOperCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CVIvrRatio::Type> cQueryHolder(cSnapShot,CVIvrRatio::GetContainerName());
        CVIvrRatio::Type::iterator iter = cQueryHolder.GetContainer().find(CVIvrRatio::KEY3,sstrKey.str().c_str(),xload::base_cmp_func<CVIvrRatio>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
            for(;!iter.eof();++iter)
            {
                lst.push_back(iter.value());
            }
            LOG_TRACE("Success!");
            return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CVIvrRatioByOperCode(
    const xc::CSnapshot &cSnapShot,
    const char *cszOperCode,
    const time_t &tm,
    AISTD list<CVIvrRatio> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszOperCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CVIvrRatio::Type> cQueryHolder(cSnapShot,CVIvrRatio::GetContainerName());
        CVIvrRatio::Type::iterator iter = cQueryHolder.GetContainer().find(CVIvrRatio::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CVIvrRatio>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                for(;!iter.eof();++iter)
                {
                    lst.push_back(iter.value());
                }
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CVIvrRatioBySpCode(
    const xc::CSnapshot &cSnapShot,
    const char *cszSpCode,
    const time_t &tm,
    AISTD list<CVIvrRatio> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszSpCode;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CVIvrRatio::Type> cQueryHolder(cSnapShot,CVIvrRatio::GetContainerName());
        CVIvrRatio::Type::iterator iter = cQueryHolder.GetContainer().find(CVIvrRatio::KEY2,sstrKey.str().c_str(),xload::base_cmp_func<CVIvrRatio>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                for(;!iter.eof();++iter)
                {
                    lst.push_back(iter.value());
                }
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CVSpRatioBySpCodeAndSpType(
    const xc::CSnapshot &cSnapShot,
    const char *szSpCode,
    const int32 &iSpType,
    const time_t &tm,
    CVSpRatio &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< szSpCode << "|"
                << iSpType;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CVSpRatio::Type> cQueryHolder(cSnapShot,CVSpRatio::GetContainerName());
        CVSpRatio::Type::iterator iter = cQueryHolder.GetContainer().find(CVSpRatio::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CVSpRatio>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CVSpRatio(
    const xc::CSnapshot &cSnapShot,
    const char *szSpCode,
    const char *szOperatorCode,
    const int32 &iSpType,
    const time_t &tm,
    CVSpRatio &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< szSpCode << "|"
                << szOperatorCode << "|"
                << iSpType;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CVSpRatio::Type> cQueryHolder(cSnapShot,CVSpRatio::GetContainerName());
        CVSpRatio::Type::iterator iter = cQueryHolder.GetContainer().find(CVSpRatio::KEY2,sstrKey.str().c_str(),xload::base_cmp_func<CVSpRatio>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CVSpUserInfo(
    const xc::CSnapshot &cSnapShot,
    const char *cszSpCode,
    const char *cszOperCode,
    const char *cszMsisdn,
    const time_t &tm,
    CVSpUserInfo &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< cszSpCode << "!"
                << cszOperCode << "!"
                << cszMsisdn;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CVSpUserInfo::Type> cQueryHolder(cSnapShot,CVSpUserInfo::GetContainerName());
        CVSpUserInfo::Type::iterator iter = cQueryHolder.GetContainer().find(CVSpUserInfo::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CVSpUserInfo>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}
int32 odac_app_lookup_CBpsSpecialUser(
    const xc::CSnapshot &cSnapShot,
    const int32 &t_SAreaCode,
    const char *cszSpecialNumber,
    const time_t &tm,
    AISTD list<CBpsSpecialUser> &lst
    /*CBpsSpecialUser &cVal*/)
{
    //add code here now
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< t_SAreaCode << "!"
                << cszSpecialNumber ;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsSpecialUser::Type> cQueryHolder(cSnapShot,CBpsSpecialUser::GetContainerName());
        CBpsSpecialUser::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsSpecialUser::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsSpecialUser>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
            for(;!iter.eof();++iter)
            {
                lst.push_back(iter.value());
            }
            LOG_TRACE("Success!");
            return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
    return 0;
}

int32 odac_app_lookup_CSysProvByArea(
    const xc::CSnapshot &cSnapShot,
    const char *cszAreaCode,
    const time_t &tm,
    CSysProv &cVal)
{
    try
    {
            AISTD stringstream sstrKey;
            sstrKey << cszAreaCode;
            LOG_TRACE("%s,key KEY2:%s,tm:%d", __func__, sstrKey.str().c_str(), tm);
            xc::CQueryHolder<CSysProv::Type> cQueryHolder(cSnapShot, CSysProv::GetContainerName());
            CSysProv::Type::iterator iter = cQueryHolder.GetContainer().find(CSysProv::KEY2, sstrKey.str().c_str(), xload::base_cmp_func<CSysProv>(tm));
            if (iter.eof())
            {
                LOG_TRACE("No record!");
                return -1;
            }
            else
            {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
            }
    }
    catch (XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]", __func__, e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CVBpsIpv4Address(const xc::CSnapshot& cSnapShot, const char *address, const time_t &tm, AISTD list<CVBpsIpv4Address> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< address;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CVBpsIpv4Address::Type> cQueryHolder(cSnapShot,CVBpsIpv4Address::GetContainerName());
        CVBpsIpv4Address::Type::iterator iter = cQueryHolder.GetContainer().find(CVBpsIpv4Address::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CVBpsIpv4Address>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
            for(;!iter.eof();++iter)
            {
                lst.push_back(iter.value());
            }
            LOG_TRACE("Success!");
            return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsIpv4AddressRange(const xc::CSnapshot& cSnapShot, const char *address, const time_t &tm, AISTD list<CBpsIpv4AddressRange> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< address;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsIpv4AddressRange::Type> cQueryHolder(cSnapShot,CBpsIpv4AddressRange::GetContainerName());
        CBpsIpv4AddressRange::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsIpv4AddressRange::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsIpv4AddressRange>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
            for(;!iter.eof();++iter)
            {
                lst.push_back(iter.value());
            }
            LOG_TRACE("Success!");
            return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsIpv6AddressRange(const xc::CSnapshot& cSnapShot, const char *address, const time_t &tm, AISTD list<CBpsIpv6AddressRange> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< address;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsIpv6AddressRange::Type> cQueryHolder(cSnapShot,CBpsIpv6AddressRange::GetContainerName());
        CBpsIpv6AddressRange::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsIpv6AddressRange::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsIpv6AddressRange>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
            for(;!iter.eof();++iter)
            {
                lst.push_back(iter.value());
            }
            LOG_TRACE("Success!");
            return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsServProduct(const xc::CSnapshot &cSnapShot, const char *msisdn, CBpsServProduct &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<<msisdn;
        time_t tm = time(NULL);
        LOG_TRACE("%s,key:%s",__func__,sstrKey.str().c_str());
        xc::CQueryHolder<CBpsServProduct::Type> cQueryHolder(cSnapShot,CBpsServProduct::GetContainerName());
        CBpsServProduct::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsServProduct::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsServProduct>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
                cVal = iter.value();
                LOG_TRACE("Success!");
                return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsSjDefaultPrice(const xc::CSnapshot& cSnapShot, const char *pszSettlementType, const time_t &tm, AISTD list<CBpsSjSmsDefaultPrice> &lst)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< pszSettlementType;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsSjSmsDefaultPrice::Type> cQueryHolder(cSnapShot,CBpsSjSmsDefaultPrice::GetContainerName());
        CBpsSjSmsDefaultPrice::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsSjSmsDefaultPrice::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsSjSmsDefaultPrice>(tm));
        if(iter.eof())
        {
                LOG_TRACE("No record!");
                return -1;
        }
        else
        {
            for(;!iter.eof();++iter)
            {
                lst.push_back(iter.value());
            }
            LOG_TRACE("Success!");
            return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CSysBureau(const xc::CSnapshot &cSnapShot, const char *cszBureauCode, CSysBureau &cVal)
{
    try
    {
        AISTD stringstream sstrKey;
        sstrKey << cszBureauCode;
        time_t tm = time(NULL);
        LOG_TRACE("%s,key KEY2:%s,tm:%d", __func__, sstrKey.str().c_str(), tm);
        xc::CQueryHolder<CSysBureau::Type> cQueryHolder(cSnapShot, CSysBureau::GetContainerName());
        CSysBureau::Type::iterator iter = cQueryHolder.GetContainer().find(CSysBureau::KEY1, sstrKey.str().c_str(), xload::base_cmp_func<CSysBureau>(tm));
        if (iter.eof())
        {
            LOG_TRACE("No record!");
            return -1;
        }
        else
        {
            cVal = iter.value();
            LOG_TRACE("Success!");
            return 0;
        }
    }
    catch (XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]", __func__, e.get_message().c_str());
            return -1;
    }
}

int32 odac_app_lookup_CBpsSpecialNet(
    const xc::CSnapshot &cSnapShot,
    const char *pszAreaCode,
    const char *pszBureauCode,
    const char *cszSpecialNumber,
    const time_t &tm,
    AISTD list<CBpsSpecialNet> &lst
    )
{
    //add code here now
    try
    {
        AISTD stringstream sstrKey;
        sstrKey<< pszAreaCode << "!" << pszBureauCode << "!" << cszSpecialNumber ;
        LOG_TRACE("%s,key:%s,tm:%d",__func__,sstrKey.str().c_str(),tm);
        xc::CQueryHolder<CBpsSpecialNet::Type> cQueryHolder(cSnapShot,CBpsSpecialNet::GetContainerName());
        CBpsSpecialNet::Type::iterator iter = cQueryHolder.GetContainer().find(CBpsSpecialNet::KEY1,sstrKey.str().c_str(),xload::base_cmp_func<CBpsSpecialNet>(tm));

        if(iter.eof())
        {
            LOG_TRACE("No record!");
            return -1;
        }
        else
        {
            for(;!iter.eof();++iter)
            {
                lst.push_back(iter.value());
            }
            LOG_TRACE("%s get %ld records. Success!",__func__, lst.size());
            return 0;
        }
    }
    catch(XC_EXCEPTION &e)
    {
            LOG_TRACE("catch XC_EXCEPTION; func = [%s];XC_EXCEPTION message =[%s]",__func__,e.get_message().c_str());
            return -1;
    }
    return 0;
}
