#!/bin/python
#-*- coding: utf-8 -*-

#import cx_Oracle
import datetime
import time
import sys
import logging
import os
import traceback
import proc_stat_a2p_cdr_daily
import cmi_common_util
import psycopg2

g_dictTrafficVendor = {}
g_dictA2PSWapVendor = {}
g_dictA2PPriceVendor = {}

g_dictTrafficCustomer = {}
g_dictA2PSWapCustomer = {}
g_dictA2PPriceCustomer = {}

def fetchAll(dbConnect, sql, parameters = {}, arraysize=500):
    try:
        cur = dbConnect.cursor()
        cur.arraysize = arraysize
        cur.execute(sql, parameters)
        rows = cur.fetchall()
        cur.close()
        return rows
    except psycopg2.Error as exc:
        print(traceback.format_exc())
        print("error sql:", sql)
        logging.error("error sql:{}".format(sql))
        logging.error("error: {}".format(traceback.format_exc()))
    return None

def getTaskId(dbConnect):
    
    ## make seq
    sql = "select SYS_TASK_LOG$SEQ.nextval from dual"
    cur = dbConnect.cursor()
    cur.execute(sql)
    seqRows = cur.fetchall()
    cur.close()
    seQ = -1

    for row in seqRows:
        seQ = row[0]

    return seQ

def getNextDay(currDay,step=1):
    yearNum = int(currDay[0:4])
    monthNum = int(currDay[4:6])
    dayNum = int(currDay[6:8])

    tDay = datetime.date(yearNum,monthNum,dayNum)
    delta = datetime.timedelta(days=step)
    tDay = tDay + delta

    strDay = tDay.strftime("%Y%m%d")

    return strDay

def insertTaskAll(dbConnect, START_DATE, END_DATE):
    '''
    10点半触发全量重批任务 1号到当日  2个方向
    '''
    sql = "select count(1) from sys_task_log a  where a.start_time>TRUNC(sysdate) and a.task_flow_name='smsRedoAll'"
    rows = fetchAll(dbConnect, sql)
    if not rows is None:
        for row in rows:
            cnt = row[0]
    if cnt == 0 :
        cur = dbConnect.cursor() 
        taskId = getTaskId(dbConnect)
        sql = "insert into  SYS_TASK_LOG (task_id,task_flow_name,task_code,start_time,state) values (" + str(taskId) +", 'smsRedoAll', 301,sysdate, 1)"
        cur.execute(sql)
        sql = "insert into sys_task_args_detail (task_id,task_args_code, task_args_value) values (" + str(taskId) +",'START_DATE','" + START_DATE + "')"
        cur.execute(sql)
        sql = "insert into sys_task_args_detail (task_id,task_args_code, task_args_value) values (" + str(taskId) +",'END_DATE','" + END_DATE + "')"
        cur.execute(sql)
        sql = "insert into sys_task_args_detail (task_id,task_args_code, task_args_value) values (" + str(taskId) +",'ACCT_ID','-1')"
        cur.execute(sql)
        sql = "insert into sys_task_args_detail (task_id,task_args_code, task_args_value) values (" + str(taskId) +",'DIRECTFLAG','0')"
        cur.execute(sql)

        cur.close()
        dbConnect.commit()

def getRuningTaskCnt(dbConnect):
    cnt = -1
    sql = "select count(1) from sys_task_log where state=2 and task_code in (301,302) "

    rows = fetchAll(dbConnect, sql)

    if rows is None:
        return cnt
    
    for row in rows:
        cnt  =row[0]

    return cnt

def updateTaskState(dbConnect, taskId, state):
    sql = '''update sys_task_log set state={0},end_time=sysdate where task_id={1}'''
    cur = dbConnect.cursor()
    cur.execute(sql.format(state,taskId))
    cur.close()
    dbConnect.commit()

def getTask2Run(dbConnect):
    sql = 'select task_id,task_flow_name from sys_task_log where state=1 and task_code in (301,302) order by task_id asc'

    rows = fetchAll(dbConnect, sql)

    if not rows is None:
        taskId = -1
        for row in rows:  # 取第1个任务
            taskId = row[0]
            taskFlowName = row[1]
            break

        if taskId > 0:
            updateTaskState(dbConnect, taskId, 2)
            sql = '''select task_args_code, nvl(task_args_value,'-1') from sys_task_args_detail 
where task_id={0} and task_args_code in ('START_DATE', 'END_DATE', 'ACCT_ID', 'DIRECTFLAG') '''

            taskRows = fetchAll(dbConnect,sql.format(taskId))

            if not taskRows is None:
                taskInfo = {}
                taskInfo['task_id'] = taskId
                taskInfo['task_flow_name'] = taskFlowName
                taskInfo['account_id'] = '-1'
                taskInfo['end_date'] = datetime.datetime.now().strftime("%Y%m%d")

                for taskRow in taskRows:
                    if taskRow[0] == 'START_DATE':
                        taskInfo['start_date'] = taskRow[1]
                    elif taskRow[0] == 'END_DATE':
                        taskInfo['end_date'] = taskRow[1]
                    elif taskRow[0] == 'ACCT_ID':
                        if len(taskRow[1]) < 3:
                            taskInfo['account_id'] = '-1'
                        else:
                            taskInfo['account_id'] = taskRow[1]
                    elif taskRow[0] == 'DIRECTFLAG':
                        taskInfo['dir_flag'] = int(taskRow[1])
                return taskInfo
    return None



def doVendorByHour(dbConnect, workDay, iHour,acctId,dirFlag):
    global g_dictA2PSWapVendor
    global g_dictA2PPriceVendor

    sqlSel = '''select /*+set(query_dop 8) */ CTID,OUT_ACCT_ID,CHARING_TYPE,DEST_OPER_ID,SEND_TIME,OUT_ERROR,OUT_ACC_SETTLE_ID,OUT_CHARGE_2,billing_items_cnt,file_source,nvl(RESERVED7,0)
from dr_cmi_sms_{0} partition (P_H{1})  
where STATUS = 1 '''

    if acctId == "-1":
        sqlSel = sqlSel + "and CDR_TYPE=501 "
    else:
        sqlSel = sqlSel + "and OUT_ACCT_ID in {0} ".format(acctId)

    sqlUpdate = '''update /*+set(query_dop 8) */ dr_cmi_sms_{0} partition (P_H{1}) 
set CDR_TYPE=501,OUT_ERROR=%(OUT_ERROR)s, OUT_CHARGE_2=%(OUT_CHARGE_2)s,OUT_RATE2=%(OUT_RATE2)s,OUT_OP_CHARGE=%(OUT_OP_CHARGE)s,OUT_ACC_SETTLE_ID=%(OUT_ACC_SETTLE_ID)s,RESERVED7=%(CURRENCY)s  
where CTID=%(rowkey)s'''


    rows = fetchAll(dbConnect, sqlSel.format(workDay,iHour), {}, 10000)

    if rows is None:
        return 0
    
    lstParam = []
    iCount = 0
    iTotal = 0
    
    for row in rows:
        rowKey = row[0]
        OUT_ACCT_ID = str(row[1])
        CHARING_TYPE = row[2]
        DEST_OPER_ID = row[3]
        SEND_TIME = str(row[4])
        OUT_ERROR_ORI = row[5]
        OUT_ACC_SETTLE_ID_ORI = row[6]
        OUT_CHARGE_2_ORI = row[7]
        BILL_CNT = row[8]
        FILE_SRC = row[9] 
        CURRENCY_ORI = row[10]

        OUT_ERROR = 10
        OUT_CHARGE_2 = 0
        OUT_RATE2 = 0
        OUT_OP_CHARGE = 0
        OUT_ACC_SETTLE_ID = 0

        rateInfo = getVendorRate(OUT_ACCT_ID, CHARING_TYPE, DEST_OPER_ID, SEND_TIME,FILE_SRC, g_dictA2PSWapVendor)

        if rateInfo is None:
            rateInfo = getVendorRate(OUT_ACCT_ID, CHARING_TYPE, DEST_OPER_ID, SEND_TIME,FILE_SRC, g_dictA2PPriceVendor)
        

        if not rateInfo  is None:
            OUT_ERROR = 0
            OUT_CHARGE_2 = rateInfo['settle_rate']
            OUT_RATE2 = OUT_CHARGE_2
            OUT_OP_CHARGE = rateInfo['option_rate']
            OUT_ACC_SETTLE_ID = int(rateInfo['product_id'])
            CURRENCY = rateInfo['currency']
            
            if OUT_ERROR != OUT_ERROR_ORI or  OUT_CHARGE_2 != OUT_CHARGE_2_ORI or OUT_ACC_SETTLE_ID != OUT_ACC_SETTLE_ID_ORI or CURRENCY_ORI != CURRENCY:
                iCount = iCount + 1
                lstParam.append({'OUT_ERROR':OUT_ERROR, 'OUT_CHARGE_2':OUT_CHARGE_2, 'OUT_RATE2':OUT_RATE2, 'OUT_OP_CHARGE':OUT_OP_CHARGE,'OUT_ACC_SETTLE_ID':OUT_ACC_SETTLE_ID,'CURRENCY':CURRENCY,'rowkey':rowKey})

                if iCount >= 10000:
                    cur = dbConnect.cursor()
                    cur.executemany(sqlUpdate.format(workDay,iHour), lstParam)
                    cur.close()
                    dbConnect.commit()
                    lstParam = []
                    iTotal = iTotal + iCount
                    iCount = 0
    if  iCount > 0:
        cur = dbConnect.cursor()
        cur.executemany(sqlUpdate.format(workDay,iHour), lstParam)
        cur.close()
        dbConnect.commit()
        iTotal = iTotal + iCount
    return iTotal

def fetchA2PVendorSwap(dbConnect, strMonth, acctId):
    global g_dictA2PSWapVendor
    g_dictA2PSWapVendor.clear()

    if acctId == "-1":
        sql = '''select /*+set(query_dop 8) */ distinct unique_id,bill_type,destination,product_id,settle_rate,option_rate,eff_date,exp_date,charge_mode,start_val,end_val,currency 
            from v_bsr_a2p_provider_swap 
            where substr(eff_date,1,6) <= '{0}' and substr(exp_date,1,6) >= '{0}' '''.format(strMonth)
    else:
        sql = '''select /*+set(query_dop 8) */ distinct unique_id,bill_type,destination,product_id,settle_rate,option_rate,eff_date,exp_date,charge_mode,start_val,end_val,currency 
            from v_bsr_a2p_provider_swap 
            where unique_id in {1} and substr(eff_date,1,6) <= '{0}' and substr(exp_date,1,6) >= '{0}' '''.format(strMonth, acctId)

    rows = fetchAll(dbConnect, sql)

    for row in rows:
        acctId = row[0]
        billType =str(row[1])
        dest = row[2]
        prodId = row[3]
        settleRate = row[4]
        optionRate = row[5]
        effDate = row[6]
        expDate = row[7]
        chargeMode = row[8]
        startVal = row[9]
        endVal = row[10] 
        currency = row[11]

        key = acctId + "|" + billType + "|" + dest
        rateInfo = {}
        rateInfo['product_id'] = prodId
        rateInfo['settle_rate'] = settleRate
        rateInfo['option_rate'] = optionRate
        rateInfo['eff_date'] = effDate
        rateInfo['exp_date'] = expDate
        rateInfo['charge_mode'] = chargeMode
        rateInfo['start_val'] = startVal
        rateInfo['end_val'] = endVal
        rateInfo['currency'] = currency

        if key in g_dictA2PSWapVendor:
            g_dictA2PSWapVendor[key].append(rateInfo)
        else:
            g_dictA2PSWapVendor[key] = []
            g_dictA2PSWapVendor[key].append(rateInfo)

def fetchA2PVendorPrice(dbConnect, strMonth, acctId):
    global g_dictA2PPriceVendor
    g_dictA2PPriceVendor.clear()

    if acctId == "-1":
        sql = '''select /*+set(query_dop 8) */ distinct unique_id,bill_type,destination,product_id,settle_rate,option_rate,eff_date,exp_date,currency
                from v_bsr_a2p_provider_rate
                where substr(eff_date,1,6) <= '{0}' and substr(exp_date,1,6) >= '{0}' '''.format(strMonth)
    else:
        sql = '''select /*+set(query_dop 8) */ distinct unique_id,bill_type,destination,product_id,settle_rate,option_rate,eff_date,exp_date,currency
                from v_bsr_a2p_provider_rate
                where unique_id in {1} and substr(eff_date,1,6) <= '{0}' and substr(exp_date,1,6) >= '{0}' '''.format(strMonth, acctId)

    rows = fetchAll(dbConnect, sql)

    for row in rows:
        acctId = row[0]
        billType =str(row[1])
        dest = row[2]
        prodId = row[3]
        settleRate = row[4]
        optionRate = row[5]
        effDate = row[6]
        expDate = row[7]
        currency = row[8]
        if acctId is None or billType is None or dest is None:
            continue
        key = acctId + "|" + billType + "|" + dest
        rateInfo = {}
        rateInfo['product_id'] = prodId
        rateInfo['settle_rate'] = settleRate
        rateInfo['option_rate'] = optionRate
        rateInfo['eff_date'] = effDate
        rateInfo['exp_date'] = expDate
        rateInfo['charge_mode'] = 1
        rateInfo['start_val'] = 1
        rateInfo['end_val'] = -1
        rateInfo['currency'] = currency

        if key in g_dictA2PPriceVendor:
            g_dictA2PPriceVendor[key].append(rateInfo)
        else:
            g_dictA2PPriceVendor[key] = []
            g_dictA2PPriceVendor[key].append(rateInfo)

def getVendorRate(acctId, billType, dest,sendTime,fileSrc, dictRate):
    global g_dictTrafficVendor
    key = acctId + "|" + billType + "|" + dest 
    lastRate = None

    if key in dictRate:
        for rateInfo in dictRate[key]:
            if sendTime >= rateInfo['eff_date'] and sendTime < rateInfo['exp_date']:
                if rateInfo['charge_mode'] == 10 or rateInfo['charge_mode'] == 20:
                    prodId = rateInfo['product_id']

                    traffic = 0
                    key2 = "{0}|{1}".format(prodId,fileSrc) 
                    if key2 in g_dictTrafficVendor:
                        traffic = g_dictTrafficVendor[key2]
                    traffic = traffic + 1

                    if traffic >= rateInfo['start_val'] and (-1 == rateInfo['end_val'] or traffic <= rateInfo['end_val']):
                        g_dictTrafficVendor[key2] = traffic
                        return rateInfo
                else:
                    if lastRate is None:
                        lastRate = rateInfo
                    else:
                        if lastRate['eff_date'] < rateInfo['eff_date']:
                            lastRate = rateInfo

    return lastRate

def fetchA2PCustomerSwap(dbConnect, strMonth, acctId):
    global g_dictA2PSWapCustomer
    g_dictA2PSWapCustomer.clear()

    if acctId == "-1":
        sql = '''select /*+set(query_dop 8) */ distinct acct_id,direction,charge_type,
        decode(length(destination),4,substr(destination, 1, 3)|| lpad(substr(destination, 4,3),2,'0'),to_char(destination)),
        product_id,item_id,settle_rate,op_rate,valid_date,expire_date,charge_mode,from_cnt,to_cnt,currency 
            from v_bsr_a2p_customer_all 
        where charge_mode> 1 and substr(valid_date,1,6) <= '{0}' and substr(expire_date,1,6) >= '{0}' '''.format(strMonth)
    else:
        sql = '''select /*+set(query_dop 8) */ distinct acct_id,direction,charge_type,
        decode(length(destination),4,substr(destination, 1, 3)|| lpad(substr(destination, 4,3),2,'0'),to_char(destination)),
        product_id,item_id,settle_rate,op_rate,valid_date,expire_date,charge_mode,from_cnt,to_cnt,currency 
            from v_bsr_a2p_customer_all 
        where acct_id in {1} and charge_mode> 1 and substr(valid_date,1,6) <= '{0}' and substr(expire_date,1,6) >= '{0}' '''.format(strMonth, acctId)

    rows = fetchAll(dbConnect, sql)

    for row in rows:
        acctId = str(row[0])
        direction = str(row[1])
        billType =str(row[2])
        dest = str(row[3])
        prodId = row[4]
        itemId = row[5]
        settleRate = row[6]
        optionRate = row[7]
        effDate = row[8]
        expDate = row[9]
        chargeMode = row[10]
        startVal = row[11]
        endVal = row[12] 
        currency = row[13]
        
        if acctId is None or direction is None or billType is None or dest is None:
            continue
        key = acctId + "|" + direction + "|" + billType + "|" + dest
        rateInfo = {}
        rateInfo['product_id'] = prodId
        rateInfo['item_id'] = itemId
        rateInfo['settle_rate'] = settleRate
        rateInfo['option_rate'] = optionRate
        rateInfo['eff_date'] = effDate
        rateInfo['exp_date'] = expDate
        rateInfo['charge_mode'] = chargeMode
        rateInfo['start_val'] = startVal
        rateInfo['end_val'] = endVal
        rateInfo['currency'] = currency

        if key in g_dictA2PSWapCustomer:
            g_dictA2PSWapCustomer[key].append(rateInfo)
        else:
            g_dictA2PSWapCustomer[key] = []
            g_dictA2PSWapCustomer[key].append(rateInfo)

def fetchA2PCustomerPrice(dbConnect, strMonth, acctId):
    global g_dictA2PPriceCustomer
    g_dictA2PPriceCustomer.clear()

    if acctId == "-1":
        sql = '''select /*+set(query_dop 8) */ distinct acct_id,direction,charge_type,
        decode(length(destination),4,substr(destination, 1, 3)|| lpad(substr(destination, 4,3),2,'0'),to_char(destination)),
        product_id,item_id,settle_rate,op_rate,valid_date,expire_date,currency 
        from settle.v_bsr_a2p_customer_all 
        where charge_mode=1 and substr(valid_date,1,6) <= '{0}' and substr(expire_date,1,6) >= '{0}'
        '''.format(strMonth)
    else:
        sql = '''select /*+set(query_dop 8) */ distinct acct_id,direction,charge_type,
        decode(length(destination),4,substr(destination, 1, 3)|| lpad(substr(destination, 4,3),2,'0'),to_char(destination)),
        product_id,item_id,settle_rate,op_rate,valid_date,expire_date,currency 
        from settle.v_bsr_a2p_customer_all 
        where acct_id in {1} and charge_mode=1 and substr(valid_date,1,6) <= '{0}' and substr(expire_date,1,6) >= '{0}'
        '''.format(strMonth, acctId)

    rows = fetchAll(dbConnect, sql)

    for row in rows:
        acctId = str(row[0])
        direction = str(row[1])
        billType =str(row[2])
        dest = str(row[3])
        prodId = row[4]
        itemId = row[5]
        settleRate = row[6]
        optionRate = row[7]
        effDate = row[8]
        expDate = row[9]
        currency = row[10]

        key = acctId + "|" + direction + "|" + billType + "|" + dest
        rateInfo = {}
        rateInfo['product_id'] = prodId
        rateInfo['item_id'] = itemId
        rateInfo['settle_rate'] = settleRate
        rateInfo['option_rate'] = optionRate
        rateInfo['eff_date'] = effDate
        rateInfo['exp_date'] = expDate
        rateInfo['charge_mode'] = 1
        rateInfo['start_val'] = 1
        rateInfo['end_val'] = -1
        rateInfo['currency'] = currency

        if key in g_dictA2PPriceCustomer:
            g_dictA2PPriceCustomer[key].append(rateInfo)
        else:
            g_dictA2PPriceCustomer[key] = []
            g_dictA2PPriceCustomer[key].append(rateInfo)

def doCustomerByHour(dbConnect, workDay, iHour,acctId,dirFlag):
    global g_dictA2PSWapCustomer
    global g_dictA2PPriceCustomer

    sqlSel = '''select /*+set(query_dop 8) */ CTID,IN_ACCT_ID,MO_MT,CHARING_TYPE,DEST_OPER_ID,SEND_TIME,IN_ERROR,substr(original_file,23,8),
    SRC_OPER_ID,ACCESS_OPER_ID,finish_time,billing_items_cnt,IN_CHARGE_2,IN_RATE2,IN_OP_CHARGE,IN_ACC_SETTLE_ID,nvl(RESERVED6,0)
from dr_cmi_sms_{0} partition (P_H{1})  
where STATUS = 1 '''
    if acctId == "-1":
        sqlSel = sqlSel + " and CDR_TYPE =501 "
    else:
        sqlSel = sqlSel + " and IN_ACCT_ID in {0} ".format(acctId)

    sqlUpdate = '''update /*+set(query_dop 8) */ dr_cmi_sms_{0} partition (P_H{1}) 
set CDR_TYPE =501,IN_ERROR=%(IN_ERROR)s, IN_RATE_PLAN_DETAIL_ID=1,resettle_taskid=255,
    IN_CHARGE_2=%(IN_CHARGE_2)s,IN_RATE2=%(IN_RATE2)s,IN_OP_CHARGE=%(IN_OP_CHARGE)s,IN_ACC_SETTLE_ID=%(IN_ACC_SETTLE_ID)s, RESERVED6=%(CURRENCY)s
where CTID=%(rowkey)s'''

    rows = fetchAll(dbConnect, sqlSel.format(workDay,iHour), {}, 10000)

    if rows is None:
        return 0
    
    lstParam = []
    iCount = 0
    iTotal = 0
    
    for row in rows:
        rowKey = row[0]
        IN_ACCT_ID = str(row[1])
        MO_MT = row[2]
        CHARING_TYPE = row[3]
        DEST_OPER_ID = row[4]
        SEND_TIME = str(row[5])
        IN_ERROR_ORI = row[6]
        billMonth = str(workDay) 
        SRC_OPER_ID = row[8]
        ACCESS_OPER_ID = row[9]
        RECV_TIME = row[10]
        BillCnt = row[11]
        IN_CHARGE_2_ORI = row[12]
        IN_RATE2_ORI = row[13]
        IN_OP_CHARGE_ORI = row[14]
        IN_ACC_SETTLE_ID_ORI = row[15]
        CURRENCY_ORI = row[16]

        #初始化
        IN_ERROR = 0
        IN_OP_CHARGE = 0
        ITEM_ID = 0
        IN_CHARGE_2 = 0
        IN_RATE2 = 0

        if BillCnt is None or BillCnt < 1:
            BillCnt = 1

        # 0:MO, 1:MT 默认MT
        DIRECTION = "1"
        if MO_MT == "MO":
            DIRECTION = "0"

        rateInfo = getCustomerRate(IN_ACCT_ID, DIRECTION, CHARING_TYPE, DEST_OPER_ID, RECV_TIME, g_dictA2PSWapCustomer)

        if rateInfo is None:
            newDst = DEST_OPER_ID[0:3] + "0" + DEST_OPER_ID[-2:]
            rateInfo = getCustomerRate(IN_ACCT_ID, DIRECTION, CHARING_TYPE, newDst, RECV_TIME, g_dictA2PSWapCustomer)

        if rateInfo is None:
            rateInfo = getCustomerRate(IN_ACCT_ID, DIRECTION, CHARING_TYPE, DEST_OPER_ID[0:3], RECV_TIME, g_dictA2PSWapCustomer)

        if rateInfo is None:
            rateInfo = getCustomerRate(IN_ACCT_ID, DIRECTION, CHARING_TYPE, DEST_OPER_ID, RECV_TIME, g_dictA2PPriceCustomer)

        if rateInfo is None:
            newDst = DEST_OPER_ID[0:3] + "0" + DEST_OPER_ID[-2:]
            rateInfo = getCustomerRate(IN_ACCT_ID, DIRECTION, CHARING_TYPE, newDst, RECV_TIME, g_dictA2PPriceCustomer)

        if rateInfo is None:
            rateInfo = getCustomerRate(IN_ACCT_ID, DIRECTION, CHARING_TYPE, DEST_OPER_ID[0:3], RECV_TIME, g_dictA2PPriceCustomer)

        if not rateInfo  is None:
            IN_ERROR = 0
            IN_OP_CHARGE = rateInfo['option_rate'] * BillCnt
            IN_ACC_SETTLE_ID = rateInfo['item_id']
            IN_CHARGE_2 = rateInfo['settle_rate'] * BillCnt
            IN_RATE2 = rateInfo['settle_rate']
            CURRENCY = rateInfo['currency']

            if IN_ERROR != IN_ERROR_ORI or IN_OP_CHARGE != IN_OP_CHARGE_ORI or IN_ACC_SETTLE_ID != IN_ACC_SETTLE_ID_ORI or IN_CHARGE_2 != IN_CHARGE_2_ORI or IN_RATE2 != IN_RATE2_ORI or CURRENCY != CURRENCY_ORI:
                iCount = iCount + 1
                lstParam.append({'IN_ERROR':IN_ERROR,'IN_OP_CHARGE':IN_OP_CHARGE,'rowkey':rowKey,'IN_ACC_SETTLE_ID':IN_ACC_SETTLE_ID,'IN_CHARGE_2':IN_CHARGE_2,'IN_RATE2':IN_RATE2,'CURRENCY':CURRENCY})

                if iCount >= 20000:
                    cur = dbConnect.cursor()
                    cur.executemany(sqlUpdate.format(workDay,iHour), lstParam)
                    cur.close()
                    dbConnect.commit()
                    lstParam = []
                    iTotal = iTotal + iCount
                    iCount = 0
    if  iCount > 0:
        cur = dbConnect.cursor()
        cur.executemany(sqlUpdate.format(workDay,iHour), lstParam)
        cur.close()
        dbConnect.commit()
        iTotal = iTotal + iCount
    return iTotal

def getCustomerRate(acctId, direction, billType, dest,sendTime, dictRate):
    global g_dictTrafficCustomer
    key = acctId + "|" + direction + "|" + billType + "|" + dest
    if key in dictRate:
        for rateInfo in dictRate[key]:
            if sendTime >= rateInfo['eff_date'] and sendTime < rateInfo['exp_date']:
                #阶梯计费，会有多条记录
                if rateInfo['charge_mode'] == 2 or rateInfo['charge_mode'] == 3:
                    prodId = rateInfo['product_id']
                    #确保唯一性,使用acctId|prodId作为key
                    keyAdd = "{0}|{1}".format(acctId,prodId)
                    traffic = 0
                    if keyAdd in g_dictTrafficCustomer:
                        traffic = g_dictTrafficCustomer[keyAdd]
                    traffic = traffic + 1

                    if traffic >= rateInfo['start_val'] and traffic <= rateInfo['end_val']:
                        g_dictTrafficCustomer[keyAdd] = traffic
                        return rateInfo
                else:
                    return rateInfo
    return None

def dobyDate(dbConnect, workDay,acctId,dirFlag):
    iTotal = 0
    starttime = datetime.datetime.now()

    for iHour in range(0,24):
        print("do on {0} {1} with acctId {2} dirFlag {3}".format(workDay,iHour,acctId,dirFlag))
        logging.info("do on {0} {1} with acctId {2} dirFlag {3}".format(workDay,iHour,acctId,dirFlag))
        iCnt = 0
        if dirFlag == 1:
            iCnt = doVendorByHour(dbConnect, workDay, iHour,acctId,dirFlag)
        elif dirFlag == 2:
            iCnt = doCustomerByHour(dbConnect, workDay, iHour,acctId,dirFlag)
        elif dirFlag == 0:
            iCnt = doVendorByHour(dbConnect, workDay, iHour,acctId,dirFlag)
            iCnt = iCnt + doCustomerByHour(dbConnect, workDay, iHour,acctId,dirFlag)
        
        logging.info("do on {0} {1} with acctId {2} dirFlag {3} update {4} records".format(workDay,iHour,acctId,dirFlag, iCnt))
        iTotal = iTotal + iCnt

    endtime = datetime.datetime.now()
    iTime = (endtime - starttime).seconds

    if iTotal > 0:
        iSpeed = iTotal
        if iTime > 0:
            iSpeed = iTotal/iTime
        logging.info("total:{0}, speed:{1}".format(iTotal, iSpeed))

    return iTotal

def doStat(dbConnect, workDay,taskId, acctId, dirFlag):
    cur = dbConnect.cursor()

    # in格式acct_id字段长度可能不够，所以插入一个特殊值，汇总时根据redo_task_id从sys_task_args_detail表取实际的acct_id
    if len(acctId) > 32:  
        acctId = "ACCT_ID_TOO_LONG"
    
    # acctId是in格式，含引号，用%s
    sql = '''insert into dps_cmi_action_log (action_id,action_code,action_idx,data_type,busi_type,redo_task_id,work_day,action_state,acct_id,dir_flag,begin_time) 
        values 
        (seq_cmi_action_id.nextval,'sms_stat', 1,1,1,%s, %s, 1, %s, %s,sysdate)'''
    cur.execute(sql, (taskId, workDay, acctId, str(dirFlag)))
    dbConnect.commit()

def waitStatEnd(dbConnect, workDay):
    iCnt = 1

    while iCnt > 0:
        rows = fetchAll(dbConnect, "select count(1) from dps_cmi_action_log where action_code='sms_stat' and action_state <> 0 and  work_day={0} ".format(workDay) ) 
        if not rows is None:
            for row in rows:
                iCnt = row[0]
        time.sleep(60)

def doReportData(dbConnect, workDay):
    # current_month = datetime.datetime.now().strftime("%Y%m")
    # workday_month = workDay[:6]

    # # Monthly data processing for current month
    # if workday_month == current_month:
    #     # Execute stored procedure
    #     try:
    #         cur = dbConnect.cursor()
    #         proc_stat_a2p_cdr_daily.generate_data(dbConnect, workDay)
    #         logging.info("Updated Stat_Cmi_Sms_Temp_Daily for current month {}".format(workDay))
    #     except Exception as err:
    #         print(traceback.format_exc())
    #         logging.error("Unexpected error while updating stats for %s: %s" % (workDay, traceback.format_exc()))

    # Generate data report regardless of whether it's the current month
    today = datetime.date.today().strftime('%Y%m%d')
    if workDay < today:
        try:
            # 当天没有已完成的605任务，才插
            iCnt = 0
            qrySql = """select count(1) from SYS_TASK_LOG a, sys_task_args_detail b 
                        where a.task_code=605 and a.state not in (7,0,-1) and date_trunc('day', a.start_time) = CURRENT_DATE 
                        and a.task_id=b.task_id and b.task_args_code='WORK_DAY' and b.task_args_value='{0}'
                    """.format(workDay)
            rows = fetchAll(dbConnect, qrySql ) 
            if not rows is None:
                for row in rows:
                    iCnt = row[0]
                    break
            
            if iCnt == 0:
                cur = dbConnect.cursor() 
                taskId = getTaskId(dbConnect)
                sql = "insert into  SYS_TASK_LOG (task_id,task_flow_name,task_code,start_time,state) values (" + str(taskId) + ", 'extract a2p', 605,sysdate, 1)"
                cur.execute(sql)
                sql = "insert into sys_task_args_detail (task_id,task_args_code, task_args_value) values (" + str(taskId) + ",'WORK_DAY','" + workDay + "')"
                cur.execute(sql)
                sql = "insert into sys_task_args_detail (task_id,task_args_code, task_args_value) values (" + str(taskId) + ",'BUSI_TYPE','14')"
                cur.execute(sql)
            dbConnect.commit()
        except psycopg2.Error as exc:
            print(traceback.format_exc())
            logging.error("error:", traceback.format_exc())
        except Exception as err:
            print(traceback.format_exc())
            logging.error("error:", traceback.format_exc())
            dbConnect.rollback()
        finally:
            if 'cur' in locals():
                cur.close() 
    else:
        print("workDay %s is today. Skipping database operations." % workDay)

if __name__ == '__main__':
    max_task_running = 8
    strProdPa = os.getenv('PROD_PA')
    strLogDir = strProdPa + "/center/log/python"
    strLogFile = strLogDir + "/a2p_resettle.log"

    # 日志信息里增加进程ID
    # LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
    LOG_FORMAT = '%(asctime)s pid:%(process)d - %(levelname)s - %(message)s'

    DATE_FORMAT = "%m/%d/%Y %H:%M:%S %p"

    logging.basicConfig(filename=strLogFile, level=logging.DEBUG, format=LOG_FORMAT, datefmt=DATE_FORMAT)

    if not os.path.exists(strLogDir):
        os.makedirs(strLogDir)

    print("start....")
    logging.info("start....")
    dbConnect = cmi_common_util.get_gs_connect()
    strToday = datetime.datetime.now().strftime("%Y%m%d")
    currMonth = strToday[0:6]
    lastMonth = "-1"
    # BSCBI_REQ_33283-A2P重批优化与汇总优化-取消自动全量重批任务
    # now = datetime.datetime.now()
    # start_time = now.replace(hour=22, minute=30, second=0, microsecond=0)
    # end_time = now.replace(hour=23, minute=59, second=59, microsecond=0)
    # if start_time <= now <= end_time:
    #     firstDay = strToday[0:6] + '01'
    #     insertTaskAll(dbConnect, firstDay, strToday)
    cntOfTaskRunning = getRuningTaskCnt(dbConnect)

    if cntOfTaskRunning >= 0 and cntOfTaskRunning < max_task_running:
        taskInfo = None

        if len(sys.argv) > 1:
            taskInfo = {}
            taskInfo['task_id'] = -1
            taskInfo['start_date'] = sys.argv[1]
            taskInfo['end_date'] = sys.argv[2]
            taskInfo['account_id'] = sys.argv[3]
            taskInfo['dir_flag'] = sys.argv[4]
            taskInfo['task_flow_name'] = ""
        else:
            taskInfo = getTask2Run(dbConnect)

        if not taskInfo is None:
            try:
                beginDate = taskInfo['start_date']
                endDate = taskInfo['end_date']
                acctId = taskInfo['account_id']  # acctId 可能是："-1", "acctid", "('acctid1', 'acctid2', ...)"
                taskFlowName = taskInfo['task_flow_name']
                dirFlag = int(taskInfo['dir_flag'])

                # 把acctId转成in的格式，后面操条件作用in
                if acctId != "-1": 
                    # acctId中没有括号
                    if "(" not in acctId: 
                        acctId = "('{}')".format(acctId)
                    
                workDay = beginDate
                logging.info("start to resettle {0} (dir:{1}) from {2} to {3} ".format(acctId, dirFlag, beginDate, endDate))

                while (workDay <= endDate):
                    if lastMonth != workDay[0:6]:
                        lastMonth = workDay[0:6]
                        fetchA2PVendorSwap(dbConnect, lastMonth, acctId)
                        fetchA2PVendorPrice(dbConnect, lastMonth, acctId)
                        fetchA2PCustomerSwap(dbConnect, lastMonth, acctId)
                        fetchA2PCustomerPrice(dbConnect, lastMonth, acctId)
                        g_dictTrafficVendor.clear()
                        g_dictTrafficCustomer.clear()
                    iTotal = dobyDate(dbConnect, workDay,acctId,dirFlag)

                    if iTotal > 0:
                        doStat(dbConnect, workDay,taskInfo['task_id'], acctId, dirFlag)
                        
                    logging.info("resettle {0} on {1}, have changed {2} records".format(acctId, workDay, iTotal))
                    workDay = getNextDay(workDay, 1)

                lastMonth = "-1"
                workDay = beginDate

                # BSCBI_REQ_33450-20250701-优化改成不等汇总结束，运营报表通知工单由a2p_gen_operational_task.py脚本定时扫描生成
                # logging.info("wait stat to end")

                # while (workDay <= endDate):
                #     waitStatEnd(dbConnect, workDay)
                #     logging.info("start to report data on {0}".format(workDay))
                #     # 全量重批只取1个方向插任务，2个方向同时插会导致cross表数据重复
                #     # 全量重批 方向直接传0 ,这里判断需要去掉了
                #     # if taskFlowName == 'smsRedoAll' and dirFlag != 1:
                #     #     logging.info("smsRedoAll and dirFlag is not 1 ,pass")
                #     # else:
                #     #     doReportData(dbConnect, workDay)
                #     doReportData(dbConnect, workDay)
                #     workDay = getNextDay(workDay, 1)
                    

                if taskInfo['task_id'] > 0:
                    updateTaskState(dbConnect, taskInfo['task_id'], 7)
                
                logging.info("success to resettle {0} (dir:{1}) from {2} to {3} ".format(acctId, dirFlag, beginDate, endDate))
            except Exception as err:
                print(traceback.format_exc())
                logging.error("error:", traceback.format_exc())
                
    else:
        print("there are {0} tasks in running,too many, please wait".format(cntOfTaskRunning))
        logging.info("There are %d tasks in running, too many, please wait", cntOfTaskRunning)

    dbConnect.close()
    print("end...")
    logging.info("end...")
