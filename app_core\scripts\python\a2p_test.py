#!/bin/python
#-*- coding: utf-8 -*-

#import cx_Oracle
import datetime
import time
import sys
import logging
import os
import traceback
import cmi_common_util


def fetchAll(dbConnect, sql, parameters=None, arraysize=500):
    try:
        cur = dbConnect.cursor()
        if parameters is None:
            cur.execute(sql)
        else:
            cur.execute(sql, parameters)
        rows = cur.fetchall()
        cur.close()
        return rows
    except Exception as e:
        if hasattr(e, 'pgcode') and hasattr(e, 'pgerror'):
            print("PostgreSQL Execute exception, ErrCode: %s, ErrMsg: %s" % (e.pgcode, e.pgerror))
        else:
            print("Execute exception, ErrMsg: %s" % str(e))
    return None
def getrealAcctId(dbConnect, strAcctId, redoTaskId):
    if len(strAcctId) < 3:  # 默认为-1
        strAcctId = "-1"
    elif strAcctId == "ACCT_ID_TOO_LONG": # 从参数表取  
        strAcctId = strAcctId[1:]      
        sql = "select task_args_value from sys_task_args_detail where task_id = {0} and task_args_code = 'ACCT_ID'".format(redoTaskId)
        rows = fetchAll(dbConnect, sql)
        if not rows is None:
            for row in rows:
                strAcctId = str(row[0])
                break
    print("getrealAcctId strAcctId:{}".format(strAcctId))
    return strAcctId


# 每次取N条数据
def getWorkDayk4Collect(dbConnect):
    sql = "select work_day, nvl(acct_id,'-1') as acct_id, dir_flag, redo_task_id from dps_cmi_action_log where action_code='sms_stat' and data_type=0 and action_state=1 order by begin_time, work_day limit {0}".format(1)
    rows = fetchAll(dbConnect, sql)
    haveData = False
    strAcctId = "-1"

    lstTaskInfo = []

    if not rows is None:
        for row in rows:
            taskInfo = {}
            taskInfo["work_day"] = str(row[0])
            oriStrAcctId = str(row[1])
            redoTaskId = int(row[3])

            strAcctId = getrealAcctId(dbConnect, oriStrAcctId, redoTaskId)
            taskInfo["ori_acct_id"] = oriStrAcctId
            taskInfo["acct_id"] = strAcctId
            taskInfo["dir_flag"] = str(row[2])
            taskInfo["redo_task_id"] = redoTaskId
            
            haveData = True
            lstTaskInfo.append(taskInfo)
            
    if not haveData:
        sql = "select work_day, nvl(acct_id,'-1') as acct_id, dir_flag, redo_task_id from dps_cmi_action_log where action_code='sms_stat' and data_type=1 and action_state=1 order by begin_time, work_day limit {0}".format(1)
        rows = fetchAll(dbConnect, sql)

        if not rows is None:
            for row in rows:
                taskInfo = {}
                taskInfo["work_day"] = str(row[0])
                oriStrAcctId = str(row[1])
                redoTaskId = int(row[3])

                strAcctId = getrealAcctId(dbConnect, oriStrAcctId, redoTaskId)
                taskInfo["ori_acct_id"] = oriStrAcctId
                taskInfo["acct_id"] = strAcctId
                taskInfo["dir_flag"] = str(row[2])
                taskInfo["redo_task_id"] = redoTaskId

                lstTaskInfo.append(taskInfo)

    return lstTaskInfo


def startCollect(dbConnect, listTaskInfo):

    # 生成 in (taskid1, taskid2, taskid3)的string
    lisTaskId = [taskInfo["redo_task_id"] for taskInfo in listTaskInfo]
    print("task_id:", lisTaskId)
    strTaskId = cmi_common_util.get_instr_from_list(lisTaskId)
    print("strTaskId:", strTaskId)
    
    sql = "update dps_cmi_action_log set action_state=2 where action_code='sms_stat' and redo_task_id in {0} and action_state=1".format(strTaskId)
    print("sql:", sql)
    cur = dbConnect.cursor()
    cur.execute(sql)
    cur.close()
    
    dbConnect.commit()

def endCollect(dbConnect, listTaskInfo): 
    # 生成 in (taskid1, taskid2, taskid3)的string
    lisTaskId = [taskInfo["redo_task_id"] for taskInfo in listTaskInfo]
    strTaskId = cmi_common_util.get_instr_from_list(lisTaskId)

    sql = "update dps_cmi_action_log set action_state=0, end_time=now() where action_code='sms_stat' and redo_task_id in {0} and action_state=2".format(strTaskId)
    cur = dbConnect.cursor()
    cur.execute(sql)
    cur.close()
    dbConnect.commit()



def test_parse_and_discard():
    # 测试用例：输入字符串 -> 期望输出列表
    test_cases = [
        ("('id1','id2')", ['id1', 'id2']),
        ("()", []),
        ("", []),
        ("(  'id3' , 'id4' , )", ['id3', 'id4']),
        ("('a',   'b' , 'c')", ['a', 'b', 'c']),
        ("('single')", ['single']),
        (None, []),
    ]

    for redoAcctIds_str, expected_list in test_cases:
        print("\nTest Input:", repr(redoAcctIds_str))
        if redoAcctIds_str is None:
            redoAcctIds_str = "()"

        # 原始逻辑片段模拟
        try:
            acctIdList = [aid.strip().strip("'") for aid in redoAcctIds_str.strip("()").split(",") if aid.strip()]
        except Exception as e:
            print("Error during parsing:", str(e))
            continue

        print("Parsed List:", acctIdList)
        print("Expected List:", expected_list)

        # 检查结果是否一致
        assert acctIdList == expected_list, "Failed: {0} != {1}".fromat(acctIdList, expected_list)

        # 模拟 uniqueIdSet
        uniqueIdSet = set(['id1', 'id2', 'id3', 'id4', 'other'])
        print("Before discard:", uniqueIdSet)

        for acctId in acctIdList:
            uniqueIdSet.discard(acctId)

        print("After discard:", uniqueIdSet)

        # 验证是否移除正确
        for acctId in expected_list:
            assert acctId not in uniqueIdSet, "Failed to remove {0}".fromat(acctId)

        print("✅ Test passed.")



if __name__ == '__main__':

    print("start....")
    logging.info("start....")
    dbConnect = cmi_common_util.get_gs_connect()

    strToday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y%m%d")
    print("strToday=", strToday)

    test_parse_and_discard()

    # listTaskInfo = getWorkDayk4Collect(dbConnect)
    # print("history: get {} tasks ...".format(len(listTaskInfo)))
    # print("listTaskInfo:", listTaskInfo)

    # startCollect(dbConnect, listTaskInfo)
    # endCollect(dbConnect, listTaskInfo)