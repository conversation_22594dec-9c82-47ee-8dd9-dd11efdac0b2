#ifndef TABLE_STRUCT_DEFINE_H
#define TABLE_STRUCT_DEFINE_H

#include <string.h>
#include <queue>
#include <vector>
#include <unistd.h>
#include <cdk/foundation/cdk_aimap.h>
#include <cdk/foundation/cdk_aistring.h>
#include <cdk/foundation/cdk_aivector.h>
#include <cdk/foundation/cdk_string.h>
#include <cdk/datetime/cdk_datetime.h>
#include <dbapi.h>

// 将时间戳转换为 otl_datetime - 实现在 table_struct_define.cpp 中
otl_datetime intToOtlDatetime(int timestamp);

// ============================================================================
// 基类定义
// ============================================================================

class CTabelStructBase { 
public:
    virtual ~CTabelStructBase() {}
    
    // 添加虚函数接口用于多态
    virtual void readFromStream(otl_stream& is) = 0;   // select 操作
    virtual void writeToStream(otl_stream& os) const = 0; // insert 操作
    virtual void printToStream(std::ostream& os) const = 0; // 打印

    virtual int32 get_syncFlag() { return 0;} /* 默认实现 */ 
    virtual AISTD string get_strPhoneID(){ return "";} /* 默认实现 */ 
    virtual int32 get_validDate() { return 0;} /* 默认实现 */ 
    virtual void convertI2Bps(const CTabelStructBase& data) { /* 默认空实现 */ }

    // 声明为友元函数
    friend otl_stream& operator>>(otl_stream& is, CTabelStructBase& data);
    friend otl_stream& operator<<(otl_stream& os, const CTabelStructBase& data);
    friend std::ostream& operator<<(std::ostream& os, const CTabelStructBase& data);
};

// 友元函数声明：实现在 table_struct_define.cpp 中
otl_stream& operator>>(otl_stream& is, CTabelStructBase& data);
otl_stream& operator<<(otl_stream& os, const CTabelStructBase& data);
std::ostream& operator<<(std::ostream& os, const CTabelStructBase& data);

// ============================================================================
// 派生类声明
// ============================================================================

class CIDataIndex : public CTabelStructBase {
public:
    AISTD string    m_strPhoneID;       // 用户编号
    AISTD string    m_strUpField;       // 更新标志串
    int32           m_iSyncFlag;        // 同步方式
    int64           m_lBusiCode;        // 业务代码
    int32           m_iRegionCode;      // 地市编号
    int32           m_iCountyCode;      // 区县编号
    int64           m_lOpID;            // 操作员ID
    AISTD string    m_strCommitDate;    // 提交日期，对应表字段COMMIT_DATE，格式为YYYYMMDD
    int64           m_lCommitTime;      // 提交时间，对应表字段COMMIT_DATE的时间戳
    int64           m_lSoNbr;           // 工单号
    AISTD string    m_strRemark;        // 备注

    CIDataIndex() : m_iSyncFlag(0), m_lBusiCode(0), m_iRegionCode(0), m_iCountyCode(0),
                    m_lOpID(0), m_lCommitTime(0), m_lSoNbr(0) {}
    ~CIDataIndex() {}

    // 虚函数声明：实现在 table_struct_define.cpp 中
    int32 get_syncFlag() override;
    AISTD string get_strPhoneID() override;
    void readFromStream(otl_stream& is) override;
    void writeToStream(otl_stream& os) const override;
    void printToStream(std::ostream& os) const override;
    
    // 友元函数声明
    friend otl_stream& operator>>(otl_stream& is, CIDataIndex& rh);
    friend otl_stream& operator<<(otl_stream& os, const CIDataIndex& rh);
    friend std::ostream& operator<<(std::ostream& os, const CIDataIndex& rh);
};

// 友元函数声明：实现在 table_struct_define.cpp 中
otl_stream& operator>>(otl_stream& os, CIDataIndex& data);
otl_stream& operator<<(otl_stream& os, const CIDataIndex& data);
std::ostream& operator<<(std::ostream& os, const CIDataIndex& data);

typedef std::vector<CIDataIndex> ListIDataIndex;

class CIDataIndexHis : public CTabelStructBase {
public:
    AISTD string    m_strPhoneID;       // 手机号码
    AISTD string    m_strUpField;       // 更新标志串
    int32           m_iSyncFlag;        // 同步方式
    int64           m_lBusiCode;        // 业务代码
    int32           m_iRegionCode;      // 地市编号
    int32           m_iCountyCode;      // 区县编号
    int64           m_lOpID;            // 操作员ID
    int64           m_lCommitTime;      // 提交时间，时间戳
    int64           m_lSoNbr;           // 工单号
    int64           m_lDealTime;        // 处理时间，时间戳
    AISTD string    m_strRemark;        // 备注

    CIDataIndexHis() : m_iSyncFlag(0), m_lBusiCode(0), m_iRegionCode(0), m_iCountyCode(0),
                       m_lOpID(0), m_lCommitTime(0), m_lSoNbr(0), m_lDealTime(0) {}
    ~CIDataIndexHis() {}

    // 虚函数声明：实现在 table_struct_define.cpp 中
    int32 get_syncFlag() override;
    AISTD string get_strPhoneID() override;
    void readFromStream(otl_stream& is) override;
    void writeToStream(otl_stream& os) const override;
    void printToStream(std::ostream& os) const override;

    // 友元函数声明
    friend otl_stream& operator>>(otl_stream& os, CIDataIndexHis& data);
    friend otl_stream& operator<<(otl_stream& os, const CIDataIndexHis& data);
    friend std::ostream& operator<<(std::ostream& os, const CIDataIndexHis& data);
};

// 友元函数声明：实现在 table_struct_define.cpp 中
otl_stream& operator>>(otl_stream& os, CIDataIndexHis& data);
otl_stream& operator<<(otl_stream& os, const CIDataIndexHis& data);
std::ostream& operator<<(std::ostream& os, const CIDataIndexHis& data);

typedef std::vector<CIDataIndexHis> ListIDataIndexHis;

class CIDataIndexErr : public CTabelStructBase {
public:
    AISTD string    m_strPhoneID;       // 用户编号
    AISTD string    m_strUpField;       // 更新标志串
    int32           m_iSyncFlag;        // 同步方式
    int64           m_lBusiCode;        // 业务代码
    int32           m_iRegionCode;      // 地市编号
    int32           m_iCountyCode;      // 区县编号
    int64           m_lOpID;            // 操作员ID
    int64           m_lCommitTime;      // 提交时间，时间戳
    int64           m_lSoNbr;           // 工单号
    int32           m_iErrCode;         // 错误代码
    AISTD string    m_strErrMsg;        // 错误信息
    int64           m_lDealTime;        // 处理时间，时间戳
    AISTD string    m_strRemark;        // 备注

    CIDataIndexErr() : m_iSyncFlag(0), m_lBusiCode(0), m_iRegionCode(0), m_iCountyCode(0),
                       m_lOpID(0), m_lCommitTime(0), m_lSoNbr(0), m_iErrCode(0), m_lDealTime(0) {}
    ~CIDataIndexErr() {}

    // 虚函数声明：实现在 table_struct_define.cpp 中
    int32 get_syncFlag() override;
    AISTD string get_strPhoneID() override;
    void readFromStream(otl_stream& is) override;
    void writeToStream(otl_stream& os) const override;
    void printToStream(std::ostream& os) const override;

    // 友元函数声明
    friend otl_stream& operator>>(otl_stream& os, CIDataIndexErr& data);
    friend otl_stream& operator<<(otl_stream& os, const CIDataIndexErr& data);
    friend std::ostream& operator<<(std::ostream& os, const CIDataIndexErr& data);
};

// 友元函数声明：实现在 table_struct_define.cpp 中
otl_stream& operator>>(otl_stream& os, CIDataIndexErr& data);
otl_stream& operator<<(otl_stream& os, const CIDataIndexErr& data);
std::ostream& operator<<(std::ostream& os, const CIDataIndexErr& data);

typedef std::vector<CIDataIndexErr> ListIDataIndexErr;

class CIMsisdnInfo : public CTabelStructBase {
public:
    AISTD string    m_strPhoneID;       // 手机号码
    AISTD string    m_strCountyCode;    // 区县代码
    AISTD string    m_strGridCode;      // 网格代码
    int32           m_iAreaCode;        // 地市代码
    AISTD string    m_strBureauCode;    // 局代码
    int32           m_iUserType;        // 用户类型
    int64           m_lValidDate;       // 生效日期
    int64           m_lExpireDate;      // 失效日期
    AISTD string    m_strReserved1;     // 保留字段1
    AISTD string    m_strReserved2;     // 保留字段2
    AISTD string    m_strReserved3;     // 保留字段3
    int64           m_lSoNbr;           // 工单号
    int32           m_iSyncFlag;        // 同步标志
    int64           m_lCommitTime;      // 提交时间
    AISTD string    m_strRemark;       // 备注

    CIMsisdnInfo() : m_iAreaCode(0), m_iUserType(0), m_lValidDate(0), m_lExpireDate(0),
                     m_lSoNbr(0), m_iSyncFlag(0), m_lCommitTime(0) {}
    ~CIMsisdnInfo() {}

    // 虚函数声明：实现在 table_struct_define.cpp 中
    int32 get_syncFlag() override;
    AISTD string get_strPhoneID() override;
    int32 get_validDate() override;
    void readFromStream(otl_stream& is) override;
    void writeToStream(otl_stream& os) const override;
    void printToStream(std::ostream& os) const override;

    // 友元函数声明
    friend otl_stream& operator>>(otl_stream& os, CIMsisdnInfo& data);
    friend otl_stream& operator<<(otl_stream& os, const CIMsisdnInfo& data);
    friend std::ostream& operator<<(std::ostream& os, const CIMsisdnInfo& rh);
};

// 友元函数声明：实现在 table_struct_define.cpp 中
otl_stream& operator>>(otl_stream& os, CIMsisdnInfo& data);
otl_stream& operator<<(otl_stream& os, const CIMsisdnInfo& data);
std::ostream& operator<<(std::ostream& os, const CIMsisdnInfo& data);

class CINationalMnp : public CTabelStructBase {
public:
    AISTD string    m_strPhoneID;       // 手机号码
    AISTD string    m_strSrcNetID;      // 携出网络标识
    AISTD string    m_strDesNetID;      // 携入网络标识
    AISTD string    m_strOwnNetID;      // 归属网络标识
    int64           m_lValidDate;       // 生效时间
    int64           m_lExpireDate;      // 失效时间
    int32           m_iSyncFlag;        // 同步标志
    int64           m_lSoNbr;           // 工单号
    int64           m_lCommitTime;      // 提交时间
    AISTD string    m_strRemark;       // 备注

    CINationalMnp() : m_lValidDate(0), m_lExpireDate(0), m_iSyncFlag(0),
                      m_lSoNbr(0), m_lCommitTime(0) {}
    ~CINationalMnp() {}

    // 虚函数声明：实现在 table_struct_define.cpp 中
    int32 get_syncFlag() override;
    AISTD string get_strPhoneID() override;
    int32 get_validDate() override;
    void readFromStream(otl_stream& is) override;
    void writeToStream(otl_stream& os) const override;
    void printToStream(std::ostream& os) const override;

    // 友元函数声明
    friend otl_stream& operator>>(otl_stream& os, CINationalMnp& data);
    friend otl_stream& operator<<(otl_stream& os, const CINationalMnp& data);
    friend std::ostream& operator<<(std::ostream& os, const CINationalMnp& rh);
};

// 友元函数声明：实现在 table_struct_define.cpp 中
otl_stream& operator>>(otl_stream& os, CINationalMnp& data);
otl_stream& operator<<(otl_stream& os, const CINationalMnp& data);
std::ostream& operator<<(std::ostream& os, const CINationalMnp& data);

class CBpsMsisdnInfo : public CTabelStructBase {
public:
    AISTD string m_strMsisdn;       // 手机号码
    AISTD string m_strCountyCode;   // 区县代码
    AISTD string m_strGridCode;     // 网格代码
    int32        m_iAreaCode;       // 地市代码
    AISTD string m_strBureauCode;   // 局代码
    int32        m_iUserType;       // 用户类型
    int32        m_iValidDate;      // 生效日期
    int32        m_iExpireDate;     // 失效日期
    AISTD string m_strReserved1;    // 保留字段1
    AISTD string m_strReserved2;    // 保留字段2
    AISTD string m_strReserved3;    // 保留字段3

    CBpsMsisdnInfo() : m_iAreaCode(0), m_iUserType(0), m_iValidDate(0), m_iExpireDate(0) {}
    ~CBpsMsisdnInfo() {}

    void convertI2Bps(const CTabelStructBase& iData);  //结构体转换

    // 虚函数声明：实现在 table_struct_define.cpp 中
    AISTD string get_strPhoneID() override;
    void readFromStream(otl_stream& is) override;
    void writeToStream(otl_stream& os) const override;
    void printToStream(std::ostream& os) const override;

    // 友元函数声明
    friend otl_stream& operator>>(otl_stream& os, CBpsMsisdnInfo& data);
    friend otl_stream& operator<<(otl_stream& os, const CBpsMsisdnInfo& data);
    friend std::ostream& operator<<(std::ostream& os, const CBpsMsisdnInfo& rh);
};

// 友元函数声明：实现在 table_struct_define.cpp 中
otl_stream& operator>>(otl_stream& os, CBpsMsisdnInfo& data);
otl_stream& operator<<(otl_stream& os, const CBpsMsisdnInfo& data);
std::ostream& operator<<(std::ostream& os, const CBpsMsisdnInfo& data);

class CBpsNationMnp : public CTabelStructBase {
public:
    AISTD string m_strPhoneNumber; // 手机号码
    AISTD string m_strSrcNetID;    // 携出网络标识
    AISTD string m_strDestNetID;   // 携入网络标识
    AISTD string m_strOwnNetID;    // 归属网络标识
    int32        m_iValidDate;     // 生效时间
    int32        m_iExpireDate;    // 失效时间

    CBpsNationMnp() : m_iValidDate(0), m_iExpireDate(0) {}
    ~CBpsNationMnp() {}

    void convertI2Bps(const CTabelStructBase& iData);  //结构体转换

    // 虚函数声明：实现在 table_struct_define.cpp 中
    AISTD string get_strPhoneID() override;
    void readFromStream(otl_stream& is) override;
    void writeToStream(otl_stream& os) const override;
    void printToStream(std::ostream& os) const override;

    // 友元函数声明
    friend otl_stream& operator>>(otl_stream& os, CBpsNationMnp& data);
    friend otl_stream& operator<<(otl_stream& os, const CBpsNationMnp& data);
    friend std::ostream& operator<<(std::ostream& os, const CBpsNationMnp& rh);
};

// 友元函数声明：实现在 table_struct_define.cpp 中
otl_stream& operator>>(otl_stream& os, CBpsNationMnp& data);
otl_stream& operator<<(otl_stream& os, const CBpsNationMnp& data);
std::ostream& operator<<(std::ostream& os, const CBpsNationMnp& data);

#endif // TABLE_STRUCT_DEFINE_H
