// 老模板: hb_sj_check_goldcoins_xml_f.expr
// 文件名                                                业务类型             old_cdr_type  new_cdr_type
// goldCoins_detail_recharge_return_ZZZ_YYYYMMDD_NNN.xml 金币充值回退交易明细   108           1262

FILE
{
	PROPERTY
	{
		PROPERTY_FILE_CHECK = NO
		PROPERTY_FILTER_CHECK =	YES	
		PROPERTY_FILE_ENCODE_TYPE = ASCII
		PROPERTY_STACK_ENABLE =	NO
	}
	
    RECORD
	{
		RECORD_NAME = jb_record
		RECORD_TYPE = BILL
		
		RECORD_XDR_OUTPUT DR_TYPE = 4
		RECORD_XDR_OUTPUT SERVICE_ID = 12
        RECORD_XDR_OUTPUT TENANT_ID = 0

		RECORD_CONVERT_FUNC = convert_get_tenantid
		RECORD_CONVERT_FUNC = convert_get_filecode

		FIELD   // 充值回退交易数量
		{
			FIELD_NAME = jb_field_1
			FIELD_NEXT_FIELD = jb_field_2
			FIELD_LEAF = YES
			FIELD_XDRKEY = AMOUNT

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = ListCount
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
			FILTER // 0记录过滤
			{
				FILTER_FUNC_TYPE = filter_eq_single
				FILTER_PARA1 = 0
				FILTER_CAUSE = F0233
			}
		}
        FIELD   // 充值回退交易id
		{
			FIELD_NAME = jb_field_2
			FIELD_NEXT_FIELD = jb_field_3
			FIELD_LEAF = YES
			FIELD_XDRKEY = NK_ORDER_ID

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = TradeId
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
        FIELD   // 充值回退交易时间
		{
			FIELD_NAME = jb_field_3
			FIELD_NEXT_FIELD = jb_field_4
			FIELD_LEAF = YES
			FIELD_XDRKEY = START_TIME

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = TradeTime
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
        FIELD   // 用户手机号码
		{
			FIELD_NAME = jb_field_4
			FIELD_NEXT_FIELD = jb_field_5
			FIELD_LEAF = YES
			FIELD_XDRKEY = PHONE

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = PhoneNumber
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD   // 对应充值交易Id
		{
			FIELD_NAME = jb_field_5
			FIELD_NEXT_FIELD = jb_field_6
			FIELD_LEAF = YES
			FIELD_XDRKEY = EXTTID

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = RechargeTradeId
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
        FIELD   // 对应充值交易时间
		{
			FIELD_NAME = jb_field_6
			FIELD_NEXT_FIELD = jb_field_7
			FIELD_LEAF = YES
			FIELD_XDRKEY = UPDATE_TIME

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = RechargeTradeTime
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD   // 充值回退金币值
		{
			FIELD_NAME = jb_field_7
			FIELD_NEXT_FIELD = jb_field_8
			FIELD_LEAF = YES
			FIELD_XDRKEY = SERVICE_FEE

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = RechargeReturnValue
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
        FIELD   // 充值回退类型
		{
			FIELD_NAME = jb_field_8
			FIELD_NEXT_FIELD = jb_field_9
			FIELD_LEAF = YES
			FIELD_XDRKEY = RPT_FNCL_TYPE

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = ReturnType
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD   // 账本id
		{
			FIELD_NAME = jb_field_9
			FIELD_NEXT_FIELD = jb_field_10
			FIELD_LEAF = YES
			FIELD_XDRKEY = UNIT_CODE

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = GoldRecordId
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
        FIELD   // 金币类型
		{
			FIELD_NAME = jb_field_10
			FIELD_NEXT_FIELD = jb_field_11
			FIELD_LEAF = YES
			FIELD_XDRKEY = GOODS_TYPE

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = CoinsType
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD   // 扣减明细金币值
		{
			FIELD_NAME = jb_field_11
			FIELD_NEXT_FIELD = jb_field_12
			FIELD_LEAF = YES
			FIELD_XDRKEY = ADJUST_FEE

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = GoldValue
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
        FIELD   // 是否定向金币
		{
			FIELD_NAME = jb_field_12
			FIELD_NEXT_FIELD = jb_field_13
			FIELD_LEAF = YES
			FIELD_XDRKEY = RESERVED7

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = IsDirect
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
        FIELD   // 明细充值回退时间
		{
			FIELD_NAME = jb_field_13
			FIELD_NEXT_FIELD = jb_field_14
			FIELD_LEAF = YES
			FIELD_XDRKEY = RECV_TIME

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = RechargeReturnTime
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD   // 交易说明
		{
			FIELD_NAME = jb_field_14
			FIELD_NEXT_FIELD = jb_field_15
			FIELD_LEAF = YES
			FIELD_XDRKEY = PF_NAME

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = Comment
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
        FIELD   // 备用字段1
		{
			FIELD_NAME = jb_field_15
			FIELD_NEXT_FIELD = jb_field_16
			FIELD_LEAF = YES
			FIELD_XDRKEY = RESERVED1

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = Reserve1
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD   // 备用字段2
		{
			FIELD_NAME = jb_field_16
			FIELD_NEXT_FIELD = jb_field_17
			FIELD_LEAF = YES
			FIELD_XDRKEY = RESERVED2

			DECODE
			{
				DECODE_FUNC_TYPE = decode_xml
				DECODE_XML_TAG = Reserve2
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD
		{
			FIELD_NAME = jb_field_17
			FIELD_NEXT_FIELD = NULL
			FIELD_LEAF = YES
			DECODE
			{
				DECODE_FUNC_TYPE = decode_ignore_onerecord
			}
		}
	}
}
