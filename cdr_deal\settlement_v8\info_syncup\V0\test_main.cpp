#include "table_op_define.h"
#include <set>

int32 do_sync2db(otl_connect* m_pOtlConnect, CIDataIndex& dataIndex)
{
    AISTD string strCommitDate = dataIndex.m_strCommitDate;  //工单日期
    int64 lSoNbr = dataIndex.m_lSoNbr;   //工单号
    AISTD string strPhoneFieldName;      //不同业务表，手机号字段名不同
    int32 iRet = 0;
    
    try
    {
        OpTableBaseBase *pOpITable = NULL;
        OpTableBaseBase *pOpBpsTable = NULL;
        if (dataIndex.m_strUpField[0] == '1')  //上发I_MSISDN_INFO -> bps_msisdn_info
        {
            pOpITable = new OpIMsisdnInfo();
            pOpBpsTable = new OpBpsMsisdnInfo();
            strPhoneFieldName = "msisdn";  //bps_msisdn_info
        }
        else if (dataIndex.m_strUpField[1] == '1')  //上发I_NATIONAL_MNP -> bps_nation_mnp
        {
            pOpITable = new OpINationalMnp();
            pOpBpsTable = new OpBpsNationMnp();
            strPhoneFieldName = "phone_number";  //bps_nation_mnp
        }

        // 工单号查询工单表
        pOpITable->set_tableNameByMon(strCommitDate.substr(0, 6));
        AISTD string strQuerySql = pOpITable->get_querySql(" SO_NBR = " + std::to_string(lSoNbr));

        std::vector<CTabelStructBase*> pITableDataList;
        int rowCount = pOpITable->run_querySql(*m_pOtlConnect, strQuerySql, pITableDataList, 0); 
        LOG_TRACE(0, "do_sync2db: Query by so_nbr: %s, return %d rows.", lSoNbr, rowCount);
#ifdef DEBUG_PRINT        
        std::cout << "Query by so_nbr: "<<lSoNbr <<", returned " << rowCount << " rows." << std::endl;
        for (int i = 0; i < rowCount; i++) {
            std::cout<<*pITableDataList[i];
        }
#endif
        if (rowCount < 0)
        {
            iRet = rowCount;
            LOG_ERROR(0, "do_sync2db: Query by so_nbr: %s, return error %d.", lSoNbr, rowCount);
            return iRet;
        }
        // 同步工单表数据到BPS表
        else if (rowCount > 0) { 
            // 同步标志sync_flag
            int32 iSyncFlag = 0;   //0-全量，1-增量

            if (dataIndex.m_iSyncFlag != -1)
            {
                iSyncFlag = dataIndex.m_iSyncFlag;
            }
            else{
                iSyncFlag = pITableDataList[0]->get_syncFlag();
            }
            
            if (iSyncFlag == 0) {  
                //全量同步，先删除，再插入
                // 多条记录号码可能有相同的，也可能有不同的，使用set去重
                std::set<AISTD string> setPhoneID;
                for(int i=0; i<rowCount; i++)
                {
                    AISTD string strPhoneID = pITableDataList[i]->get_strPhoneID();  //按号码同步
                    setPhoneID.insert(strPhoneID);
                }

                // 根据set值生成 in ('number1','number2',...)条件
                AISTD string strInCodition= strPhoneFieldName + " in ('";
                for (auto& strPhoneID : setPhoneID)
                {
                    strInCodition += strPhoneID + "','";
                }
                strInCodition.erase(strInCodition.size()-2, 2);
                strInCodition += ")";

                AISTD string strDeletSql = pOpBpsTable->get_deleteSql(strInCodition);
                LOG_TRACE(0, "strDeletSql in FULL=[%s]", strDeletSql.c_str());
                pOpBpsTable->run_deleteSql(*m_pOtlConnect, strDeletSql);
                
                // 转结构体：I表结构到Bps表结构
                std::vector<CTabelStructBase*> vecBpsData;
                for(int i=0; i<rowCount; i++)
                {
                    CTabelStructBase* pBpsData = pOpBpsTable->createDataObject();
                    pBpsData->convertI2Bps(*pITableDataList[i]);
                    vecBpsData.push_back(pBpsData);
                }

                // 插入
                iRet = pOpBpsTable->run_insertSql(*m_pOtlConnect, vecBpsData);
                // 清除vecBpsData
                for (size_t i = 0; i < vecBpsData.size(); i++)
                {
                    delete vecBpsData[i];
                }
                vecBpsData.clear();
            }
            else{   
                // 增量同步: 按号码+生效时间查找，有则更新（删除、再插入），无则插入
                //          等同于直接按"号码+生效时间"删除，再插入
                std::vector<CTabelStructBase*> vecBpsData;
                for (int i=0; i<rowCount; i++)
                {
                    // 根据手机号+生效时间 删除记录，再插入
                    AISTD string strPhoneID = pITableDataList[i]->get_strPhoneID(); 
                    AISTD string strCodition = strPhoneFieldName + " = '" + strPhoneID + "' and VALID_DATE = " + std::to_string(pITableDataList[i]->get_validDate());

                    AISTD string strDelSql = pOpBpsTable->get_deleteSql(strCodition);
                    std::cout<<"strDelSql in Increment ="<<strDelSql<<std::endl;
                    LOG_TRACE(0, "strDelSql in Increment=[%s]", strDelSql.c_str());

                    pOpBpsTable->run_deleteSql(*m_pOtlConnect, strDelSql);
                    
                    CTabelStructBase* pBpsData = pOpBpsTable->createDataObject();
                    pBpsData->convertI2Bps(*pITableDataList[i]);
                    vecBpsData.push_back(pBpsData);
                }
                // 插入
                iRet = pOpBpsTable->run_insertSql(*m_pOtlConnect, vecBpsData);
                // 清除vecBpsData
                for (size_t i = 0; i < vecBpsData.size(); i++)
                {
                    delete vecBpsData[i];
                }
                vecBpsData.clear();
            }
        }
        // 清理查询结果
        for(int i=0; i<rowCount; i++)
        {
            delete pITableDataList[i];
        }
        pITableDataList.clear();
    }
    catch(otl_exception& p)
    {
        LOG_ERROR(0, "OTL exception: %s\n%s\n%s\n", (char *)p.msg, p.stm_text, p.var_info);
        return -1;
    }
    
    return iRet;
}

int main() {
    std::string connection_str = "jsbd_hb";
    std::string user = "jsbd";
    std::string password = "panwei#123";

    OpTableBaseBase *pOpTable = NULL;
    OpTableBaseBase *pOpTable2 = NULL;

    // OpIDataIndex op_IDataIndex;
    // pOpTable = &op_IDataIndex;
    pOpTable = new OpIDataIndex();
    pOpTable2 = new OpIMsisdnInfo();

    try{
        otl_connect db;
        db.rlogon((user + "/" + password + "@" + connection_str).c_str());

        // 插入操作 - 使用对象向量（更安全）
        // std::vector<CIDataIndex> dataList;
        std::vector<CTabelStructBase*> dataList;
        std::vector<CTabelStructBase*> dMsisdnInfoList;
        for (int i = 0; i < 3; i++)
        {
            // CIDataIndex data;
            // data.m_strPhoneID = "1234567890123";
            // data.m_strUpField = "1000000";
            // data.m_iSyncFlag = -1;
            // data.m_lBusiCode = 123456789012345;
            // data.m_iRegionCode = 1234;
            // data.m_iCountyCode = i;
            // data.m_lOpID = 987654321;
            // data.m_lCommitTime = 1750144340;
            // data.m_lSoNbr = 1000000+i;
            // data.m_strRemark = "This is a remark";

            CTabelStructBase* pbaseObj = pOpTable->createDataObject();
            CIDataIndex* pIDataIndex = dynamic_cast<CIDataIndex*>(pbaseObj);

            pIDataIndex->m_strPhoneID = "1234567890"+std::to_string(i);
            pIDataIndex->m_strUpField = "1000000";
            pIDataIndex->m_iSyncFlag = 1;
            pIDataIndex->m_lBusiCode = 123456789012345;
            pIDataIndex->m_iRegionCode = 1234;
            pIDataIndex->m_iCountyCode = i;
            pIDataIndex->m_lOpID = 987654321;
            pIDataIndex->m_lCommitTime = 1750144340;
            pIDataIndex->m_lSoNbr = 1000000+i;
            pIDataIndex->m_strRemark = "This is a remark";

            // dataList.push_back(data);
            // dataList.push_back(&data);
            dataList.push_back(pIDataIndex);


            // CIMsisdnInfo dMsisdnInfo;
            CTabelStructBase* pbaseObj2 = pOpTable2->createDataObject();
            CIMsisdnInfo* pobjMsisdnInfo = dynamic_cast<CIMsisdnInfo*>(pbaseObj2);

            pobjMsisdnInfo->m_strPhoneID = "1234567890"+std::to_string(i);
            pobjMsisdnInfo->m_strCountyCode = "1234";
            pobjMsisdnInfo->m_strGridCode = "1234";
            pobjMsisdnInfo->m_iAreaCode = 1234;
            pobjMsisdnInfo->m_strBureauCode = "1234";
            pobjMsisdnInfo->m_iUserType = 1;
            pobjMsisdnInfo->m_lValidDate = 1750144340;
            pobjMsisdnInfo->m_lExpireDate = 1790144340;
            pobjMsisdnInfo->m_lSoNbr = 1000000+i;
            pobjMsisdnInfo->m_iSyncFlag = 0;

            // dMsisdnInfoList.push_back(&dMsisdnInfo);
            dMsisdnInfoList.push_back(pobjMsisdnInfo);
        }

        // 使用对象向量调用插入函数
        int insertCount = pOpTable->run_insertSql(db, dataList);
        std::cout << "Inserted " << insertCount << " rows." << std::endl;
        if (insertCount <= 0)
        {
            std::cout << "no data inserted." << std::endl;
        }

        // 插入CIMsisdnInfo
        pOpTable2->set_tableNameByMon("202506");
        insertCount = pOpTable2->run_insertSql(db, dMsisdnInfoList);
        std::cout << "Inserted CIMsisdnInfo " << insertCount << " rows." << std::endl;
        if (insertCount <= 0)
        {
            std::cout << "no data inserted." << std::endl;
        }

        for(auto& pObj : dataList)
        {
            delete pObj;
        }
        dataList.clear();

        for(auto& pObj : dMsisdnInfoList)
        {
            delete pObj;
        }
        dMsisdnInfoList.clear();

        // 查询操作
        std::vector<CTabelStructBase*> resDataList;
        // int rowCount = op_IDataIndex.run_querySql(db, op_IDataIndex.get_querySql(), resDataList);
        int rowCount = pOpTable->run_querySql(db, pOpTable->get_querySql(" 1=1"), resDataList, 0); // 显式传递参数
        std::cout << "Query returned " << rowCount << " rows." << std::endl;
        for (int i = 0; i < rowCount; i++) {
            // resDataList[i].print(std::cout);
            std::cout<<*resDataList[i];

            do_sync2db(&db, (*static_cast<CIDataIndex*>(resDataList[i])));

            delete resDataList[i];
        }
        resDataList.clear();

        OpIDataIndexHis op_IDataIndexHis;
        pOpTable = &op_IDataIndexHis;

        // 插入操作
        std::vector<CTabelStructBase*> dataListHis;
        for (int i = 0; i < 3; i++)
        {
            CIDataIndexHis data;
            data.m_strPhoneID = "123456789012345678901234567";
            data.m_strUpField = "1000000";
            data.m_iSyncFlag = 1;
            data.m_lBusiCode = 123456789012345;
            data.m_iRegionCode = 1234;
            data.m_iCountyCode = i;
            data.m_lOpID = 987654321;
            data.m_lCommitTime = 1750144340;
            data.m_lSoNbr = 1234567890+i;
            data.m_strRemark = "This is a remark";
            data.m_lDealTime = 1750144340;

            dataListHis.push_back(&data);
        }
        pOpTable->set_tableNameByMon("202506");
        // insertCount = pOpTable->run_insertSql(db, dataListHis);
        // std::cout << "Inserted i_data_index_his" << insertCount << " rows." << std::endl;
        // if (insertCount <= 0)
        // {
        //     std::cout << "no data inserted." << std::endl;
        // }

        // 查询操作
        std::vector<CTabelStructBase*> resDataListHis;
        // int rowCount = op_IDataIndex.run_querySql(db, op_IDataIndex.get_querySql(), resDataList);
        rowCount = pOpTable->run_querySql(db, pOpTable->get_querySql(" 1=1"), resDataListHis, 0); // 显式传递参数
        std::cout << "Query returned " << rowCount << " rows." << std::endl;
        for (int i = 0; i < rowCount; i++) { 
            // resDataList[i].print(std::cout);
            std::cout<<*resDataListHis[i];
            delete resDataListHis[i];
        }
        resDataListHis.clear();


        db.logoff();
    }
    catch (otl_exception& e) { 
        std::cout << "Error: " << e.msg <<"VAR_INFO:"<< e.var_info << std::endl;
    }
    

    return 0;
}