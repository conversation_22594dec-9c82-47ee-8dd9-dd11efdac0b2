--*****************************************************************************
-- *	file:	mread.expr
-- *  brief:
-- *		define the rule of uploader gsm's "mread" file.
-- *		定义上发 全网手机阅读业务 文件通用分析模板
-- *
-- *	Copyright (c) 2003,2004,2005 Asiainfo Technologies(China),Inc.
-- *
-- *	RCS: $Id: mread.expr,v 1.1.2.55 2014/01/15 07:15:36 nijj Exp $
-- *
-- *	History:
-- *		2009-09-07  hexb  create
-- ****************************************************************************
require "libluabaseD"
require "libluadebugD"
	
	local logtrace = libluadebugD.report_trace
 	local logerror = libluadebugD.report_error
	
	<% use("settle_Xdr_app.MXdr::SXdr") %>
    --为支持reset模板可以使用，定义为全局变量，去掉local
    pSubCommon = nil;               --SXdr.SSubXdr.SSubCommon
    pGsmInfo = nil;                 --SXdr.SSubXdr.SGsmInfo

    pFileOpInfo = nil;
    pOriCharge = nil;
    local iDoPrintInfo	= 1;	            --1:打印调试信息到log,	0:不打印调试信息到log
	
	t_sEmpty		= " ";
	t_sDealDateTime	= "";
	t_sOutRecord		= "";
	t_sProvCode		= "791"; --省代码

	--!组织头、尾、空记录，统计、监控信息
	t_iProcNo	= 0;

	t_iValidNumber	= 0;
	t_iErrorNumber	= 0;
	t_iRecordNumber	= 0;
	t_iDupNumber	= 0;
	t_iLateNumber	= 0;
	t_iSecondCdr	= 0;
	t_iErrLater		= 0;
	t_iErrLater     = 0;
	t_iErrLaterOnce = 0;

	t_sBeginTime	= "20300101000000";
	t_sEndTime	= "20000101000000";

	t_lTotalInfoFee		= 0;
	t_lTotalDiscountFee     = 0;

	--Get Next Upload Time.
	t_sNextUploadTime = "";
	iRes,t_sDealDateTime		= gethosttime();
	t_sDate		= "";
	
  

--[[============================================================================
--函数名称: init_getsdlval
--作    用: 初始化指针
--输入参数:
--输出参数:
--返回值：
--==========================================================================--]]
function init_getsdlval()
	if iDoPrintInfo == 1
	then	
        logtrace('----------init_getsdlval begin-------------');
    end

    pSubXdr = <%get_struct_value('PXdr',"MXdr::SXdr.TOTAL_XDR") %>
        
	if pSubXdr == nil
	then
		if iDoPrintInfo == 1
		then
            logtrace('----------init_getsdlval failed.-------------');
        end
        return -1;
    end
        
	------  init pCommon  ------  
	pSP = <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SP_INFO62") %>;
	pSubCommon		= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SUB_COMMON") %>
	pFileOpInfo  	= <%get_struct_value('pSubCommon', "MXdr::SSubCommon.FILE_OP_INFO")%>;
	if pSJSMS_INFO60 == nil or pSubCommon == nil or pFileOpInfo == nil
	then
		if iDoPrintInfo == 1
		then
			logtrace('----------init_getsdlval failed.-------------');
		end
		return -1;
	end;

	if iDoPrintInfo == 1
	then
		logtrace('----------init_getsdlval end-------------');
	end
	return 0;
end

local function head_record()
	iRes, t_sDealDateTime = GetHostTimeR();
	t_iProcNo	= t_iFileNo;
	if(t_iProcNo == nil or t_iProcNo == "")
	then
		t_iProcNo	= 0;
	end
	t_sNextUploadTime = t_sUploadTime;
	t_sDate		= tostring(t_iDate);
	--t_sDealDateTime = "2024042421050000"

	if(t_iDate < tonumber(string.sub(t_sDealDateTime,0,8)))
	then
		t_sDealDateTime	= t_iDate.."235501";
	else
		if(tonumber(t_iDate) > tonumber(string.sub(t_sDealDateTime,0,8)))
		then
			t_sDealDateTime = t_iDate.."000000"
		end
	end


	t_iLaterOnceNumber = 0;
	if ( t_iProcNo ~= 0 )then
		t_iLaterOnceNumber = t_iLaterOnceNum;
	end

	t_sOutRecord = "1046000"
		.. lpad(t_sProvCode, " ", 3)
		.. rpad(t_sEmpty, " ", 2)
		.. rpad(t_sEmpty, " ", 8)
		.. rpad("46000000"," ",10)
		.. lpad(t_iProcNo, "0", 3)
		.. rpad(t_sEmpty, " ", 20)
		.. lpad(t_sDealDateTime, " ", 14)
		.. "01"
		.. rpad(t_sEmpty, " ", 129)
		;

	t_iResult	= 0;

	return;
end
	
local function file_name()
	------------start reset---------
	t_sDealDateTime="";
	
	--!组织头、尾、空记录，统计、监控信息
	t_iProcNo		= 0;
	t_iValidNumber	= 0;
	t_iErrorNumber	= 0;
	t_iRecordNumber	= 0;
	t_iDupNumber	= 0;

	t_iSecondCdr	= 0;
	t_iErrLater		= 0;
	t_iErrLater		= 0;
	t_iErrLaterOnce	= 0;
	t_sBeginTime	= "20300101000000";
	t_sEndTime		= "20000101000000";
	t_iLaterOnceNumber = 0;
	t_sNextUploadTime = "";
	t_lTotalInfoFee = 0;
	t_lTotalDiscountFee = 0;
	t_sDate		= "";
	sFileType	= "";
	
	t_iResult	= 0;
	t_sOutRecord = "";
	t_sOutFileName	= "";
	------------end reset---------

	sFileType	= t_sFileType;
	t_iProcNo	= t_iFileNo;
	t_sDate		= tostring(t_iDate);
	--modify by niewl 20160809, BIZBILLING_REQ_172081:add t_sOutFileName  = "";	
	t_sOutFileName	= sFileType
			.. t_sDate
			.. lpad(tostring(t_iProcNo), "0", 3)
			.. "."
			.. t_sProvCode;

	t_iResult	= 0;

	return;
end

local function tail_record()

		t_iProcNo	= t_iFileNo;
		if(t_iProcNo == nil or t_iProcNo == "")
		then
			t_iProcNo	= 0;
		end
		t_sOutRecord	= "90" 
			.. rpad("46000000"," ",10)
			.. rpad(t_sEmpty, " ", 8)
			.. "46000"
			.. lpad(t_sProvCode, " ", 3)
			.. rpad(t_sEmpty, " ", 2)
			.. lpad(t_iProcNo, "0", 3)
			.. lpad(t_iValidNumber, "0", 9)
			.. rpad(t_sEmpty, " ", 156);
	t_iResult	= 0;
	return;
end

local function null_body_record()
	t_sOutRecord	= "";
	t_iResult	= 0;

	return;
end
	
	
local function body_record()

	pSP = <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SP_INFO62") %>;
	pSubCommon		= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SUB_COMMON") %>
	--模版分析XDR系列变量定义
	t_sSmsSeq		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.MSG_ID")%>;
	t_iCdrType		=    <%get_struct_value('pSP', "MXdr::SSjSpInfo.ORI_CDR_TYPE")%>;
	t_iUserType	    =  <%get_struct_value('pSP', "MXdr::SSjSpInfo.USER_TYPE")%>;
	t_sChargeDN	    =   <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN")%>;
	t_sSpCode		=  <%get_struct_value('pSP', "MXdr::SSjSpInfo.SP_CODE")%>;
	t_sThirdDN 		=  <%get_struct_value('pSubCommon', "MXdr::SSubCommon.ADN")%>;
	t_sServCode	    =   <%get_struct_value('pSP', "MXdr::SSjSpInfo.SERV_CODE")%>;
	t_sOperCode  	=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.OPER_CODE")%>;
	t_iChargeType	=    <%get_struct_value('pSP', "MXdr::SSjSpInfo.CHARGE_TYPE")%>;
	t_iInfoFee		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.INFO_FEE")%>;
	t_iMonthFee	    =    <%get_struct_value('pSP', "MXdr::SSjSpInfo.MONTH_FEE")%>;
	t_sStatus		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.DATA_TYPE")%>;
	t_iPriority		=  <%get_struct_value('pSP', "MXdr::SSjSpInfo.PRIORITY")%>;
	t_sInfoLen		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.INFO_LEN")%>;
	t_sChrgProv		= <%get_struct_value('pSP', "MXdr::SSjSpInfo.CHRG_PROV")%>;
	t_sIsmgCode	    =   <%get_struct_value('pSP', "MXdr::SSjSpInfo.ISMG_CODE")%>;
	t_sForwIsmg		=    <%get_struct_value('pSP', "MXdr::SSjSpInfo.FWD_ISMG")%>;
	t_sSmscCode	    =    <%get_struct_value('pSP', "MXdr::SSjSpInfo.SMSC_CODE")%>;
	t_sApplyT		=   <%get_struct_value('pSubCommon', "MXdr::SSubCommon.START_TIME")%>;
	t_sFinishT		=    <%get_struct_value('pSubCommon', "MXdr::SSubCommon.FINISH_TIME")%>;
	t_sMsgSrcType = <%get_struct_value('pSP', "MXdr::SSjSpInfo.SUB_TYPE")%>;
	t_iDrType 	  =   <%get_struct_value('PXdr', "MXdr::SXdr.DR_TYPE")%>;  --for error
	--t_iErrcode      = trimx(IA5STRING(getseppos(t_ori, sepp("82:;")))," ");

	t_sChargeDN = t_sThirdDN;

	t_iRecType = 20;
	t_sEcCode = "";
	t_sSiCode = "";
	
	if(tonumber(t_iCdrType)  ~=  1)
	then
		t_sErrNo = "F8030";
		return error_deal(pSubCommon,t_sErrNo);
	end
	
	if(t_sStatus  ~=  "0")
	then
		t_sErrNo = "F8110";
		return  error_deal(pSubCommon,t_sErrNo);
	end

	--add by zhufd at 20200902 for not update 12329
	if(t_sServCode == "12329" and t_sOperCode == "MZJ6910901")
	then
		t_sErrNo = "F12329";
		return error_deal(pSubCommon,t_sErrNo);
	end

	if((string.len(t_sSmsSeq) ~= 20 and string.len(t_sSmsSeq) ~= 18) or is_number(t_sSmsSeq) ~= 1)
    then
        t_sErrNo = "F8020";
        return error_deal(pSubCommon,t_sErrNo);
    end



	t_iApplyTime = libluabaseD.gettime(t_sApplyT);
	if(t_iApplyTime == -1)
	then
		t_sErrNo = "F8170";
		return error_deal(pSubCommon,t_sErrNo);
	end

	t_iUserType = 0;
	if(string.len(t_sThirdDN) ~= 11 or is_number(t_sThirdDN) ~= 1)
	then
		t_sErrNo = "F8080";
		return error_deal(pSubCommon,t_sErrNo);
	end
	iRes,iOperatorId,iUserType,iNetType,strHlrCode,strAreaCode = LookupHlrInfo(t_sThirdDN,string.sub(t_sApplyT,0,8));
    l_TempOperId = iOperatorId;
    if(l_TempOperId==0)
    then
        t_sErrNo = "F8081";
        return error_deal(pSubCommon,t_sErrNo);
	else
		t_sHomeArea =strAreaCode
		iRes,t_sHomeProv = GetProvByAreaCode(t_sHomeArea,string.sub(t_sApplyT,0,8));
		if(t_sHomeProv  ~=  t_sProvCode)
		then
			t_sErrNo = "F8082";
			return error_deal(pSubCommon,t_sErrNo);
		end
    end

	iFind,iOperId,strSPCode,strServCode1,strServCode2,strAccArea,iBusiType,iPalType,iServRange = LookupSpInfo(t_sServCode,string.sub(t_sApplyT,0,8));
	l_TempSpInfoFind = iFind;
	if(l_TempSpInfoFind == 0)
    then
		logtrace('----LookupSpInfo-------------------------------'..iFind..'--'..t_sServCode);
        t_sErrNo = "F8091";
        return error_deal(pSubCommon,t_sErrNo);
    end
	t_sSendProv = strAccArea;
    if(t_sSendProv == t_sProvCode)
    then
        t_sErrNo = "F8092";
        return error_deal(pSubCommon,t_sErrNo);
    end
	--Modified on 20190123 取消SERV_CODE值的重置逻辑，保持平台原值上发
	--t_sServCode = GetSpInfoServCode2(l_TempSpInfo);
	--I_paltype=GetSpInfoPalType(l_TempSpInfo);
	if(iPalType == 5)
	then
		-- 5 免结算
		t_sErrNo = "F8093";
		return error_deal(pSubCommon,t_sErrNo);
	end

	I_servrange=iServRange;
	if(I_servrange == 0)
	then
		-- 0 不提供服务
		t_sErrNo = "F8094";
		return error_deal(pSubCommon,t_sErrNo);
	end

	
	

	if(t_iPriority < 0 or t_iPriority > 9)
	then
		t_sErrNo = "F8120";
		return error_deal(pSubCommon,t_sErrNo);
	end

	if(is_number(t_sInfoLen) ~= 1)
	then
		t_sErrNo = "F8130";
		return error_deal(pSubCommon,t_sErrNo);
	end

	iRes,t_sIsmgProv = LookupIsmgProv(t_sIsmgCode,string.sub(t_sApplyT,0,8));
	if(t_sIsmgProv == "")
	then
		t_sErrNo = "F8140";
		return error_deal(pSubCommon,t_sErrNo);
	elseif(t_sIsmgProv  ~=  t_sProvCode)
	then
		t_sErrNo = "F8141";
		return error_deal(pSubCommon,t_sErrNo);
	end

	if(string.len(t_sSmscCode) ~= 11 or string.sub(t_sSmscCode,1,5) ~= "13800")
	then
		t_sErrNo = "F8160";
		return error_deal(pSubCommon,t_sErrNo)
	end
	g_iBeforeMins = 0;
	g_iLaterMins = 0;
	iTempBeforeMins = 0;
    iTempBeforeMins = 0 + g_iBeforeHours;
    iTempLaterMins = 0;

    iTempLaterMins = 0 + g_iBeforeHoursForward;

	sTempDateTime1   = adddt(t_sDealDateTime, iTempBeforeMins*60);
	sTempDateTime2   = adddt(t_sDealDateTime, -(iTempLaterMins*60));
	if((t_iApplyTime > libluabaseD.gettime(sTempDateTime1)) or (t_iApplyTime < libluabaseD.gettime(sTempDateTime2)))
	then
		t_sErrNo = "F8171";
		return error_deal(pSubCommon,t_sErrNo);
	end
	
	t_iFinishTime = libluabaseD.gettime(t_sFinishT);
	if(t_iFinishTime == -1)
	then
		t_sErrNo = "F8180";
		return error_deal(pSubCommon,t_sErrNo);
	end	
	if(t_iFinishTime < t_iApplyTime)
	then
		t_sErrNo = "F8181";
		return error_deal(pSubCommon,t_sErrNo);
	end
	
	timeDiff = tonumber(t_iFinishTime) - tonumber(t_iApplyTime)
	logtrace("test code timeDiff: "..timeDiff)	
	
	if(timeDiff > (72*60*60))
	then
		t_sErrNo = "F8182";
		return error_deal(pSubCommon,t_sErrNo);
	end
	if(t_sMsgSrcType  ~=  1 and t_sMsgSrcType  ~=  2 and t_sMsgSrcType  ~=  3)
	then
		t_sErrNo = "F8190";
		return error_deal(pSubCommon,t_sErrNo);
	end

	if (20 == t_iDrType)
	then
	  sTempKey = t_sSmsSeq..t_sThirdDN..t_sFinishT.."|"..t_iDrType; 
	else
	  sTempKey = t_sSmsSeq..t_sThirdDN..t_sFinishT;
	end
	  
	--[[t_iCheckFlag = checkdup(sTempKey,string.sub(t_sFinishT,0,8),string.len(sTempKey),$DupDir);
	if(t_iCheckFlag == -1)
	then
		t_sErrNo = "F200";
		return error_deal(pSubCommon,t_sErrNo);
	end
	if(t_iCheckFlag < 0)
	then
		t_sErrNo = "F888";
		return error_deal(pSubCommon,t_sErrNo);
	end
	--]]
	
	t_iCheckFlag = checkdup(0,0);
	if(t_iCheckFlag == -1)
	then
		t_sErrNo = "F8200";
		return error_deal(pSubCommon,t_sErrNo);
	end
	if(t_iCheckFlag < 0)
	then
		t_sErrNo = "F8888";
		return error_deal(pSubCommon,t_sErrNo);
	end
	
	
	--!组织体记录
		
		t_sOutRecord = tostring(t_iRecType)
        .. rpad(t_sSmsSeq," ",20)
        .. lpad(tostring(t_iCdrType), "0", 2)
        .. tostring(t_iUserType)
        .. rpad(t_sChargeDN, " ",15)
        .. rpad(t_sEcCode," ",18)
        .. rpad(t_sSiCode, " ", 12)
        .. rpad(t_sThirdDN, " ",15)
        .. rpad(t_sServCode, " ",21)
        .. rpad(t_sOperCode, " ",10)
        .. rpad(t_sStatus," ",7)
        .. tostring(t_iPriority)
        .. lpad(t_sInfoLen,"0",3)
        .. lpad(t_sIsmgCode,"0",6)
        .. lpad(t_sForwIsmg,"0",6)
        .. t_sSmscCode
        .. rpad(t_sApplyT, " ", 14)
        .. rpad(t_sFinishT, " ", 14)
        .. rpad(t_sMsgSrcType," ",4)
        .. rpad(t_sEmpty, " ", 16)
        ;
		 	
		if(string.len(t_sOutRecord) ~= 198)
		then
			t_sErrNo = "F1100";
			return error_deal(pSubCommon,t_sErrNo);
		end
		--[[if (is_includeChinese(t_sOutRecord))
		then
			t_sErrNo = "F0102";
			return error_deal(pSubCommon,t_sErrNo);
		end--]]
		t_iValidNumber	= t_iValidNumber + 1;
		t_iResult	= 0;
	return;
end


function upload_main()
	if iDoPrintInfo == 1 then
		logtrace('------------------------------upload_mread_main begin--------------------------------');
	end

	--0:head 1:tail 2:body 3:null body 4:filename 
	if(t_iLocation == 0) then
		head_record();
	elseif(t_iLocation == 1) then
		tail_record();
	elseif(t_iLocation == 2) then
		init_getsdlval();
		body_record();
	elseif(t_iLocation == 3) then
		null_body_record();
	elseif(t_iLocation == 4) then
		file_name();
	end

	if iDoPrintInfo == 1 then
		logtrace('------------------------------upload_mread_main end--------------------------------');
	end
end


function error_deal(pSubCommon,sErrorCode)
	<%set_struct_value('pFileOpInfo', "MFileOp::SFileOp.TREAT_FLAG",1)%>
	<%set_struct_value('pFileOpInfo',"MFileOp::SFileOp.ERROR_CODE",sErrorCode) %>
	logtrace("error_deal,errocode:"..sErrorCode)
	
	--!累加错单记录
	t_iErrorNumber	= t_iErrorNumber + 1;
	t_sOutRecord = t_sErrNo .. "000000" 
			.. t_iRecType
			.. rpad(t_sSmsSeq," ",20)
			.. lpad(t_iCdrType, "0", 2)
			.. t_iUserType
			.. rpad(t_sChargeDN, " ",15)
			.. rpad(t_sEcCode," ",18)
			.. rpad(t_sSiCode, " ", 12)
			.. rpad(t_sThirdDN, " ",15)
			.. rpad(t_sServCode, " ",21)
			.. rpad(t_sOperCode, " ",10)
			.. rpad(t_sStatus," ",7)
			.. t_iPriority
			.. lpad(t_sInfoLen,"0",3)
			.. lpad(t_sIsmgCode,"0",6)
			.. lpad(t_sForwIsmg,"0",6)
			.. t_sSmscCode
			.. rpad(t_sApplyT, " ", 14)
			.. rpad(t_sFinishT, " ", 14)
			.. rpad(t_sMsgSrcType," ",4)
			.. rpad(t_sEmpty, " ", 16)
			;
		 	
	t_iResult	= 1;
end

