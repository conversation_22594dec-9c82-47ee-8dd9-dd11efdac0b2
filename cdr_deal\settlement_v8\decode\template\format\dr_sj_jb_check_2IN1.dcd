// 老模板: hb_sj_check_goldcoins_book_f.expr
// 文件名                                           业务类型                             old_cdr_type  new_cdr_type
// goldCoins_detail_fileCheck_ZZZ_YYYYMMDD_NNN.txt  金币交易明细相关同步文件的校验文件           110      1264
// goldCoins_fileCheck_ZZZ_YYYYMMDD_NNN.txt         台账及定向金币交易相关同步文件的校验文件接口 1182      1267


FILE
{
	PROPERTY
	{
		PROPERTY_FILE_CHECK = NO
		PROPERTY_FILTER_CHECK = YES
		PROPERTY_FILE_ENCODE_TYPE = ASCII
		PROPERTY_STACK_ENABLE = NO
	}
	RECORD
    {
        RECORD_NAME = d_control_record
        RECORD_TYPE = CONTROL

        FIELD_NAME = BEGIN CASE 20 RECORD_NEXT_RECORD = body_record
        FIELD_NAME = BEGIN CASE 90 RECORD_NEXT_RECORD = other_record

        FIELD
        {
            FIELD_NAME = BEGIN
            FIELD_NEXT_FIELD = NULL
            FIELD_LEAF = YES
            DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
            DEAL
            {
                DEAL_FUNC_TYPE = str2str_byList
                DEAL_PARA1 = 20,other
                DEAL_PARA2 = 20,90
            }
        }
    }
    //tail
    RECORD
    {
        RECORD_NAME = other_record
        RECORD_TYPE = TAIL

		RECORD_XDR_OUTPUT FILTER_CODE = F9007
    }
    //body
	RECORD
	{
		RECORD_NAME = body_record
		RECORD_TYPE = BILL

		RECORD_XDR_OUTPUT DR_TYPE = 4
		RECORD_XDR_OUTPUT SERVICE_ID = 12
        RECORD_XDR_OUTPUT TENANT_ID = 0

		RECORD_CONVERT_FUNC = convert_get_tenantid
		RECORD_CONVERT_FUNC = convert_get_filecode

		FIELD	// 接口编码
		{
			FIELD_NAME = BEGIN
			FIELD_NEXT_FIELD = jb_check_2IN1_field_1
			FIELD_LEAF = YES
			FIELD_XDRKEY = TID

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 日期
		{
			FIELD_NAME = jb_check_2IN1_field_1
			FIELD_NEXT_FIELD = jb_check_2IN1_field_2
			FIELD_LEAF = YES
			FIELD_XDRKEY = START_TIME

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 文件数
		{
			FIELD_NAME = jb_check_2IN1_field_2
			FIELD_NEXT_FIELD = jb_check_2IN1_field_3
			FIELD_LEAF = YES
			FIELD_XDRKEY = REFUND_FEE

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 记录数
		{
			FIELD_NAME = jb_check_2IN1_field_3
			FIELD_NEXT_FIELD = jb_check_2IN1_field_4
			FIELD_LEAF = YES
			FIELD_XDRKEY = SERVICE_FEE

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 金币数
		{
			FIELD_NAME = jb_check_2IN1_field_4
			FIELD_NEXT_FIELD = jb_check_2IN1_field_5
			FIELD_LEAF = YES
			FIELD_XDRKEY = AMOUNT

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 备用字段1
		{
			FIELD_NAME = jb_check_2IN1_field_5
			FIELD_NEXT_FIELD = jb_check_2IN1_field_6
			FIELD_LEAF = YES
			FIELD_XDRKEY = RESERVED1

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	// 备用字段2
		{
			FIELD_NAME = jb_check_2IN1_field_6
			FIELD_NEXT_FIELD = jb_check_2IN1_field_7
			FIELD_LEAF = YES
			FIELD_XDRKEY = RESERVED2

			DECODE
			{
				DECODE_FUNC_TYPE = decode_asc
				DECODE_SPLIT = |
			}
			DEAL
			{
				DEAL_FUNC_TYPE = str2str_trimBoth
			}
		}
		FIELD	//RESERVED
		{
			FIELD_NAME = jb_check_2IN1_field_7
			FIELD_NEXT_FIELD = NULL
			FIELD_LEAF = YES
			DECODE
			{
				DECODE_FUNC_TYPE = decode_ignore_onerecord
			}
		}
	}
}