	require "libluabaseD"
   	require "libluadebugD"
	<% use("settle_Xdr_app.MXdr::SXdr") %>
	<% use("settle_RplVarType.MRplVarType::SErrorInfo") %>
    local logtrace = libluadebugD.report_trace
	local logerror = libluadebugD.report_error

	pSubXdr = nil;
	pGsmInfo 	= nil;
	pSubCommon	= nil;
	pReserved	= nil;

	--SXdr.SSubXdr.SGsmInfo
	--SXdr.SSubXdr.SSubCommon.SRatingResList
	local t_sErrMsg = "";
	local RplFileIdx 	= -1;
	local iDoPrintInfo	= 0;	--1:打印调试信息到log,	0:不打印调试信到log
	pFileOpInfo = nil;
	pOriCharge = nil;
	c_oper_mobile	= 2;	--中国移动
	c_oper_rail = 8;
	c_oper_special  = 9999; --	特殊运营商

	c_call_type_nodeal1 = "11"; --关口局转接局内话单
	c_call_type_in = "1";    --入局
	c_call_type_out = "2";   --出局
	c_call_type_in_v = "3";    --入局视频
	c_call_type_out_v = "4";   --出局视频
	c_call_type_nodeal2 = "22";  --关口局转接局外话单

	c_net_type_gsm = 2;     --       网络类型为GSM
	c_net_type_cdma = 3;    --	 网络类型为CDMA
	c_net_type_phs=4;

	c_number_type_blank = 1;
	c_number_type_zero  = 2;
	c_number_type_comm  = 3;
	c_number_type_error = 4;

function isNumber(words)
  if string.len(words) < 1 then
    return false
  end
  for i=1,string.len(words) do
    if string.byte(string.sub(words,i,i)) < 48 or string.byte(string.sub(words,i,i)) > 57 then
      return false
    end
  end
  return true
end

function bitwise_and(a, b)
    local result = 0
    local shift = 1
    while a > 0 or b > 0 do
        local bit_a = a % 2
        local bit_b = b % 2
        if bit_a == 1 and bit_b == 1 then
            result = result + shift
        end
        a = math.floor(a / 2)
        b = math.floor(b / 2)
        shift = shift * 2
    end
    return result
end
-- 国际号码处理
function fmtnbr(strUserNumber,type)
	local retrunValue=strUserNumber;
	local headNumber3=string.sub(strUserNumber,1,3);
	local headNumber2=string.sub(strUserNumber,1,2);
	local headNumber1=string.sub(strUserNumber,1,1);
	if type == 0 then 
		return strUserNumber;
	elseif type ==1 then 
		if headNumber3 =="179" 
			or headNumber3 == "193" 
			or headNumber2 == "12" 
			or headNumber1 =="0" then
		  return strUserNumber;
		 else
			return  "0"..strUserNumber;
		end
	elseif type ==2 then 
		if headNumber3 =="179" 
			or headNumber3 == "193" 
			or headNumber2 == "00" then 
			return strUserNumber;
		end
		if headNumber1 =="0" then 
			return "0"..strUserNumber;
		else
			return "00"..strUserNumber;
		end
			
	end;
	return retrunValue;
end;
	--处理msc
function error_deal(pSubCommon,sErrorCode,sErrorInfo)
	if iDoPrintInfo == 1    then
		logtrace('----------error_deal() begin-------------');
	end
	local pFileOpInfo = <%get_struct_value('pSubCommon',"MXdr::SSubCommon.FILE_OP_INFO") %>
	local iTreatFlag = <%get_struct_value('pFileOpInfo',"MFileOp::SFileOp.TREAT_FLAG") %>
	if iDoPrintInfo == 1    then
		logtrace('--TREAT_FLAG:'.. iTreatFlag );
	end

	-- 2012-09-11 modified by nijj for TRAC#56851-业务分析处理前,若TREAT_FLAG为1,则直接返回
	if iTreatFlag == 1	then
		return;
	else
		<%set_struct_value('pFileOpInfo', "MFileOp::SFileOp.TREAT_FLAG",1)%>
		<%set_struct_value('pFileOpInfo',"MFileOp::SFileOp.ERROR_CODE",sErrorCode) %>
		<%set_struct_value('pSubCommon',"MXdr::SSubCommon.ERROR_MESSAGE",sErrorInfo) %>
	end;

	if iDoPrintInfo == 1    then
		logtrace('----------error_deal() end-------------');
	end
end


function do_main()

	pSubXdr = <%get_struct_value('PXdr',"MXdr::SXdr.TOTAL_XDR")%>
	pSubCommon	= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SUB_COMMON") %>
	pReserved   = <%get_sdl_ref('pSubXdr',"MXdr::SSubXdr.SUB_COMMON.RESERVE_FIELDS") %>
	pGsmInfo 	= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.GSM_INFO45") %>
--getfieldValue
    tenantId = <%get_struct_value('PXdr', "MXdr::SXdr.TENANT_ID")%>;
	t_sDIST_FEE_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_REGION_CODE") %>
	if t_sDIST_FEE_CODE == nil or  t_sDIST_FEE_CODE == "" then
		t_sDIST_FEE_CODE =tostring(tenantId);
	end
	t_sODN_REGION_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_REGION_CODE") %>
	if t_sODN_REGION_CODE == nil or  t_sODN_REGION_CODE == "" then
		t_sODN_REGION_CODE ="0";
	end
	t_sTDN_REGION_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_REGION_CODE") %>
	if t_sTDN_REGION_CODE == nil or  t_sTDN_REGION_CODE == "" then
		t_sTDN_REGION_CODE ="0";
	end
	t_sODN_COUNTRY_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_COUNTRY_CODE") %>
	if t_sODN_COUNTRY_CODE == nil or  t_sODN_COUNTRY_CODE == "" then
		t_sODN_COUNTRY_CODE ="0";
	end
	t_sTDN_COUNTRY_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_COUNTRY_CODE") %>
	if t_sTDN_COUNTRY_CODE == nil or  t_sTDN_COUNTRY_CODE == "" then
		t_sTDN_COUNTRY_CODE ="0";
	end
	t_sODN_COUNTY_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_COUNTY_CODE") %>
	if t_sODN_COUNTY_CODE == nil or  t_sODN_COUNTY_CODE == "" then
		t_sODN_COUNTY_CODE ="0";
	end
	t_sTDN_COUNTY_CODE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_COUNTY_CODE") %>
	if t_sTDN_COUNTY_CODE == nil or  t_sTDN_COUNTY_CODE == "" then
		t_sTDN_COUNTY_CODE ="0";
	end
	t_sCALL_TYPE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.CALL_TYPE") %>
	if t_sCALL_TYPE == nil or  t_sCALL_TYPE == "" then
		t_sCALL_TYPE = "0";
	end
	t_iDURATION=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.DURATION") %>
	if t_iDURATION == nil  or  t_iDURATION == "" then
		t_iDURATION = 0;
	end
	t_sODN=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN") %>
	if t_sODN == nil  or  t_sODN == ""  then
		t_sODN = "";
	end
	t_sTDN=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN") %>
	if t_sTDN == nil or  t_sTDN == ""  then
		t_sTDN = "";
	end
	t_sADN=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ADN") %>
	if t_sADN == nil or  t_sADN == ""  then
		t_sADN = "";
	end
	t_sODN_FIXED=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_FIXED") %>
	if t_sODN_FIXED == nil or  t_sODN_FIXED == ""  then
		t_sODN_FIXED = "";
	end
	t_sTDN_FIXED=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_FIXED") %>
	if t_sTDN_FIXED == nil or  t_sTDN_FIXED == ""  then
		t_sTDN_FIXED = "";
	end
	t_sADN_FIXED=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ADN_FIXED") %>
	if t_sADN_FIXED == nil or  t_sADN_FIXED == ""  then
		t_sADN_FIXED = "";
	end
	t_sSTART_TIME=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.START_TIME") %>
	if t_sSTART_TIME == nil or  t_sSTART_TIME == ""  then
		t_sSTART_TIME = "";
	end
	t_sFINISH_TIME=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.FINISH_TIME") %>
	if t_sFINISH_TIME == nil or  t_sFINISH_TIME == ""  then
		t_sFINISH_TIME = "";
	end

	t_iODN_ACC_TYPE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_TYPE") %>
	if t_iODN_ACC_TYPE == nil or  t_iODN_ACC_TYPE == ""  then
		t_iODN_ACC_TYPE = 0;
	end

	t_sODN_ACC_NO=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_NO") %>
	if t_sODN_ACC_NO == nil or  t_sODN_ACC_NO == ""  then
		t_sODN_ACC_NO = "";
	end
	t_iODN_ACC_OPER=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_OPER") %>
	if t_iODN_ACC_OPER == nil or  t_iODN_ACC_OPER == ""  then
		t_iODN_ACC_OPER = 0;
	end
	t_sODN_HOME_AREA=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_HOME_AREA") %>
	if t_sODN_HOME_AREA == nil or  t_sODN_HOME_AREA == ""  then
		t_sODN_HOME_AREA = "";
	end
	t_sODN_VISIT_AREA=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_VISIT_AREA") %>
	if t_sODN_VISIT_AREA == nil or  t_sODN_VISIT_AREA == ""  then
		t_sODN_VISIT_AREA = "";
	end
	t_iODN_OPER=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_OPER") %>
	if t_iODN_OPER == nil or  t_iODN_OPER == ""  then
		t_iODN_OPER = 0;
	end
	t_iODN_SERV=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_SERV") %>
	if t_iODN_SERV == nil or  t_iODN_SERV == "" then
		t_iODN_SERV = 0;
	end
	t_iODN_NET=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_NET") %>
	if t_iODN_NET == nil or  t_iODN_NET == "" then
		t_iODN_NET = 0;
	end
	t_iODN_ROAM=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ROAM") %>
	if t_iODN_ROAM == nil or  t_iODN_ROAM == "" then
		t_iODN_ROAM = 0;
	end
	t_iODN_LONG=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_LONG") %>
	if t_iODN_LONG == nil or  t_iODN_LONG == "" then
		t_iODN_LONG = 0;
	end
	t_iODN_TRADEMARK=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_TRADEMARK") %>
	if t_iODN_TRADEMARK == nil or  t_iODN_TRADEMARK == "" then
		t_iODN_TRADEMARK = 0;
	end
	t_iTDN_ACC_TYPE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_TYPE") %>
	if t_iTDN_ACC_TYPE == nil or  t_iTDN_ACC_TYPE == ""  then
		t_iTDN_ACC_TYPE = 0;
	end
	t_sTDN_ACC_NO=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_NO") %>
	if t_sTDN_ACC_NO == nil or  t_sTDN_ACC_NO == "" then
		t_sTDN_ACC_NO = 0;
	end
	t_iTDN_ACC_OPER=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_OPER") %>
	if t_iTDN_ACC_OPER == nil or  t_sODN == "" then
		t_iTDN_ACC_OPER = 0;
	end
	t_sTDN_HOME_AREA=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_HOME_AREA") %>
	if t_sTDN_HOME_AREA == nil or  t_sTDN_HOME_AREA == "" then
		t_sTDN_HOME_AREA = "";
	end
	t_sTDN_VISIT_AREA=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_VISIT_AREA") %>
	if t_sTDN_VISIT_AREA == nil or  t_sTDN_VISIT_AREA == "" then
		t_sTDN_VISIT_AREA = "";
	end
	t_iTDN_OPER=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_OPER") %>
	if t_iTDN_OPER == nil or  t_iTDN_OPER == "" then
		t_iTDN_OPER = 0;
	end
	t_iTDN_SERV=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_SERV") %>
	if t_iTDN_SERV == nil or  t_iTDN_SERV == "" then
		t_iTDN_SERV = 0;
	end
	t_iTDN_NET=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_NET") %>
	if t_iTDN_NET == nil or  t_iTDN_NET == "" then
		t_iTDN_NET = 0;
	end
	t_iTDN_ROAM=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ROAM") %>
	if t_iTDN_ROAM == nil or  t_iTDN_ROAM == "" then
		t_iTDN_ROAM = 0;
	end
	t_iTDN_LONG=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_LONG") %>
	if t_iTDN_LONG == nil or  t_iTDN_LONG == "" then
		t_iTDN_LONG = 0;
	end
	t_iTDN_TRADEMARK=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_TRADEMARK") %>
	if t_iTDN_TRADEMARK == nil or  t_iTDN_TRADEMARK == "" then
		t_iTDN_TRADEMARK = 0;
	end
	t_sDEAL_DATE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.DEAL_DATE") %>
	if t_sDEAL_DATE == nil or  t_sDEAL_DATE == "" then
		t_sDEAL_DATE = "";
	end
	t_sINPUT_DATE=<%get_struct_value('pSubCommon', "MXdr::SSubCommon.INPUT_DATE") %>
	if t_sINPUT_DATE == nil or  t_sINPUT_DATE == "" then
		t_sINPUT_DATE = "";
	end
	t_sRAW_TAG= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.RAW_TAG") %>
	if t_sRAW_TAG == nil or  t_sRAW_TAG == "" then
		t_sRAW_TAG = "";
	end
	t_iSEQ= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.SEQ") %>
	if t_iSEQ == nil or  t_iSEQ == "" then
		t_iSEQ = 0;
	end
	t_iTIME_SEG= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TIME_SEG") %>
	if t_iTIME_SEG == nil or  t_iTIME_SEG == "" then
		t_iTIME_SEG = 0;
	end
	t_sMSRN= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSRN") %>
	if t_sMSRN == nil or  t_sMSRN == "" then
		t_sMSRN = "";
	end
	t_sMSC= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSC") %>
	if t_sMSC == nil or  t_sMSC == "" then
		t_sMSC = "";
	end
	t_sLAC= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.LAC") %>
	if t_sLAC == nil or  t_sLAC == "" then
		t_sLAC = "";
	end
	t_sCELL= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.CELL") %>
	if t_sCELL == nil or  t_sCELL == "" then
		t_sCELL = "";
	end
	t_sTRUNK_IN= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN") %>
	if t_sTRUNK_IN == nil or  t_sTRUNK_IN == "" then
		t_sTRUNK_IN = "";
	end
	t_sTRUNK_OUT= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT") %>
	if t_sTRUNK_OUT == nil or  t_sTRUNK_OUT == "" then
		t_sTRUNK_OUT = "";
	end
	--t_iMSC_VENDOR= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSC_VENDOR") %>
	t_iMSC_VENDOR=2;
	t_iTRUNK_IN_OPER= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_OPER") %>
	if t_iTRUNK_IN_OPER == nil or  t_iTRUNK_IN_OPER == "" then
		t_iTRUNK_IN_OPER = 0;
	end
	t_sTRUNK_IN_AREA= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_AREA") %>
	if t_sTRUNK_IN_AREA == nil or  t_sTRUNK_IN_AREA == "" then
		t_sTRUNK_IN_AREA = "";
	end
	t_iTRUNK_IN_SERV= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_SERV") %>
	if t_iTRUNK_IN_SERV == nil or  t_iTRUNK_IN_SERV == "" then
		t_iTRUNK_IN_SERV = 0;
	end
	t_iTRUNK_OUT_OPER= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_OPER") %>
	if t_iTRUNK_OUT_OPER == nil or  t_iTRUNK_OUT_OPER == "" then
		t_iTRUNK_OUT_OPER = 0;
	end
	t_sTRUNK_OUT_AREA= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_AREA") %>
	if t_sTRUNK_OUT_AREA == nil or  t_sTRUNK_OUT_AREA == "" then
		t_sTRUNK_OUT_AREA = "";
	end
	t_iTRUNK_OUT_SERV= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_SERV") %>
	if t_iTRUNK_OUT_SERV == nil or  t_iTRUNK_OUT_SERV == ""  then
		t_iTRUNK_OUT_SERV = 0;
	end
	t_iSIX_SEC= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.SIX_SEC") %>
	if t_iSIX_SEC == nil or  t_iSIX_SEC == "" then
		t_iSIX_SEC = 0;
	end
	t_iMINUTES= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.MINUTES") %>
	if t_iMINUTES == nil or  t_iMINUTES == "" then
		t_iMINUTES = 0;
	end
	t_iIDLE_SIX_SECS= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.IDLE_SIX_SECS") %>
	if t_iIDLE_SIX_SECS == nil or  t_iIDLE_SIX_SECS == "" then
		t_iIDLE_SIX_SECS = 0;
	end
	t_iIDLE_MINUTES= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.IDLE_MINUTES") %>
	if t_iIDLE_MINUTES == nil or  t_iIDLE_MINUTES == "" then
		t_iIDLE_MINUTES = 0;
	end
	t_iFIVE_MIN= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.FIVE_MIN") %>
	if t_iFIVE_MIN == nil or  t_iFIVE_MIN == "" then
		t_iFIVE_MIN = 0;
	end
	t_iAFTER_MINS= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.AFTER_MINS") %>
	if t_iAFTER_MINS == nil or  t_iAFTER_MINS == "" then
		t_iAFTER_MINS = 0;
	end
	t_iLOCAL_TYPE= <%get_struct_value('pGsmInfo', "MXdr::SGsmInfo.LOCAL_TYPE") %>
	if t_iLOCAL_TYPE == nil or  t_iLOCAL_TYPE == "" then
		t_iLOCAL_TYPE = 0;
	end
	t_iSERVICE_ID = <%get_struct_value('PXdr', "MXdr::SXdr.SERVICE_ID")%>
	if t_iSERVICE_ID == nil or  t_iSERVICE_ID == "" then
		t_iSERVICE_ID = 0;
	end
	t_iDR_TYPE = <%get_struct_value('PXdr', "MXdr::SXdr.DR_TYPE")%>
	if t_iDR_TYPE == nil or  t_iDR_TYPE == "" then
		t_iDR_TYPE = 0;
	end

	st_RESERVED1 = getsdlval(pReserved,"RESERVED1");
	if(st_RESERVED1 == nil or st_RESERVED1 == "")
	then
		st_RESERVED1 = "";
	end

    --start analyze
    s_original_file= <%get_struct_value('PXdr', "MXdr::SXdr.ORIGINAL_FILE")%>;
    t_iORI_FILE_TYPE = <%get_struct_value('PXdr', "MXdr::SXdr.ORI_FILE_TYPE")%>
    temp_prov=0;
    
    if t_iORI_FILE_TYPE == 803 then
        --新系统
        number_plan = math.floor(t_iTIME_SEG/16);
        result = bitwise_and(number_plan, 0x7)
        if result==0x01 then
            t_iLOCAL_TYPE = 1;
            --t_sODN=string.sub(t_sODN,3)
			t_sODN=fmtnbr(t_sODN,2);
        end
        t_iTIME_SEG = 0;
        
        if string.sub(t_sODN,1,3) == "861" and string.len(t_sODN) > 12 then
            t_sODN = string.sub(t_sODN,3);
        end
        if string.sub(t_sODN,1,6) == "010156" then
            t_sODN = string.sub(t_sODN,2);
        end
        
        if string.sub(t_sSTART_TIME, 9, 10) < "07" then
            t_iTIME_SEG = 1;
        end
        
        if st_RESERVED1 == nil or st_RESERVED1 == "" then
            st_RESERVED1="0";
        end
        if math.floor(tonumber(st_RESERVED1)/16) == 0x01 then
            t_sErrorNo  = "E4301";
            t_sErrMsg = "billing_flag";
            error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
        end
        st_RESERVED1 = "";
        
        --主被叫号码去除非数字内容
        start_pos = string.find(t_sODN,'%D');
        if start_pos then
            t_sODN = string.sub(t_sODN,1,start_pos-1);
        end
        start_pos = string.find(t_sTDN,'%D');
        if start_pos then
            t_sTDN = string.sub(t_sTDN,1,start_pos-1);
        end
        
    end
    
    if t_iORI_FILE_TYPE == 806 then
        --新系统号码处理偏移
        t_sODN = string.sub(t_sODN,3,string.len(t_sODN));
        t_sTDN = string.sub(t_sTDN,3,string.len(t_sTDN));
        
        t_sMSC = string.sub(t_sMSC,3,string.len(t_sMSC));
        t_sMSRN = string.sub(t_sMSRN,3,string.len(t_sMSRN));
    end
    
    if(string.sub(s_original_file,1,10) == "N_GSM_PPYT") then
        temp_prov = 701
    elseif(string.sub(s_original_file,1,10) == "N_GSM_PPXY") then
        temp_prov = 790
    elseif(string.sub(s_original_file,1,10) == "N_GSM_PPNC") then
        temp_prov = 791
    elseif(string.sub(s_original_file,1,10) == "N_GSM_PPJJ") then
        temp_prov = 792
    elseif(string.sub(s_original_file,1,10) == "N_GSM_PPSR") then
        temp_prov = 793
    elseif(string.sub(s_original_file,1,10) == "N_GSM_PPFZ") then
        temp_prov = 794
    elseif(string.sub(s_original_file,1,10) == "N_GSM_PPYC") then
        temp_prov = 795
    elseif(string.sub(s_original_file,1,10) == "N_GSM_PPJA") then
        temp_prov = 796
    elseif(string.sub(s_original_file,1,10) == "N_GSM_PPGZ") then
        temp_prov = 797
    elseif(string.sub(s_original_file,1,10) == "N_GSM_PPGD") then
        temp_prov = 798
    elseif(string.sub(s_original_file,1,10) == "N_GSM_PPPX") then
        temp_prov = 799
    end

    if(temp_prov ~= 0) then
        odn_flag = string.sub(t_sODN,1,2);
        if(odn_flag == "AA" or odn_flag == "BB" or odn_flag == "CC" or odn_flag == "DD" or odn_flag == "EE") then
            t_sODN = string.sub(t_sODN,3,string.len(t_sODN));
            t_sODN_ACC_NO = odn_flag;
        end
        tdn_flag = string.sub(t_sTDN,1,2);
        if(tdn_flag == "AA" or tdn_flag == "BB" or tdn_flag == "CC" or tdn_flag == "DD" or tdn_flag == "EE") then
            t_sTDN = string.sub(t_sTDN,3,string.len(t_sTDN));
            t_sTDN_ACC_NO = tdn_flag;
        end
    end
    
    if t_sRAW_TAG == "2" or t_sRAW_TAG == "11" or t_sRAW_TAG == "A3" then
        t_sCALL_TYPE = c_call_type_in;
    end
    if t_sRAW_TAG == "10" or t_sRAW_TAG == "12" or t_sRAW_TAG == "A4" then
        t_sCALL_TYPE = c_call_type_out;
    end
    
    if t_iSERVICE_ID == "" and t_iDR_TYPE == "" and t_iORI_FILE_TYPE == "" then
        t_sErrorNo  = "E4302";
        t_sErrMsg = "no valid record";
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    
    temp_six_sec = t_iDURATION/6;
    t_iSIX_SEC = math.floor(temp_six_sec);
    if(t_iDURATION%6 > 0) then
        t_iSIX_SEC = t_iSIX_SEC + 1;
    end
    temp_minutes = t_iDURATION/60;
    t_iMINUTES = math.floor(temp_minutes);
    if(t_iDURATION%60 > 0) then
        t_iMINUTES = t_iMINUTES + 1;
    end
    temp_five_min = t_iDURATION/300;
    t_iFIVE_MIN = math.floor(temp_five_min);
    if(t_iDURATION%300 > 0) then
        t_iFIVE_MIN = t_iFIVE_MIN + 1;
    end
    t_iAFTER_MINS = 0;
    if(t_iMINUTES > 3) then
        t_iAFTER_MINS = t_iMINUTES - 3;
    end

    if(string.len(t_sMSRN) > 15) then
        t_sMSRN = string.sub(t_sMSRN,1,15);
    end

    --交换机分析
    l_date = string.sub(t_sSTART_TIME, 1, 8);
    iRes,m_iLocationType,m_iSwitchSeq,m_szAreaCode,m_szBureauCode,m_szProvCode,m_szSwitchId,m_szSwitchType,m_szVersion = GsmSwitchInfo(t_sMSC,l_date);
    l_area = m_szAreaCode;
    t_sODN_REGION_CODE = m_szAreaCode;

    --入中继分析
    if(t_sTRUNK_IN ~= "")
    then
        m_bGetInfo,m_iInTrunkBusiId,m_iOutTrunkBusiId,m_iSettlerId,m_iTollType,m_szAreaCode,m_szExpireDate,m_szSwitchId,m_szTrunkId,m_szValidDate = GsmRouterInfo(t_sMSC,t_sTRUNK_IN, l_date);
        if(m_bGetInfo == 0)
        then
            if(t_sRAW_TAG == "2" or t_sRAW_TAG == "11" or t_sRAW_TAG == "A3") then
                t_sErrorNo  = "E4303";
                t_sErrMsg = "NO CONFIG TRUNKID"..t_sTRUNK_IN;
                error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
            else
                t_iTRUNK_IN_OPER = c_oper_mobile;
                t_sTRUNK_IN_AREA = l_area;
                t_iTRUNK_IN_SERV = 0;
            end
        else
            t_iTRUNK_IN_OPER = m_iSettlerId;
            t_sTRUNK_IN_AREA = m_szAreaCode;
            t_iTRUNK_IN_SERV = m_iInTrunkBusiId;
        end
    end
    --出中继分析
    if(t_sTRUNK_OUT ~= "")
    then
        m_bGetInfo,m_iInTrunkBusiId,m_iOutTrunkBusiId,m_iSettlerId,m_iTollType,m_szAreaCode,m_szExpireDate,m_szSwitchId,m_szTrunkId,m_szValidDate  = GsmRouterInfo(t_sMSC,t_sTRUNK_OUT, l_date);
        if(m_bGetInfo == 0)
        then
            if(t_sRAW_TAG == "10" or t_sRAW_TAG == "12" or t_sRAW_TAG == "A4") then
                t_sErrorNo  = "E4304";
                t_sErrMsg = "NO CONFIG TRUNKID"..t_sTRUNK_OUT;
                error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
            else
                t_iTRUNK_OUT_OPER = c_oper_mobile;
                t_sTRUNK_OUT_AREA = l_area;
                t_iTRUNK_OUT_SERV = 0;
            end
        else
            t_iTRUNK_OUT_OPER = m_iSettlerId;
            t_sTRUNK_OUT_AREA = m_szAreaCode;
            t_iTRUNK_OUT_SERV = m_iOutTrunkBusiId;
        end
    end
    
    --other oper to 铁通
    if (t_sCALL_TYPE == c_call_type_out and t_iTRUNK_IN_OPER ~= 2 and t_iTRUNK_OUT_OPER == 8) then
        t_iTRUNK_IN_OPER = 2;
    end
    if (t_sCALL_TYPE == c_call_type_in and t_iTRUNK_OUT_OPER ~= 2 and t_iTRUNK_IN_OPER == 8) then
        t_iTRUNK_OUT_OPER = 2;
    end

    --主叫号码分析
    if(t_sODN == "")
    then
        t_sODN = "0";
    end
    temp_odn = t_sODN;
    l_Rn = string.sub(temp_odn,1,4);
    if(l_Rn == "1241" or l_Rn == "1242" or l_Rn == "1243")
    then
        temp_odn = string.sub(temp_odn,5,string.len(temp_odn));
    end
  
	m_iNumberType,m_iAccessFlag,m_iAccessOperator,m_szAccessNumber,m_iNetWork,
	m_iSpecialType,m_iSpecialId,m_szSpecialNumber,m_szHomeCountryCode,m_iCountryType,
	m_szProvId,m_iProvDefaultOp,m_szHomeAreaCode,m_szHomeBureauCode,m_szHomeBusinessCode,
	m_szHomeRegionCode,m_szOriginalNumber,m_iOperatorParty = NumberAnalysis(temp_odn, 1, t_sMSC, t_sSTART_TIME, l_area,45);
	if m_iNumberType ~= c_number_type and string.len(m_szOriginalNumber)== 11 then
		local l_np_info_flag,t_iTODN_NET,t_iTODN_OPER,szPhoneId = FindNpInfo(m_szOriginalNumber,l_date);
		if l_np_info_flag == 1 then
			m_iNumberType = 3;
			m_iOperatorParty = t_iTODN_OPER
			m_szHomeCountryCode = "";
			-- m_szHomeAreaCode= "";--无携转地市
			m_szOriginalNumber = temp_odn;
			m_iNetWork	=	t_iTODN_NET;
			m_iSpecialType = 0;
			m_iAccessFlag = 0;
			m_iAccessOperator = 0;
			m_szAccessNumber = "";
		end;	
	end
    if(m_iNumberType ~= c_number_type_error or t_iLOCAL_TYPE == 1) then
        t_iODN_OPER = m_iOperatorParty;
        l_country_code = m_szHomeCountryCode;
        if(l_country_code == "") then
            l_country_code = "86";
        end
        if(l_country_code == "86") then
            t_sODN_HOME_AREA = m_szHomeAreaCode;
            if(t_sODN_HOME_AREA == "") then
                t_sODN_HOME_AREA = "10";
            end
        else
            t_sODN_HOME_AREA = "00"..l_country_code;
        end
        
        t_sODN_FIXED = m_szOriginalNumber;
        t_sODN_VISIT_AREA = t_sODN_HOME_AREA;
        
        t_iODN_NET = m_iNetWork;
        if(t_iODN_NET == 1 and t_sODN_HOME_AREA == tostring(tenantId)) then
            t_iODN_OPER = t_iTRUNK_IN_OPER;
        end
        
        t_iODN_SERV = m_iSpecialType;
        if(t_iODN_SERV == 0) then
            t_iODN_SERV = 1;
        end
        
        t_iODN_ACC_TYPE = m_iAccessFlag;
        if((t_sRAW_TAG == "2" or t_sRAW_TAG == "11" or t_sRAW_TAG == "A3") and t_iODN_ACC_TYPE ~= 0
            and t_iODN_ACC_TYPE ~= 26 and t_iODN_OPER ~=8) then
            t_sODN_HOME_AREA = "NNN";
        end
        if((t_sRAW_TAG == "10" or t_sRAW_TAG == "12" or t_sRAW_TAG == "A4") 
            and (t_iODN_OPER == 2 or t_iODN_OPER == 3)
            and t_sODN_HOME_AREA == "NNN") then
            t_sODN_HOME_AREA = l_area;
        end
        
        t_iODN_LONG = GetLongType(t_sODN_HOME_AREA ,l_area,l_date);
        
        if(t_iODN_ACC_TYPE == 0) then
            t_iODN_ACC_TYPE = 1;
        end
        
        --处理国际异常来话
        if(t_iLOCAL_TYPE == 1) then
            t_iODN_LONG = 2;
        end
        if(t_iODN_LONG ~= 0) then
            --昌九一体化
            if((t_sODN_HOME_AREA == "791" and t_sODN_REGION_CODE == "792") 
                or (t_sODN_HOME_AREA == "792" and t_sODN_REGION_CODE == "791")) then
                t_iODN_LONG = 0;
            end
            --昌抚一体化
            if((t_sODN_HOME_AREA == "791" and t_sODN_REGION_CODE == "794") 
                or (t_sODN_HOME_AREA == "794" and t_sODN_REGION_CODE == "791")) then
                t_iODN_LONG = 0;
            end
        end
        
        t_iODN_ACC_OPER = m_iAccessOperator;
        t_sODN_ACC_NO = m_szAccessNumber;
        
        if(t_iODN_ACC_OPER == 0) then
            t_iODN_ACC_OPER = t_iODN_OPER;
        end
        
    else
        t_sErrorNo  = "E4305";
        t_sErrMsg = "ERR_ODN_NUMBER";
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    
    temp_odn_bureau_code="";
    if(t_iODN_NET ~= 3 and string.sub(t_sODN_HOME_AREA,1,2) ~= "00") then
        m_bGetInfo,m_iNumberType,m_iOperatorId,m_szAreaCode,m_szBureauCode = SpecialUserAnalyze(t_sODN_FIXED,t_sODN_HOME_AREA,l_date);
        if(m_bGetInfo == 1) then
            t_iODN_NET = m_iNumberType;
            t_iODN_OPER = m_iOperatorId;
            temp_odn_bureau_code = m_szBureauCode;
        end
    end
    
    --被叫号码分析
    temp_tdn_prefix = "";
    if(string.sub(t_sTDN,1,5) == "12583") then
        temp_tdn_prefix = string.sub(t_sTDN,1,6);
        t_sTDN = string.sub(t_sTDN,7,string.len(t_sTDN));
    end
    if(t_sTDN == "") then
        t_sErrorNo  = "E4306";
        t_sErrMsg = "ERR_TDN_NUMBER";
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    
    temp_tdn = t_sTDN;
    l_Rn = string.sub(temp_tdn,1,4);
    if(l_Rn == "1241" or l_Rn == "1242" or l_Rn == "1243")
    then
        temp_tdn = string.sub(temp_tdn,5,string.len(temp_tdn));
    end
    
	m_iNumberType,m_iAccessFlag,m_iAccessOperator,m_szAccessNumber,m_iNetWork,
	m_iSpecialType,m_iSpecialId,m_szSpecialNumber,m_szHomeCountryCode,m_iCountryType,
	m_szProvId,m_iProvDefaultOp,m_szHomeAreaCode,m_szHomeBureauCode,m_szHomeBusinessCode,
	m_szHomeRegionCode,m_szOriginalNumber,m_iOperatorParty = NumberAnalysis(temp_tdn, 2, t_sMSC, t_sSTART_TIME, l_area,45);
	if m_iNumberType ~= c_number_type_error and string.len(m_szOriginalNumber) == 11 then 	
		local l_np_info_flag,t_iTTDN_NET,t_iTTDN_OPER,szPhoneId = FindNpInfo(m_szOriginalNumber,l_date);
		if	l_np_info_flag == 1 then 
			 m_iNumberType = 3;
			 m_iOperatorParty = t_iTTDN_OPER;
			 m_iNetWork = t_iTTDN_NET;
			 --m_szHomeAreaCode =""
			 m_szOriginalNumber=temp_tdn;
			 m_szHomeCountryCode = "";
			 m_iSpecialType = 0;
			 m_iAccessFlag = 0;
			 m_iAccessOperator = 0;
		end;
	end
    
    l_roam_area_code = GetRoamAreaCode(t_sMSRN,t_sSTART_TIME);
    
    if(m_iNumberType ~= c_number_type_error) then
        st_tdn_fixed = m_szOriginalNumber;
        t_iTDN_OPER = m_iOperatorParty;
        
        l_country_code = m_szHomeCountryCode;
        if(l_country_code == "") then
            l_country_code = "86";
        end
        if(l_country_code == "86") then
            t_sTDN_HOME_AREA = m_szHomeAreaCode;
            if(t_sTDN_HOME_AREA == "") then
                t_sTDN_HOME_AREA = "10";
            end
        else
            t_sTDN_HOME_AREA = "00"..l_country_code;
        end
        
        t_sTDN_FIXED = m_szOriginalNumber;
        --t_sTDN_VISIT_AREA = t_sTDN_HOME_AREA;
        
        t_iTDN_NET = m_iNetWork;
        if(t_iTDN_NET == 1 and t_sTDN_HOME_AREA == tostring(tenantId)) then
            t_iTDN_OPER = t_iTRUNK_OUT_OPER;
        end
        
        t_iTDN_SERV = m_iSpecialType;
        if(t_iTDN_SERV == 0) then
            t_iTDN_SERV = 1;
        end
        
        if(t_sMSRN ~= "") then
            t_sTDN_VISIT_AREA = l_roam_area_code;
        else
            if(m_szHomeCountryCode ~= "86" and t_sCALL_TYPE == c_call_type_in and string.sub(t_sTDN,1,5) ~= "17951") then
                t_sTDN_VISIT_AREA = l_area;
            end
        end
        
        if(t_sTDN_HOME_AREA == "NNN" and (t_iTDN_OPER == 2 or t_iTDN_OPER == 3)) then
            t_sTDN_HOME_AREA = l_area;
        end
        if(t_sTDN_VISIT_AREA == "") then
            t_sTDN_VISIT_AREA = t_sTDN_HOME_AREA;
        end
        
        t_iTDN_LONG = GetLongType(t_sTDN_HOME_AREA ,l_area,l_date);
        
        t_iTDN_ACC_TYPE = m_iAccessFlag;
        if(t_iTDN_ACC_TYPE == 0) then
            t_iTDN_ACC_TYPE = 1;
        end
        
        t_iTDN_ACC_OPER = m_iAccessOperator;
        t_sTDN_ACC_NO = m_szAccessNumber;
        
        if(t_iTDN_ACC_OPER == 0) then
            t_iTDN_ACC_OPER = t_iTDN_OPER;
        end
        
    else
        t_sErrorNo  = "E4307";
        t_sErrMsg = "ERR_TDN_NUMBER";
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    
    temp_tdn_bureau_code = "";
    if(t_iTDN_NET ~= 3 and string.sub(t_sTDN_HOME_AREA,1,2) ~= "00") then
        m_bGetInfo,m_iNumberType,m_iOperatorId,m_szAreaCode,m_szBureauCode = SpecialUserAnalyze(t_sTDN_FIXED,t_sTDN_HOME_AREA,l_date);
        if(m_bGetInfo == 1) then
            t_iTDN_NET = m_iNumberType;
            t_iTDN_OPER = m_iOperatorId;
            temp_tdn_bureau_code = m_szBureauCode;
        end
    end

    isFind,productType,countyCode,areaCode = BpsServProductAnalyze(t_sODN_FIXED);
    if isFind == 1 then
        t_iODN_TRADEMARK = productType;
        t_sODN_COUNTY_CODE = countyCode;
	--	t_sODN_REGION_CODE = areaCode;	
    end
    isFind,productType,countyCode,areaCode = BpsServProductAnalyze(t_sTDN_FIXED);
    if isFind == 1 then
        t_iTDN_TRADEMARK = areaCode;
        t_sTDN_COUNTY_CODE = countyCode;
		t_sTDN_REGION_CODE = areaCode;	
    end
    
    t_sODN_COUNTY_CODE = t_sTDN_COUNTY_CODE;
    
    --SELF_TRADEMARK
    if(t_sCALL_TYPE == c_call_type_in) then
        st_RESERVED1 = tostring(t_iTDN_TRADEMARK);
    else
        st_RESERVED1 = tostring(t_iODN_TRADEMARK);
    end
    
    if(t_iTRUNK_IN_OPER == c_oper_mobile and t_sCALL_TYPE == c_call_type_in and t_iTRUNK_OUT_OPER ~= 8) then
        t_sErrorNo  = "E4308";
        t_sErrMsg = "ERR INCALL";
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    if(t_iTRUNK_OUT_OPER == c_oper_mobile and t_sCALL_TYPE == c_call_type_out and t_iTRUNK_IN_OPER ~= 8) then
        t_sErrorNo  = "E4309";
        t_sErrMsg = "ERR INCALL";
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    if(t_sCALL_TYPE == c_call_type_in) then
        t_settle_side = t_iTRUNK_IN_OPER;
    else
        if(t_sCALL_TYPE == c_call_type_out) then
            t_settle_side = t_iTRUNK_OUT_OPER;
        end
        if(t_sCALL_TYPE == c_call_type_nodeal2) then
            t_settle_side1 = t_iTRUNK_OUT_OPER;--SETTLE_SIDE2
            t_settle_side = t_iTRUNK_IN_OPER;
        end
    end
    
    if string.sub(t_sODN,1,4) == "0950" then
        t_sODN_FIXED = string.sub(t_sODN,2,string.len(t_sODN));
        t_iLOCAL_TYPE = 1;
    end
    if string.sub(t_sTDN,1,4) == "0950" then
        t_sTDN_FIXED = string.sub(t_sTDN,2,string.len(t_sTDN));
        t_iLOCAL_TYPE = 1;
    end
    
    if((t_sTDN_HOME_AREA == "NNN" and t_sCALL_TYPE == c_call_type_in)
        or (t_sCALL_TYPE == c_call_type_out and (t_sTDN_HOME_AREA == "NNN" or (t_iLOCAL_TYPE ~= 1 and t_sODN_HOME_AREA == "NNN")))) then
        t_sErrorNo  = "E4310";
        t_sErrMsg = "ERR_TDN_NUMBER";
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    t_iTDN_ROAM = GetRoamType(l_roam_area_code,t_sCALL_TYPE,m_szHomeCountryCode,m_szAccessNumber,m_szHomeAreaCode,m_szProvId);

    t_iIDLE_SIX_SECS = GetIdleDuration(t_sSTART_TIME,t_iDURATION,6);
    t_iIDLE_MINUTES= GetIdleDuration(t_sSTART_TIME,t_iDURATION,60);
    l_six_secs = t_iSIX_SEC - t_iIDLE_SIX_SECS;
    t_iSIX_SEC = l_six_secs;
    
    if(t_sCALL_TYPE == c_call_type_nodeal1 and t_iODN_OPER == c_oper_mobile and t_iTDN_OPER == c_oper_mobile) then
        t_sErrorNo  = "E4311";
        t_sErrMsg = "NODEALCALALTYPE";
        error_deal(pSubCommon,t_sErrorNo,t_sErrMsg);
    end
    
    --一卡多号
    t_sTDN = temp_tdn_prefix..t_sTDN;
    if t_iTDN_NET == 1 and t_iODN_NET == 1 and t_iODN_LONG == 0 and t_iTDN_LONG == 0 then
        t_iLOCAL_TYPE = 10+GetSysBureauIsBureauCall(temp_odn_bureau_code,temp_tdn_bureau_code);
    end
    
    if t_sODN_REGION_CODE == nil or t_sODN_REGION_CODE == "" then
        t_sODN_REGION_CODE = "0";
    end
    temp_region_code = tonumber(t_sODN_REGION_CODE);
    if temp_region_code==701 or temp_region_code==790 or temp_region_code==791 or temp_region_code==792 or temp_region_code==793
        or temp_region_code==794 or temp_region_code==795 or temp_region_code==796 or temp_region_code==797 or temp_region_code==798
        or temp_region_code==799 then
        if t_iTRUNK_IN_OPER == 3 then
            t_iTRUNK_IN_OPER = 9;
        end
        if t_iTRUNK_OUT_OPER == 3 then
            t_iTRUNK_OUT_OPER = 9;
        end
        if t_iTRUNK_IN_OPER == 8 then
            t_iTRUNK_IN_OPER = 2;
        end
        if t_iTRUNK_OUT_OPER == 8 then
            t_iTRUNK_OUT_OPER = 2;
        end
        if t_sCALL_TYPE == "1" and (t_iTRUNK_IN_OPER == 9 and t_iODN_NET ~= 1) then
            if t_iODN_OPER == 1 then
                t_iODN_NET = 2;
            end
            if t_iODN_NET == 2 and t_iODN_LONG == 2 then
            
            elseif t_iODN_NET == 0 and (t_iODN_LONG == 1 or t_iODN_LONG == 10) and t_iTDN_LONG == 0 then
            
            else
                t_iTRUNK_IN_OPER = 3;
                t_settle_side = t_iTRUNK_IN_OPER;
            end
        end
        if t_sCALL_TYPE == "1" and (t_iTRUNK_IN_OPER==9 and t_iTRUNK_OUT_OPER==8 and t_iODN_NET==1) then
            t_sCALL_TYPE = 21;
            t_settle_side = t_iTRUNK_IN_OPER;
        end
        if t_sCALL_TYPE == "2" and (t_iTRUNK_OUT_OPER==9 and t_iTDN_NET~=1) then
            if t_iTDN_NET ~= 0 then
                t_iTRUNK_OUT_OPER = 3;
                t_settle_side = t_iTRUNK_OUT_OPER;
            end
        end
        if t_sCALL_TYPE == "2" and (t_iTRUNK_OUT_OPER==9 and t_iTRUNK_IN_OPER==8 and t_iODN_NET==1) then
            t_sCALL_TYPE = 22;
            t_settle_side = t_iTRUNK_OUT_OPER;
        end
        if t_sCALL_TYPE == "2" and (t_iTRUNK_IN_OPER==2 and (t_iODN_NET==1 or (t_iODN_NET==0 and t_iODN_OPER==8))) then
            t_sCALL_TYPE = 22;
            t_iTRUNK_IN_OPER = 8;
        end
        if t_sCALL_TYPE == "1" and (t_iTRUNK_OUT_OPER==2 and (t_iTDN_NET==1 or (t_iTDN_NET==0 and t_iTDN_OPER==8))) then
            t_sCALL_TYPE = 21;
            t_iTRUNK_OUT_OPER = 8;
        end
        
        --通过网络类型判断如果为移动固话，则用铁通与其他运营商的批价规则
        if t_sCALL_TYPE == "1" and ((t_iTRUNK_OUT_OPER==2 or t_iTRUNK_OUT_OPER==7 or t_iTRUNK_OUT_OPER==8) and (t_iTDN_NET~=2 and t_iTDN_NET~=6) ) then
            if (t_iTDN_ACC_TYPE~=1 or t_iTDN_SERV~=1) and t_iTDN_NET==0 and t_iTDN_OPER~=8 then
            
            else
                t_sCALL_TYPE = "21";
            end
        elseif t_sCALL_TYPE == "2" and (t_iTRUNK_IN_OPER==2 or t_iTRUNK_IN_OPER==7 or t_iTRUNK_IN_OPER==8) and (t_iODN_NET~=2 and t_iODN_NET~=6) then
            if (t_iODN_ACC_TYPE~=1 or t_iODN_SERV~=1) and t_iODN_NET==0 and t_iODN_OPER~=8 then
            
            else
                t_sCALL_TYPE = "22";
            end
        end
        
        --移动GSM呼叫联通移网业务台（移网中继）也批到固网业务台
        if t_sCALL_TYPE == "2" and t_iTRUNK_OUT_OPER==3 and (t_iTDN_NET==0 and t_iTDN_SERV~=1) then
            t_iTRUNK_OUT_OPER = 9;
            t_settle_side = t_iTRUNK_OUT_OPER;
        end
        
        --融合地市本地联通业务台外呼中国移动GSM
        if t_sCALL_TYPE == "1" and (t_iTRUNK_IN_OPER==3 or t_iTRUNK_IN_OPER==9) and t_iTRUNK_OUT_OPER==2 and t_iODN_SERV~=1
            and (t_iTDN_NET==2 or t_iTDN_NET==6) and t_iODN_LONG==0 then
            if t_iTRUNK_IN_OPER==3 then
                t_iTRUNK_IN_OPER=9;
                t_settle_side = t_iTRUNK_IN_OPER;
            end
            t_sCALL_TYPE = "21";
        end
        
        --融合地市移动业务台外呼中国联通固网
        if t_sCALL_TYPE == "2" and t_iTRUNK_IN_OPER==2 and t_iTRUNK_OUT_OPER==9 and t_iODN_SERV~=1
            and t_iTDN_NET==1 and t_iODN_LONG==0 then
            t_sCALL_TYPE = "22";
        end
        --移动TD呼叫联通业务台或特服资费6分每分钟
        if t_sCALL_TYPE == "2" and t_iTRUNK_IN_OPER==2 and t_iTRUNK_OUT_OPER==9 and t_iODN_NET==6
            and t_iTDN_SERV~=1 and t_iODN_LONG==0 then
            t_sCALL_TYPE = "22";
        end
        
    end
    
--set ananlysResultfieldValue
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.DIST_FEE_CODE",t_sDIST_FEE_CODE) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_REGION_CODE",t_sODN_REGION_CODE) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_REGION_CODE",t_sTDN_REGION_CODE) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_COUNTRY_CODE",t_sODN_COUNTRY_CODE) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_COUNTRY_CODE",t_sTDN_COUNTRY_CODE) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_COUNTY_CODE",t_sODN_COUNTY_CODE) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_COUNTY_CODE",t_sTDN_COUNTY_CODE) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.CALL_TYPE",t_sCALL_TYPE) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.DURATION",t_iDURATION) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN",t_sODN) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN",t_sTDN) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ADN",t_sADN) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_FIXED",t_sODN_FIXED) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_FIXED",t_sTDN_FIXED) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ADN_FIXED",t_sADN_FIXED) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.START_TIME",t_sSTART_TIME) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.FINISH_TIME",t_sFINISH_TIME) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_TYPE",t_iODN_ACC_TYPE) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_NO",t_sODN_ACC_NO) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ACC_OPER",t_iODN_ACC_OPER) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_HOME_AREA",t_sODN_HOME_AREA) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_VISIT_AREA",t_sODN_VISIT_AREA) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_OPER",t_iODN_OPER) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_SERV",t_iODN_SERV) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_NET",t_iODN_NET) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_ROAM",t_iODN_ROAM) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_LONG",t_iODN_LONG) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.ODN_TRADEMARK",t_iODN_TRADEMARK) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_TYPE",t_iTDN_ACC_TYPE) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_NO",t_sTDN_ACC_NO) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ACC_OPER",t_iTDN_ACC_OPER) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_HOME_AREA",t_sTDN_HOME_AREA) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_VISIT_AREA",t_sTDN_VISIT_AREA) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_OPER",t_iTDN_OPER) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_SERV",t_iTDN_SERV) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_NET",t_iTDN_NET) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_ROAM",t_iTDN_ROAM) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_LONG",t_iTDN_LONG) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.TDN_TRADEMARK",t_iTDN_TRADEMARK) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.DEAL_DATE",t_sDEAL_DATE) %>
	<%set_struct_value('pSubCommon', "MXdr::SSubCommon.INPUT_DATE",t_sINPUT_DATE) %>


	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.RAW_TAG",t_sRAW_TAG) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.SEQ",t_iSEQ) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TIME_SEG",t_iTIME_SEG) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSRN",t_sMSRN) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSC",t_sMSC) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.LAC",t_sLAC) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.CELL",t_sCELL) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN",t_sTRUNK_IN) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT",t_sTRUNK_OUT) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.MSC_VENDOR",t_iMSC_VENDOR) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_OPER",t_iTRUNK_IN_OPER) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_AREA",t_sTRUNK_IN_AREA) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_IN_SERV",t_iTRUNK_IN_SERV) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_OPER",t_iTRUNK_OUT_OPER) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_AREA",t_sTRUNK_OUT_AREA) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.TRUNK_OUT_SERV",t_iTRUNK_OUT_SERV) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.SIX_SEC",t_iSIX_SEC) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.MINUTES",t_iMINUTES) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.IDLE_SIX_SECS",t_iIDLE_SIX_SECS) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.IDLE_MINUTES",t_iIDLE_MINUTES) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.FIVE_MIN",t_iFIVE_MIN) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.AFTER_MINS",t_iAFTER_MINS) %>
	<%set_struct_value('pGsmInfo', "MXdr::SGsmInfo.LOCAL_TYPE",t_iLOCAL_TYPE) %>
	<%set_struct_value('PXdr', "MXdr::SXdr.SETTLE_SIDE",t_settle_side)%>
	<%set_struct_value('PXdr', "MXdr::SXdr.SERVICE_ID",t_iSERVICE_ID)%>
	<%set_struct_value('PXdr', "MXdr::SXdr.DR_TYPE",t_iDR_TYPE)%>


    if st_RESERVED1 ~=	nil then
		setsdlval(pReserved,"RESERVED1",st_RESERVED1)
	end;
	if st_RESERVED2 ~=	nil then
		setsdlval(pReserved,"RESERVED2",st_RESERVED2)
	end;
    if st_RESERVED3 ~=	nil then 
		setsdlval(pReserved,"RESERVED3",st_RESERVED3)
	end;
	if t_settle_side  ~= 8 then
		if t_settle_side1 ~=nil or t_settle_side1~="" or t_settle_side1 ~="0" or t_settle_side1~=0
		then
			local pSubSettleSideList 	= <%get_sdl_ref('PXdr',"MXdr::SXdr.SUB_SETTLE_SIDE")%>;
			setsdllen(pSubSettleSideList,1);
			local pSettleSide  = getsdl(pSubSettleSideList, 1);
			<%set_struct_value('pSettleSide', "MXdr::SSubSettleSide.settle_side", t_settle_side1)%>;
		end
	end
	return 0;
end
