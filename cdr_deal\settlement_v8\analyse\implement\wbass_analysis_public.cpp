﻿#include "wbass_analysis_public.h"

CODACClient m_ODACClient;
/**
 * 定义全局odac接口调用类对象
 */

int NumberAnalysis(xc::CSnapshot & snapShot, 
            const char* p_pchNumber,
            int         nCallingType ,//1  主叫， 2 被叫
            const char* p_pMscId ,  //交换机ID,NOKIA交换机被叫号即使是长途也没有0
            char*       p_pchCenterArea,
            NumberInfo* p_pSNumber,
            int32 m_nServiceId)
{
    char l_szNumber[64];
    char l_szHlr[8 + 1];
    int l_excursion;
    int l_excursion_temp=0;
    int l_mhead_len;
    int l_len;
    int i,j;

    int l_ccode_flag; //country_code
    int l_acode_flag; //area_code
    int l_ld_flag;       //
    int l_ldk_flag;     //
    int l_ss_flag;      //special_service
    int l_acode_mob_flag;
    int l_prov_flag;  //province_code
    int l_unkown_calling_flag = 0; //主叫号码不可识别
    int bMustBeLong = 0;
    int m_nDefaultOperId = 0;
    char m_szProvCode[64];
    
    char * p_pchTemp;
    memset(p_pSNumber, 0, sizeof(NumberInfo));
    
    CBpsGsmMsc        cGsmMscData;
    CSysCity          cCityData;
    CSysProv          cProvData;
    CBpsHlr           cHlrData;
    CSysCountry       cCountryData;
    CBpsPstnNumseg    cPstnNumsegData;
    CBpsAddMobileSeg  cMobileSegData;
    CBpsAccessNumber  cAccessNumberData;
    CBpsAddMmmCode    cMmmCodeData;
    CBpsSpecialNumber cSpecialNumberData;

    // qinhaiping add 20051105 获取交换机属性
    if( (p_pMscId != NULL && strlen(p_pMscId) != 0)
        && (0 != m_ODACClient.findGsmMsc(snapShot,p_pMscId, cGsmMscData, 0)))
    {
        return ANA_SUCCESS;
    }
    //add end    

    l_len = strlen(p_pchNumber);
    bzero(l_szNumber, sizeof(l_szNumber));
    if (l_len > sizeof(l_szNumber))
        l_len = sizeof(l_szNumber) - 1;

    if ((p_pchNumber == NULL) || (l_len == 0))
    {
        //qinhaiping modify it 老系统处理时，如果是空号，置为'0'
        memcpy(l_szNumber ,"0",1);
        l_len = 1;
        //return ANA_ERROR;
    }
    else
    {
        j=0;
        for(i=0;i<l_len;i++)
        {
            if(((*(p_pchNumber+i) <='9')&&(*(p_pchNumber+i) >='0')))
            {
                *(l_szNumber+j)=*(p_pchNumber+i);
                j++;
            }
        }
    }

    l_len = strlen(l_szNumber);

    /*  判断号码是否为全空号    */
    for (i = 0; i < l_len; i++)
    {
        if (l_szNumber[i] != ' ')
        {
            break;
        }
    }

    if (i == l_len)
    {
        printf("输入的号码为全空号!\n");
        p_pSNumber->m_iNumberType = ANA_NUMBER_ERROR;
        return ANA_SUCCESS;
    }

    /*  下面是一个正常号码的分析流程    */
    l_excursion = 0;
    l_ccode_flag = 0;
    l_acode_flag = 0;
    l_ld_flag = 0;
    l_ss_flag = 0;
    l_ldk_flag = 0;
    l_acode_mob_flag = 0;
    l_prov_flag = 0;

    p_pSNumber->m_iNumberType = ANA_NUMBER_COMM;

START:
    /*  判断号码是否为全零号    */
    l_len = strlen(l_szNumber+l_excursion);
    for (i = 0; i < l_len; i++)
    {
        if (*(l_szNumber+l_excursion+i) != '0')
        {
            break;
        }
    }
    if (i == l_len)
    {
//      printf("输入的号码为全零号!\n");
#ifdef __ZERO_BE_LONG__
                        goto ACODE_CON;
#else
                        goto DEF_CON;
#endif
//      return ANA_SUCCESS;
    }

    //参考老系统的号码规整先做一部分处理
    if(nCallingType== 1)  //主叫
    {
        if(strncmp(l_szNumber+l_excursion,"0017951",7)==0)
        {
            l_excursion += 2;
        }       
        if(strncmp(l_szNumber+l_excursion,"0021179",7)==0)
        {
            l_excursion += 1;
        }     
        if(strncmp(l_szNumber+l_excursion,"0000",4)==0)
            l_excursion += 2;
        if(strncmp(l_szNumber+l_excursion,"0086",4)==0)
        {
            l_ccode_flag = 1;
            l_excursion += 4;
            strcpy(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA);
        }                                   

        if(strncmp(l_szNumber+l_excursion,"017",3)==0)
            l_excursion +=1;

        if(strncmp(l_szNumber+l_excursion,"096",3)==0)
            l_excursion +=1;

        if(strncmp(l_szNumber+l_excursion,"011",3)==0)
            l_excursion +=1;

        if(strncmp(l_szNumber+l_excursion,"019",3)==0)
            l_excursion +=1;                  

        if(strncmp(l_szNumber+l_excursion,"44179",5)==0)
            l_excursion +=2;
    }
    if((strncmp(l_szNumber+l_excursion,"861",3)==0&&strlen(l_szNumber+l_excursion)>12)||
       (strncmp(l_szNumber+l_excursion,"8617951",7)==0&&strlen(l_szNumber+l_excursion)>8))
    {
        l_excursion +=2;
        l_ccode_flag = 1;
        strcpy(p_pSNumber->m_szHomeCountryCode, ANA_CCODE_CHINA);
    }                                       

    //  判断是否为特服号码
SS_CON:
    if (l_acode_flag)
    {
        p_pchTemp =  p_pSNumber->m_szHomeAreaCode;  
    }
    else
    {
        p_pchTemp =  p_pchCenterArea;
    }
    if ( 0 == m_ODACClient.findSpecialNumber(snapShot,m_nServiceId, 
                                              p_pchCenterArea, 
                                              p_pchTemp, 
                                              l_szNumber + l_excursion, 
                                              cSpecialNumberData, 
                                              0) )
    {
        l_ss_flag = 1;
        p_pSNumber->m_iOperatorParty = cSpecialNumberData.get_operatorId();

        p_pSNumber->m_iSpecialType = cSpecialNumberData.get_numberType();
        
        p_pSNumber->m_iSpecialId = cSpecialNumberData.get_numberId()*100 
                                    + cSpecialNumberData.get_conditionGroupId();
 
        strcpy(p_pSNumber->m_szSpecialNumber,
                cSpecialNumberData.get_specialNumber());
//      strcpy(p_pSNumber->m_szOriginalNumber,
//              cSpecialNumberData.getspecialNumber());
//   qinhaiping modify 20051031
        strcpy(p_pSNumber->m_szOriginalNumber,
                l_szNumber + l_excursion);

        if (l_ccode_flag == 0)
        {
            strcpy(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA);
        }

        if (l_acode_flag == 0)
        {
            strcpy(p_pSNumber->m_szHomeAreaCode,p_pchCenterArea);
        }

        if (l_prov_flag == 0)
        {
            //  由地区长途区号得到省代码
            if ( 0 == m_ODACClient.findCityByAreaCode(snapShot,p_pSNumber->m_szHomeAreaCode,
                                                        cCityData,0) )
            {
                strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
                strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                if ( 0 == m_ODACClient.findProv(snapShot,atoi(cCityData.get_provCode()),cProvData,0) )
                {
                    p_pSNumber->m_iProvDefaultOp =
                            cProvData.get_defaultOperatorId();
                }
                l_prov_flag = 1;
            }
        }

        return ANA_SUCCESS;
    }
    
    if (l_ss_flag == -1)
    {
        goto AREA_SS_CON;
    }

    //  判断是否为长途接入
LD_CON:
    if (0 == m_ODACClient.findAccessNumber(snapShot,l_szNumber + l_excursion, 
                                             cAccessNumberData, 0))
    {
        l_excursion += strlen(cAccessNumberData.get_accessNumber());

        p_pSNumber->m_iAccessFlag = cAccessNumberData.get_numberType();

        p_pSNumber->m_iAccessOperator =
                cAccessNumberData.get_operatorId();

        strcpy(p_pSNumber->m_szAccessNumber,
                cAccessNumberData.get_accessNumber());

        if (cAccessNumberData.get_analysisType() == ANA_ACCESS_TYPE_NO)
        {
            //  不用分析后面的号码
            if (l_ccode_flag == 0)
            {
                strcpy(p_pSNumber->m_szHomeCountryCode, ANA_CCODE_CHINA);
            }

            if (l_acode_flag == 0)
            {
                strcpy(p_pSNumber->m_szHomeAreaCode, p_pchCenterArea);
            }
            strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion);

            if (l_prov_flag == 0)
            {
                //  由地区长途区号得到省代码
                if (0 == m_ODACClient.findCityByAreaCode(snapShot,p_pSNumber->m_szHomeAreaCode, 
                                                           cCityData,0))
                {
                    strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
                    strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                    if (0 == m_ODACClient.findProv(snapShot,atoi(cCityData.get_provCode()), 
                                                  cProvData,0))
                    {
                        p_pSNumber->m_iProvDefaultOp =
                                cProvData.get_defaultOperatorId();
                    }
                    l_prov_flag = 1;
                }
            }

            if (p_pSNumber->m_iOperatorParty == 0)
            {
                p_pSNumber->m_iOperatorParty = p_pSNumber->m_iAccessOperator;
            }

            return ANA_SUCCESS;
        }
        else
        {
            //  IP接入
            //对接入号码继续规整
            //如果号码全为0  走缺省
            
            l_ld_flag = 1;
            goto START;
        }
    }

    if (l_ldk_flag == -1)
    {
        goto AREA_LDK_CON;
    }

    //  是否国家代码开头
    if (memcmp(l_szNumber + l_excursion, ANA_CCODE_HEAD,
                ANA_CCODE_HEAD_LEN) == 0)
    {
        l_excursion += ANA_CCODE_HEAD_LEN;

        if (memcmp(l_szNumber + l_excursion, ANA_CCODE_CHINA,
                ANA_CCODE_CHINA_LEN) == 0)
        {
            strcpy(p_pSNumber->m_szHomeCountryCode,
                    ANA_CCODE_CHINA);
            l_excursion += ANA_CCODE_CHINA_LEN;
            l_ccode_flag = 1;
        }
        else
        {
            //qinhaiping modify it
            //国际来话且无主叫号码,有可能由多个0，规范中为2个或3个0，但是实际上发现有4个0的，因为无法确认到底有多少个0，所以循环
            while((memcmp(l_szNumber + l_excursion,"0",1)==0))
            {
                l_excursion ++;
            }

            //CIC码放在接入号中了,标志为TYPE_CIC_CODE
            if( 0 == m_ODACClient.findAccessNumber(snapShot,l_szNumber + l_excursion, cAccessNumberData, 0))
            {
                if(cAccessNumberData.get_cicFlag()&&p_pSNumber->m_iAccessFlag==0)
                {
                    l_excursion += strlen(cAccessNumberData.get_accessNumber()); 
                    p_pSNumber->m_iAccessOperator = cAccessNumberData.get_operatorId();
                    bzero(p_pSNumber->m_szOriginalNumber,sizeof(p_pSNumber->m_szOriginalNumber));
                    if (p_pSNumber->m_iOperatorParty == 0)
                    {
                        p_pSNumber->m_iOperatorParty = ANA_OPER_INTER;
                    }   
                    strcpy(p_pSNumber->m_szAccessNumber,
                           cAccessNumberData.get_accessNumber());                               
                    p_pSNumber->m_iAccessFlag = cAccessNumberData.get_numberType();
                    l_unkown_calling_flag = 1;  //
                }
            }
            if ( 0 != m_ODACClient.findCountry(snapShot,l_szNumber + l_excursion,
                                                 cCountryData,0) )
            {
                //p_pSNumber->m_iNumberType = ANA_NUMBER_ERROR;

                //20051027  如果找不到，则按国内长途处理,不再返回错误，这样可能导致国家代码不全，但是错误无法发现，
                //由于实际号码经常有00加地区号的，所以权宜之计取此方式
                if(l_unkown_calling_flag != 1 && l_ld_flag !=1)
                {
                    strcpy(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA);
                    l_ccode_flag = 1;
                    goto   ACODE_CON;      
                }                               
                else if (l_unkown_calling_flag == 1)
                {
                    //0019开头的美国号码
                    p_pSNumber->m_iOperatorParty = 0; //清除判断为CIC代码时赋的值
                    p_pSNumber->m_iAccessFlag =0;
                    p_pSNumber->m_iAccessOperator = 0;
                    l_excursion -= strlen(p_pSNumber->m_szAccessNumber);
                    memset(p_pSNumber->m_szAccessNumber,0,strlen(p_pSNumber->m_szAccessNumber));

                    if( 0 != m_ODACClient.findCountry(snapShot,l_szNumber + l_excursion, cCountryData,0) )
                    {
                        p_pSNumber->m_iNumberType = ANA_NUMBER_ERROR; //国家代码表没有美国
                        return ANA_SUCCESS;
                    }    
                    l_unkown_calling_flag = 0;
                }   
                //20051027 add end;
            }
            strcpy(p_pSNumber->m_szHomeCountryCode,
                        cCountryData.get_countryCode());

            p_pSNumber->m_iCountryType = atoi(cCountryData.get_countryCode());

            //qinhaiping add
            if(l_unkown_calling_flag == 1 && strncmp(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA,2)==0)
            {
                //国际来话，但是国家代码是中国
                l_excursion += strlen(l_szNumber + l_excursion);
                goto DEF_CON;
            }
            else if(strncmp(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA,2)==0)
            {
                l_ccode_flag = 1;
                goto    ACODE_CON;
            }

            //qinhaiping add end  
            l_excursion += strlen(p_pSNumber->m_szHomeCountryCode);
            l_ccode_flag = 1;

            if(p_pSNumber->m_iOperatorParty == 0)
            {
                p_pSNumber->m_iOperatorParty = m_nDefaultOperId;
            }
            if(l_unkown_calling_flag != 1)
                strcpy(p_pSNumber->m_szOriginalNumber,l_szNumber + l_excursion );

            return ANA_SUCCESS;
        }
    }

    //  判断是否为手机号码
MOBILE:
    /*if (((l_ccode_flag == 0) &&
        (((memcmp(l_szNumber + l_excursion, ANA_MOBILE_HEAD,
                ANA_MOBILE_HEAD_LEN) == 0)&&(strlen(l_szNumber + l_excursion) >= 7)) ||
        ((memcmp(l_szNumber + l_excursion, ANA_MOBILE_LHEAD,
                ANA_MOBILE_LHEAD_LEN) == 0)&&(strlen(l_szNumber + l_excursion) >= 8)))) ||
        ((l_ccode_flag == 1) &&
        (memcmp(l_szNumber + l_excursion, ANA_MOBILE_HEAD,
                ANA_MOBILE_HEAD_LEN) == 0)))*/

    if( 0 == m_ODACClient.findMobileSeg(snapShot,l_szNumber+l_excursion, 
                                          cMobileSegData,0) )
    {
        bzero(l_szHlr, sizeof(l_szHlr));

        if(memcmp(cMobileSegData.get_mCodeSeg(),"0",1)==0)
            l_mhead_len = 3;
        else
            l_mhead_len = 2;

        memcpy(l_szHlr, l_szNumber + l_excursion + l_mhead_len -2 , 8);

        if (0 == m_ODACClient.findHlr(snapShot,l_szHlr, cHlrData, 0))
        {
            //  长途区号加手机号码的拨号方式
            if (l_acode_flag == 1)
            {
                if (strcmp(p_pSNumber->m_szHomeAreaCode,
                           cHlrData.get_areaCode()) != 0)
                {
    //                  l_excursion -= l_mhead_len;
#ifdef __HLR_TO_AREACODE__  
                        strcpy(p_pSNumber->m_szHomeAreaCode,
                                cHlrData.get_areaCode());
                                
#else
                        goto AREA_MOBILE_CON;
#endif      
                }
            }
            else
            {
                strcpy(p_pSNumber->m_szHomeAreaCode,
                       cHlrData.get_areaCode());
                l_acode_flag = 1;
            }
            p_pSNumber->m_iOperatorParty = cHlrData.get_operatorId();

            p_pSNumber->m_iNetWork = cHlrData.get_netType();

            if (l_ccode_flag == 0)
            {
                strcpy(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA);
            }

            if (l_prov_flag == 0)
            {
                //  由地区长途区号得到省代码
                if ( 0 == m_ODACClient.findCityByAreaCode(snapShot,p_pSNumber->m_szHomeAreaCode, cCityData,0) )
                {
                    strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
                    strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                    if ( 0 == m_ODACClient.findProv(snapShot,atoi(cCityData.get_provCode()),cProvData,0) )
                    {
                        p_pSNumber->m_iProvDefaultOp =
                                    cProvData.get_defaultOperatorId();
                    }
                    l_prov_flag = 1;
                }
            }

            if(memcmp(l_szNumber + l_excursion, "0", 1)==0)
            {
                strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion + 1);
            }
            else
                strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion);

            return ANA_SUCCESS;
        }
        else if( 0 == m_ODACClient.findMmmCode(snapShot,l_szHlr, cMmmCodeData, 0) )
        {
            //  长途区号加手机号码的拨号方式
            if (l_acode_flag == 1)
            {
                if (strcmp(p_pSNumber->m_szHomeAreaCode,
                        cMmmCodeData.get_areaCode()) != 0)
                {
    //                  l_excursion -= l_mhead_len;
#ifdef __HLR_TO_AREACODE__  
                        strcpy(p_pSNumber->m_szHomeAreaCode,
                                cMmmCodeData.get_areaCode());
                                
#else
                        goto AREA_MOBILE_CON;
#endif      
                }
            }
            else
            {
                 strcpy(p_pSNumber->m_szHomeAreaCode,
                            cMmmCodeData.get_areaCode());
                 l_acode_flag = 1;
            }
            p_pSNumber->m_iOperatorParty = cMmmCodeData.get_operatorId();
    
            p_pSNumber->m_iNetWork = cMmmCodeData.get_netType();
    
            if (l_ccode_flag == 0)
            {
                strcpy(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA);
            }

            if (l_prov_flag == 0)
            {
                //  由地区长途区号得到省代码
                if ( 0 == m_ODACClient.findCityByAreaCode(snapShot,p_pSNumber->m_szHomeAreaCode, cCityData,0))
                {
                    strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
                    strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                    if ( 0 == m_ODACClient.findProv(snapShot,atoi(cCityData.get_provCode()), cProvData,0) )
                    {
                        p_pSNumber->m_iProvDefaultOp =
                                    cProvData.get_defaultOperatorId();
                    }
                    l_prov_flag = 1;
                }
            }
 
            if(memcmp(l_szNumber + l_excursion, "0", 1)==0)
            {
                strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion + 1);
            }
            else
                strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion);

            return ANA_SUCCESS;             
        }       
//如果是手机号码，但H码表、M码表找不到相应信息的，则直接打成错号
#ifdef __NOT_FIND_HOMEAREA_BE_ERR_RECORD__
    else if(1)
    {
        p_pSNumber->m_iNumberType = ANA_NUMBER_ERROR;
        //cout << l_szHlr << ": NOT FIND HOMEAREA, ERROR NUMBER." << endl;
        return ANA_SUCCESS;
    }  
#endif         
        else if ((memcmp(l_szNumber + l_excursion + l_mhead_len -2 ,
                            "13800", 5) == 0))
                            // qinhaiping delete it 138005712680 号码位数不正确 &&
                    //(strlen(l_szNumber) - l_excursion == 11))
        {
            p_pSNumber->m_iOperatorParty = 2;
            p_pSNumber->m_iNetWork = 2;
            if (l_ccode_flag == 0)
            {
                strcpy(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA);
            }

            if(memcmp(l_szNumber + l_excursion, "0", 1)==0)
            {
                strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion + 1);
            }
            else
                strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion);

            if (memcmp(l_szNumber + l_excursion + l_mhead_len + 3,"138", 3) == 0)
            {
                strcpy(p_pSNumber->m_szHomeAreaCode,
                       p_pchCenterArea);
                l_acode_flag = 1;
            }
            else
            {
                bzero(l_szHlr, sizeof(l_szHlr));
                memcpy(l_szHlr, l_szNumber + l_excursion + l_mhead_len + 3, 4);  //去掉800后取4位 如138005712680 取5712  此处也可以全取，但是为节约时间，只取4位，qinhaiping 2005.11.3
                if ( 0 == m_ODACClient.findCityByAreaCode(snapShot,l_szHlr, cCityData,0) )
                {
                    strcpy(p_pSNumber->m_szHomeAreaCode,
                            cCityData.get_areaCode());
                    strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                    l_acode_flag = 1;
                }   
                else
                {
#ifdef __HLR_BE_LONG__
                            strcpy(p_pSNumber->m_szHomeAreaCode,
                                    "NNN");

#else
                            strcpy(p_pSNumber->m_szHomeAreaCode,
                                    p_pchCenterArea);
#endif                                  
                    l_acode_flag = 1;
                }
            }

            //qinhaiping add it 20051103 应该算分析成功了
            if (l_prov_flag == 0)
            {
                //  由地区长途区号得到省代码
                if ( 0 == m_ODACClient.findCityByAreaCode(snapShot,p_pSNumber->m_szHomeAreaCode, cCityData,0) )
                {
                    strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
                           strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                    if ( 0 == m_ODACClient.findProv(snapShot,atoi(cCityData.get_provCode()), cProvData,0) )
                    {
                        p_pSNumber->m_iProvDefaultOp =
                                    cProvData.get_defaultOperatorId();
                    }
                    l_prov_flag = 1;
                }
            }

            return ANA_SUCCESS;
            //add end  
        }
        else 
        {
            if (l_acode_flag == 1)
            {
                goto AREA_MOBILE_CON;
            }
            if (l_ccode_flag == 0)
            {
                strcpy(p_pSNumber->m_szHomeCountryCode, ANA_CCODE_CHINA);
            }   
            p_pSNumber->m_iOperatorParty = cMobileSegData.get_operatorId();

            p_pSNumber->m_iNetWork = cMobileSegData.get_netType();
#ifdef __HLR_BE_LONG__
                strcpy(p_pSNumber->m_szHomeAreaCode,                "NNN");
#else                           
                strcpy(p_pSNumber->m_szHomeAreaCode,                p_pchCenterArea);
#endif              
            strcpy(p_pSNumber->m_szProvId ,                 m_szProvCode);
            //p_pSNumber->m_iNumberType = ANA_NUMBER_ERROR;  
            strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion );

            return ANA_SUCCESS;
        }
    }

    if (l_acode_mob_flag == -1)
    {
        goto AREA_MOBILE_CON;
    }

    //  是否地区代码开头
    if ((l_ccode_flag == 1) ||
        (memcmp(l_szNumber + l_excursion, ANA_ACODE_HEAD,
                ANA_ACODE_HEAD_LEN) == 0))
    {
#ifdef __ZERO_BE_LONG__
        bMustBeLong = 1;        
#endif          
        goto ACODE_CON;
    }
    else
    {
        //qinhaiping add 20051022 没有地区代码开头也可能是有区号的,在待处理号码前加0，如果能找到区号，且去掉区号后号码长途大于7，或者虽然不大于7但是能在特服号码中找到。
        //则认为前面的是没有0的地区号，
        //否则无需处理，此方式来源于旧系统。
        //1104 只对主叫且交换机表中设置校正属性为"0" 的才假设为长途,目前只有诺基亚的交换机是这样的.
        if((nCallingType == 2 && cGsmMscData.get_calledModify()!=NULL&&(strcmp("0",cGsmMscData.get_calledModify())==0 ))
           ||(nCallingType==1 && cGsmMscData.get_callingModify()!=NULL&&(strcmp("0",cGsmMscData.get_callingModify())==0)) )
        {
            if(0 == m_ODACClient.findCityByAreaCode(snapShot,l_szNumber + l_excursion,cCityData,0))
            {
                //后面接入的是特殊号码，可能长度小于7位。
                l_excursion += strlen(cCityData.get_areaCode());
                if( 0 == m_ODACClient.strictfindSpecialNumber(snapShot,m_nServiceId, 
                                                                p_pchCenterArea, 
                                                                p_pchTemp, 
                                                                l_szNumber + l_excursion, 
                                                                cSpecialNumberData,
                                                                0) )
                {
                    if(strcmp(cCityData.get_areaCode(),p_pchCenterArea)==0) //号码前的区号weiisis结算地区?   
                    {
                        l_acode_flag = 1;
                        strcpy(p_pSNumber->m_szHomeAreaCode,cCityData.get_areaCode());
                        goto    SS_CON;     
                    }
                    else
                        l_excursion -= strlen(cCityData.get_areaCode());
                }   
                else if(strlen(l_szNumber + l_excursion) >=7)
                {
                    l_excursion -= strlen(cCityData.get_areaCode());    
                    goto ACODE_FINDCITY;
                }
                else
                {   
                    l_excursion -= strlen(cCityData.get_areaCode());
                }
            }
        //qinhaiping add end
        }

        goto DEF_CON;
    }

ACODE_CON:
    if ((l_ccode_flag != 1) &&
        (memcmp(l_szNumber + l_excursion, ANA_ACODE_HEAD,
                ANA_ACODE_HEAD_LEN) == 0))
    {
#ifdef __ZERO_BE_LONG__
        bMustBeLong = 1;        
#endif      
        l_excursion += ANA_ACODE_HEAD_LEN;
    }

    if (0 == m_ODACClient.findCityByAreaCode(snapShot,l_szNumber + l_excursion, cCityData,0))
    {
ACODE_FINDCITY:
        strcpy(p_pSNumber->m_szHomeAreaCode,
                cCityData.get_areaCode());
        strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());

        if (l_prov_flag == 0)
        {
            //  由地区长途区号得到省代码
            if (0 == m_ODACClient.findCityByAreaCode(snapShot,p_pSNumber->m_szHomeAreaCode, cCityData,0))
            {
                strcpy(p_pSNumber->m_szProvId,cCityData.get_provCode());
                strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                if (0 == m_ODACClient.findProv(snapShot,atoi(cCityData.get_provCode()), cProvData,0))
                {
                    p_pSNumber->m_iProvDefaultOp =
                            cProvData.get_defaultOperatorId();
                }
                l_prov_flag = 1;
            }
        }

        l_excursion += strlen(p_pSNumber->m_szHomeAreaCode);
        l_acode_flag = 1;

        //  判断地区号码后是否为特服
        l_ss_flag = -1;
        goto SS_CON;
AREA_SS_CON:
        l_ss_flag = 0;

        //  判断地区号码后是否为卡接入
        l_ldk_flag = -1;
        goto LD_CON;
AREA_LDK_CON:
        l_ldk_flag = 0;

        //  判断地区号码后是否为手机号码
        l_acode_mob_flag = -1;
        goto MOBILE;
AREA_MOBILE_CON:
        l_acode_mob_flag = 0;
    }
// 缺省本地  20051116 qinhaiping 
//  else
//          return ANA_ERROR;
// modify end 20051116
    else
    {
        if(bMustBeLong) //号码前面有0，一定判为长途
        {
            strcpy(p_pSNumber->m_szHomeAreaCode,"NNN");
            if(0 == m_ODACClient.findCityByAreaCode(snapShot,p_pSNumber->m_szHomeAreaCode, cCityData,0))
            {
                strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
                strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                if ( 0 == m_ODACClient.findProv(snapShot,atoi(cCityData.get_provCode()), cProvData,0) )
                {
                    p_pSNumber->m_iProvDefaultOp =
                            cProvData.get_defaultOperatorId();
                }
            }
            p_pSNumber->m_iNetWork = ANA_NET_TYPE_PSTN;
            if (p_pSNumber->m_iOperatorParty == 0)
            {
                p_pSNumber->m_iOperatorParty = p_pSNumber->m_iProvDefaultOp;
            } 
            return ANA_SUCCESS;
        }       
    }
DEF_CON:

    if (l_ccode_flag == 0)
    {
        strcpy(p_pSNumber->m_szHomeCountryCode,
                ANA_CCODE_CHINA);
    }

    if (l_acode_flag == 0)
    {
#ifdef __SHORT_BE_LONG__
            if(strlen(l_szNumber + l_excursion)<=6 || strlen(l_szNumber + l_excursion)>8)
                strcpy( p_pSNumber->m_szHomeAreaCode,"NNN");
            else
                strcpy(p_pSNumber->m_szHomeAreaCode,                p_pchCenterArea);   
#else
        strcpy(p_pSNumber->m_szHomeAreaCode,
                p_pchCenterArea);                   
#endif      

    }
    strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion);
    
    if (l_prov_flag == 0)
    {
        //  由地区长途区号得到省代码
        if ( 0 == m_ODACClient.findCityByAreaCode(snapShot,p_pSNumber->m_szHomeAreaCode, cCityData,0) )
        {
            strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
            strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
            if ( 0 == m_ODACClient.findProv(snapShot,atoi(cCityData.get_provCode()), cProvData,0) )
            {
                p_pSNumber->m_iProvDefaultOp =
                        cProvData.get_defaultOperatorId();
            }
            l_prov_flag = 1;
        }
    }

    if (p_pSNumber->m_iOperatorParty == 0)
    {
        p_pSNumber->m_iOperatorParty = p_pSNumber->m_iProvDefaultOp;
    }

    //  如果省代码为本省，需要分析固话的营业区间信息，网络信息。
    if (strcmp(p_pSNumber->m_szProvId, m_szProvCode) == 0)
    {
        if ( 0 == m_ODACClient.findPstnNumSeg(snapShot,p_pSNumber->m_szHomeAreaCode,l_szNumber + l_excursion, cPstnNumsegData,0) )
        {
            strcpy(p_pSNumber->m_szHomeBusinessCode,
                    cPstnNumsegData.get_businessAreaCode());

            p_pSNumber->m_iOperatorParty =
                    cPstnNumsegData.get_operatorId();
            p_pSNumber->m_iNetWork = cPstnNumsegData.get_netType();
        }
    }

    //如果没有给network赋值，默认为本地PSTN
    if (p_pSNumber->m_iNetWork == 0)
    {
        p_pSNumber->m_iNetWork = ANA_NET_TYPE_PSTN;
    }

    return ANA_SUCCESS;
}

//NumberAnalysisNew
/*!
 * \func NumberAnalysisNew
 * \brief
 * \hujie add
 */
int NumberAnalysisNew(xc::CSnapshot & snapShot, 
            const char* p_pchNumber,
            int         nCallingType ,//1  主叫， 2 被叫
            const char* p_pMscId  ,  //交换机ID,NOKIA交换机被叫号即使是长途也没有0
            int64    iStartTime, //hujie add,判断局数据生,失效时间
            const char*       p_pchCenterArea,
            NumberInfo* p_pSNumber,
            int32 m_nServiceId)
{
    char l_szNumber[64];
    char l_szHlr[8 + 1];
    int l_excursion;
    int l_excursion_temp=0;
    int l_mhead_len;
    int l_len;
    int i,j;

    int l_ccode_flag; //country_code
    int l_acode_flag; //area_code
    int l_ld_flag;       //
    int l_ldk_flag;     //
    int l_ss_flag;      //special_service
    int l_acode_mob_flag;
    int l_prov_flag;  //province_code
    int l_unkown_calling_flag = 0; //主叫号码不可识别
    int bMustBeLong = 0;
    //default operid old system from sys_prov.defaule_oper_id 南电北网
    int m_nDefaultOperId = 1;
    
    char m_szProvCode[64];
    
    char * p_pchTemp;

    memset(p_pSNumber, 0, sizeof(NumberInfo));
    
    CBpsGsmMsc        cGsmMscData;
    CSysCity          cCityData;
    CSysProv          cProvData;
    CBpsHlr           cHlrData;
    CSysCountry       cCountryData;
    CBpsAddMmmCode    cMmmCodeData;
    CBpsPstnNumseg    cPstnNumsegData;
    CBpsAddMobileSeg  cMobileSegData;
    CBpsAccessNumber  cAccessNumberData;
    CBpsSpecialNumber cSpecialNumberData;

    // qinhaiping add 20051105 获取交换机属性
    //if( (p_pMscId != NULL && strlen(p_pMscId) != 0)&& (0 != m_ODACClient.findGsmMsc(p_pMscId, cGsmMscData, NULL)) )
    if( (p_pMscId != NULL && strlen(p_pMscId) != 0)
        && (0 != odac_app_lookup_CBpsGsmMsc(snapShot,p_pMscId,iStartTime, cGsmMscData)) )
    {
        return ANA_SUCCESS;
    }
    //add end    

    l_len = strlen(p_pchNumber);
    bzero(l_szNumber, sizeof(l_szNumber));
    if (l_len > sizeof(l_szNumber))
        l_len = sizeof(l_szNumber) - 1;
            
    if ((p_pchNumber == NULL) || (l_len == 0))
    {
        //qinhaiping modify it 老系统处理时，如果是空号，置为'0'
        memcpy(l_szNumber ,"0",1);
        l_len = 1;
        //return ANA_ERROR;
    }
    else
    {
        j=0;
        for(i=0;i<l_len;i++)
        {
            if(((*(p_pchNumber+i) <='9')&&(*(p_pchNumber+i) >='0')))
            {
                *(l_szNumber+j)=*(p_pchNumber+i);
                j++;
            }
        }
    }
                
    l_len = strlen(l_szNumber);

    /*  判断号码是否为全空号    */
    for (i = 0; i < l_len; i++)
    {
        if (l_szNumber[i] != ' ')
        {
            break;
        }
    }

    if (i == l_len)
    {
        printf("输入的号码为全空号!\n");
        p_pSNumber->m_iNumberType = ANA_NUMBER_ERROR;
        return ANA_SUCCESS;
    }

    /*  下面是一个正常号码的分析流程    */
    l_excursion = 0;
    l_ccode_flag = 0;
    l_acode_flag = 0;
    l_ld_flag = 0;
    l_ss_flag = 0;
    l_ldk_flag = 0;
    l_acode_mob_flag = 0;
    l_prov_flag = 0;

    p_pSNumber->m_iNumberType = ANA_NUMBER_COMM;

START:
    /*  判断号码是否为全零号    */
    l_len = strlen(l_szNumber+l_excursion);
    for (i = 0; i < l_len; i++)
    {
        if (*(l_szNumber+l_excursion+i) != '0')
        {
            break;
        }
    }
    if (i == l_len)
    {
//      printf("输入的号码为全零号!\n");
#ifdef __ZERO_BE_LONG__
                        goto ACODE_CON;
#else
                        goto DEF_CON;
#endif
//      return ANA_SUCCESS;
    }
                
    //参考老系统的号码规整先做一部分处理
    if(nCallingType== 1)  //主叫
    {
        if(strncmp(l_szNumber+l_excursion,"0017951",7)==0)
        {
            l_excursion += 2;
        }       
        if(strncmp(l_szNumber+l_excursion,"0021179",7)==0)
        {
            l_excursion += 1;
        }     
        if(strncmp(l_szNumber+l_excursion,"0000",4)==0)
            l_excursion += 2;
        if(strncmp(l_szNumber+l_excursion,"0086",4)==0)
        {
            l_ccode_flag = 1;
            l_excursion += 4;
            strcpy(p_pSNumber->m_szHomeCountryCode, ANA_CCODE_CHINA);
        }                                   

        if(strncmp(l_szNumber+l_excursion,"017",3)==0)
            l_excursion +=1;

        if(strncmp(l_szNumber+l_excursion,"096",3)==0)
            l_excursion +=1;

        if(strncmp(l_szNumber+l_excursion,"011",3)==0)
            l_excursion +=1;

        if(strncmp(l_szNumber+l_excursion,"019",3)==0)
            l_excursion +=1;                  

        if(strncmp(l_szNumber+l_excursion,"44179",5)==0)
            l_excursion +=2;
    }
    if((strncmp(l_szNumber+l_excursion,"861",3)==0&&strlen(l_szNumber+l_excursion)>12)||
       (strncmp(l_szNumber+l_excursion,"8617951",7)==0&&strlen(l_szNumber+l_excursion)>8))
    {
        l_excursion +=2;
        l_ccode_flag = 1;
        strcpy(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA);
    } 
                                      
#ifdef __EXECUTION_SEQUENCE__
                        goto LD_CON;
#endif

    //  判断是否为特服号码
SS_CON:
//  if (m_BpsSpecialNumberMgr.Find(l_szNumber + l_excursion,
//                  m_BpsSpecialNumberData, m_ErrorMsg))

    if (l_acode_flag)
    {
        p_pchTemp =  p_pSNumber->m_szHomeAreaCode;  
    }
    else
    {
        p_pchTemp =  (char *)p_pchCenterArea;
    }
    if ( 0 == m_ODACClient.findSpecialNumber(snapShot,m_nServiceId, 
                             p_pchCenterArea, 
                             p_pchTemp, 
                             l_szNumber + l_excursion, 
                             cSpecialNumberData, 
                             iStartTime) )
    {
        l_ss_flag = 1;
        p_pSNumber->m_iOperatorParty = cSpecialNumberData.get_operatorId();

        p_pSNumber->m_iSpecialType = cSpecialNumberData.get_numberType();
        
        p_pSNumber->m_iSpecialId = cSpecialNumberData.get_numberId()*100 
                                    + cSpecialNumberData.get_conditionGroupId();
        
        strcpy(p_pSNumber->m_szSpecialNumber,
                cSpecialNumberData.get_specialNumber());
//      strcpy(p_pSNumber->m_szOriginalNumber,
//              m_BpsSpecialNumberData.getSpecialNumber());
//   qinhaiping modify 20051031
        strcpy(p_pSNumber->m_szOriginalNumber,l_szNumber + l_excursion);

        if (l_ccode_flag == 0)
        {
            strcpy(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA);
        }

        if (l_acode_flag == 0)
        {
            strcpy(p_pSNumber->m_szHomeAreaCode,p_pchCenterArea);
        }

        if (l_prov_flag == 0)
        {
            //  由地区长途区号得到省代码
            if ( 0 == m_ODACClient.findCityByAreaCode(snapShot,p_pSNumber->m_szHomeAreaCode, cCityData,iStartTime) )
            {
                strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
                strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                if (0 == m_ODACClient.findProv(snapShot,
                        atoi(cCityData.get_provCode()),
                        cProvData,iStartTime))
                {
                    p_pSNumber->m_iProvDefaultOp =
                            cProvData.get_defaultOperatorId();
                }
                l_prov_flag = 1;
            }
        }
            
        return ANA_SUCCESS;
    }
    
    if (l_ss_flag == -1)
    {
#ifdef __EXECUTION_SEQUENCE__
           goto AREA_LDK_CON;
#endif
        goto AREA_SS_CON;
    }

#ifdef __EXECUTION_SEQUENCE__
                                goto MOBILE;
#endif

    //  判断是否为长途接入
LD_CON:
    if (0 == m_ODACClient.findAccessNumber(snapShot,l_szNumber + l_excursion,
                    cAccessNumberData, iStartTime))
    {
        l_excursion += strlen(cAccessNumberData.get_accessNumber());

        p_pSNumber->m_iAccessFlag = cAccessNumberData.get_numberType();

        p_pSNumber->m_iAccessOperator =
                cAccessNumberData.get_operatorId();

        strcpy(p_pSNumber->m_szAccessNumber,
                cAccessNumberData.get_accessNumber());
        if (cAccessNumberData.get_analysisType() == ANA_ACCESS_TYPE_NO)
        {
            //  不用分析后面的号码
            if (l_ccode_flag == 0)
            {
                strcpy(p_pSNumber->m_szHomeCountryCode, ANA_CCODE_CHINA);
            }

            if (l_acode_flag == 0)
            {
                strcpy(p_pSNumber->m_szHomeAreaCode, p_pchCenterArea);
            }
            strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion);

            if (l_prov_flag == 0)
            {
                //  由地区长途区号得到省代码
                if (0 == m_ODACClient.findCityByAreaCode(snapShot,
                            p_pSNumber->m_szHomeAreaCode,
                            cCityData,iStartTime))
                {
                    strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
                    strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                    if (0 ==m_ODACClient.findProv(snapShot,
                            atoi(cCityData.get_provCode()),
                            cProvData,iStartTime))
                    {
                        p_pSNumber->m_iProvDefaultOp =
                                cProvData.get_defaultOperatorId();
                    }
                    l_prov_flag = 1;
                }
            }

            if (p_pSNumber->m_iOperatorParty == 0)
            {
                p_pSNumber->m_iOperatorParty = p_pSNumber->m_iAccessOperator;
            }

            return ANA_SUCCESS;
        }
        else
        {
            //  IP接入
            //对接入号码继续规整
            //如果号码全为0  走缺省
            
            l_ld_flag = 1;
            goto START;
        }
    }

    if (l_ldk_flag == -1)
    {
#ifdef __EXECUTION_SEQUENCE__
                l_acode_flag = 1;
        l_ss_flag = -1;
        //判断是否为特服
           goto SS_CON;
#endif
        goto AREA_LDK_CON;
    }

    //  是否国家代码开头
    if (memcmp(l_szNumber + l_excursion, ANA_CCODE_HEAD,
                ANA_CCODE_HEAD_LEN) == 0)
    {
        l_excursion += ANA_CCODE_HEAD_LEN;

        if (memcmp(l_szNumber + l_excursion, ANA_CCODE_CHINA,
                ANA_CCODE_CHINA_LEN) == 0)
        {
            strcpy(p_pSNumber->m_szHomeCountryCode,
                    ANA_CCODE_CHINA);
            l_excursion += ANA_CCODE_CHINA_LEN;
            l_ccode_flag = 1;
        }
        else
        {
            //qinhaiping modify it
            //国际来话且无主叫号码,有可能由多个0，规范中为2个或3个0，但是实际上发现有4个0的，因为无法确认到底有多少个0，所以循环
            while((memcmp(l_szNumber + l_excursion,"0",1)==0))
            {
                l_excursion ++;
            }

            //CIC码放在接入号中了,标志为TYPE_CIC_CODE
            if(0 == m_ODACClient.findAccessNumber(snapShot,l_szNumber + l_excursion,
                                         cAccessNumberData,
                                         iStartTime))
            {
                if(cAccessNumberData.get_cicFlag()&&p_pSNumber->m_iAccessFlag==0)
                {
                    l_excursion += strlen(cAccessNumberData.get_accessNumber()); 
                    p_pSNumber->m_iAccessOperator = cAccessNumberData.get_operatorId();
                    bzero(p_pSNumber->m_szOriginalNumber,sizeof(p_pSNumber->m_szOriginalNumber));
                    if (p_pSNumber->m_iOperatorParty == 0)
                    {
                        p_pSNumber->m_iOperatorParty = ANA_OPER_INTER;
                    }   
                    strcpy(p_pSNumber->m_szAccessNumber,
                           cAccessNumberData.get_accessNumber());                               
                    p_pSNumber->m_iAccessFlag = cAccessNumberData.get_numberType();
                    l_unkown_calling_flag = 1;  //
                }
            }
            if (0 != m_ODACClient.findCountry(snapShot,
                            l_szNumber + l_excursion,
                            cCountryData,iStartTime))
            {
                //p_pSNumber->m_iNumberType = ANA_NUMBER_ERROR;

                //20051027  如果找不到，则按国内长途处理,不再返回错误，这样可能导致国家代码不全，但是错误无法发现，
                //由于实际号码经常有00加地区号的，所以权宜之计取此方式
                if(l_unkown_calling_flag != 1 && l_ld_flag !=1)
                {
                     strcpy(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA);
                     l_ccode_flag = 1;
                     goto   ACODE_CON;      
                }                               
                else if (l_unkown_calling_flag == 1)
                {
                    //0019开头的美国号码
                    p_pSNumber->m_iOperatorParty = 0; //清除判断为CIC代码时赋的值
                    p_pSNumber->m_iAccessFlag =0;
                    p_pSNumber->m_iAccessOperator = 0;
                    l_excursion -= strlen(p_pSNumber->m_szAccessNumber);
                    memset(p_pSNumber->m_szAccessNumber,0,strlen(p_pSNumber->m_szAccessNumber));

                    if(0 != m_ODACClient.findCountry(snapShot,
                                l_szNumber + l_excursion,
                                cCountryData,iStartTime))
                    {
                        p_pSNumber->m_iNumberType = ANA_NUMBER_ERROR; //国家代码表没有美国
                        return ANA_SUCCESS;
                    }    
                    l_unkown_calling_flag = 0;
                }   
                //20051027 add end;
            }
            strcpy(p_pSNumber->m_szHomeCountryCode,
                   cCountryData.get_countryCode());

            p_pSNumber->m_iCountryType = cCountryData.get_countryType();

            //qinhaiping add
            if(l_unkown_calling_flag == 1 && strncmp(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA,2)==0)
            {
                //国际来话，但是国家代码是中国
                l_excursion += strlen(l_szNumber + l_excursion);
                goto DEF_CON;
            }
            else if(strncmp(p_pSNumber->m_szHomeCountryCode,ANA_CCODE_CHINA,2)==0)
            {
                l_ccode_flag = 1;
                goto    ACODE_CON;
            }     

            //qinhaiping add end  
            l_excursion += strlen(p_pSNumber->m_szHomeCountryCode);
            l_ccode_flag = 1;

            if(p_pSNumber->m_iOperatorParty == 0)
            {
                p_pSNumber->m_iOperatorParty = m_nDefaultOperId;
            }
            if(l_unkown_calling_flag != 1)
                strcpy(p_pSNumber->m_szOriginalNumber,l_szNumber + l_excursion );

            return ANA_SUCCESS;
        }
    }

#ifdef __EXECUTION_SEQUENCE__
                        goto SS_CON;
#endif

    //  判断是否为手机号码
MOBILE:
    /*if (((l_ccode_flag == 0) &&
        (((memcmp(l_szNumber + l_excursion, ANA_MOBILE_HEAD,
                ANA_MOBILE_HEAD_LEN) == 0)&&(strlen(l_szNumber + l_excursion) >= 7)) ||
        ((memcmp(l_szNumber + l_excursion, ANA_MOBILE_LHEAD,
                ANA_MOBILE_LHEAD_LEN) == 0)&&(strlen(l_szNumber + l_excursion) >= 8)))) ||
        ((l_ccode_flag == 1) &&
        (memcmp(l_szNumber + l_excursion, ANA_MOBILE_HEAD,
                ANA_MOBILE_HEAD_LEN) == 0)))*/
    if(0 == m_ODACClient.findMobileSeg(snapShot,l_szNumber+l_excursion ,cMobileSegData,iStartTime))
    {
        bzero(l_szHlr, sizeof(l_szHlr));

        if(memcmp(cMobileSegData.get_mCodeSeg(),"0",1)==0)
            l_mhead_len = 3;
        else
            l_mhead_len = 2;

        memcpy(l_szHlr, l_szNumber + l_excursion + l_mhead_len -2 , 8);

        if (0 == m_ODACClient.findHlr(snapShot,(l_szHlr), cHlrData, iStartTime))
        {
            //  长途区号加手机号码的拨号方式
            if (l_acode_flag == 1)
            {
                if (strcmp(p_pSNumber->m_szHomeAreaCode,
                           cHlrData.get_areaCode()) != 0)
                {
    //                  l_excursion -= l_mhead_len;
#ifdef __HLR_TO_AREACODE__  
                        strcpy(p_pSNumber->m_szHomeAreaCode,
                                cHlrData.get_areaCode());
                                
#else
                        goto AREA_MOBILE_CON;
#endif                      
                }
            }
            else
            {
                strcpy(p_pSNumber->m_szHomeAreaCode,
                       cHlrData.get_areaCode());
                l_acode_flag = 1;
            }
            p_pSNumber->m_iOperatorParty = cHlrData.get_operatorId();

            p_pSNumber->m_iNetWork = cHlrData.get_netType();
            if (l_ccode_flag == 0)
            {
                strcpy(p_pSNumber->m_szHomeCountryCode, ANA_CCODE_CHINA);
            }

            if (l_prov_flag == 0)
            {
                //  由地区长途区号得到省代码
                if (0 == m_ODACClient.findCityByAreaCode(snapShot,
                            p_pSNumber->m_szHomeAreaCode,
                            cCityData,iStartTime))
                {
                    strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
                    strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                    if (0 == m_ODACClient.findProv(snapShot,
                            atoi(cCityData.get_provCode()),
                            cProvData,iStartTime))
                    {
                        p_pSNumber->m_iProvDefaultOp =
                                cProvData.get_defaultOperatorId();
                    }
                    l_prov_flag = 1;
                }
            }

            if(memcmp(l_szNumber + l_excursion, "0", 1)==0)
            {
                strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion + 1);
            }
            else
                strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion);

            return ANA_SUCCESS;
        }
        else if(0 == m_ODACClient.findMmmCode(snapShot,(l_szHlr), cMmmCodeData,iStartTime))
        {
            //  长途区号加手机号码的拨号方式
            if (l_acode_flag == 1)
            {
                if (strcmp(p_pSNumber->m_szHomeAreaCode,
                        cMmmCodeData.get_areaCode()) != 0)
                {
    //                  l_excursion -= l_mhead_len;
#ifdef __HLR_TO_AREACODE__  
                        strcpy(p_pSNumber->m_szHomeAreaCode,
                                cMmmCodeData.get_areaCode());
                                
#else
                        goto AREA_MOBILE_CON;
#endif
                }
            }
            else
            {
                strcpy(p_pSNumber->m_szHomeAreaCode,
                       cMmmCodeData.get_areaCode());
                l_acode_flag = 1;
            }
            p_pSNumber->m_iOperatorParty = cMmmCodeData.get_operatorId();

            p_pSNumber->m_iNetWork = cMmmCodeData.get_netType();
 
            if (l_ccode_flag == 0)
            {
                strcpy(p_pSNumber->m_szHomeCountryCode, ANA_CCODE_CHINA);
            }

            if (l_prov_flag == 0)
            {
                //  由地区长途区号得到省代码
                if (0 == m_ODACClient.findCityByAreaCode(snapShot,
                            p_pSNumber->m_szHomeAreaCode,
                            cCityData,iStartTime))
                {
                    strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
                    strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                    if (0 == m_ODACClient.findProv(snapShot,
                            atoi(cCityData.get_provCode()),
                            cProvData,iStartTime))
                    {
                        p_pSNumber->m_iProvDefaultOp =
                                cProvData.get_defaultOperatorId();
                    }
                    l_prov_flag = 1;
                }
            }

            if(memcmp(l_szNumber + l_excursion, "0", 1)==0)
            {
                strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion + 1);
            }
            else
                strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion);
                    
            return ANA_SUCCESS;             
        }       
//如果是手机号码，但H码表、M码表找不到相应信息的，则直接打成错号
#ifdef __NOT_FIND_HOMEAREA_BE_ERR_RECORD__
        else if(1)
        {
            p_pSNumber->m_iNumberType = ANA_NUMBER_ERROR;
            //cout << l_szHlr << ": NOT FIND HOMEAREA, ERROR NUMBER." << endl;
            return ANA_SUCCESS;
        }  
#endif         

        else if ((memcmp(l_szNumber + l_excursion + l_mhead_len -2 , "13800", 5) == 0))
                        // qinhaiping delete it 138005712680 号码位数不正确 &&
                    //(strlen(l_szNumber) - l_excursion == 11))
        {
            p_pSNumber->m_iOperatorParty = 2;
            p_pSNumber->m_iNetWork = 2;
            if (l_ccode_flag == 0)
            {
                strcpy(p_pSNumber->m_szHomeCountryCode, ANA_CCODE_CHINA);
            }

            if(memcmp(l_szNumber + l_excursion, "0", 1)==0)
            {
                strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion + 1);
            }
            else
                strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion);

            if (memcmp(l_szNumber + l_excursion + l_mhead_len + 3, "138", 3) == 0)
            {
                strcpy(p_pSNumber->m_szHomeAreaCode, p_pchCenterArea);
                l_acode_flag = 1;
            }
            else
            {
                bzero(l_szHlr, sizeof(l_szHlr));
                memcpy(l_szHlr, l_szNumber + l_excursion + l_mhead_len + 3, 4);  //去掉800后取4位 如138005712680 取5712  此处也可以全取，但是为节约时间，只取4位，qinhaiping 2005.11.3
                if (0 == m_ODACClient.findCityByAreaCode(snapShot,l_szHlr,
                            cCityData,iStartTime))
                {
                    strcpy(p_pSNumber->m_szHomeAreaCode,
                           cCityData.get_areaCode());
                    strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                    l_acode_flag = 1;
                }   
                else
                {
#ifdef __HLR_BE_LONG__
                            strcpy(p_pSNumber->m_szHomeAreaCode, 
                                    "NNN");
#else                                                               
                            strcpy(p_pSNumber->m_szHomeAreaCode,
                                    p_pchCenterArea);
#endif                                  
                    l_acode_flag = 1;
                }
            }

            //qinhaiping add it 20051103 应该算分析成功了
            if (l_prov_flag == 0)
            {
                //  由地区长途区号得到省代码
                if (0 == m_ODACClient.findCityByAreaCode(snapShot,
                                    p_pSNumber->m_szHomeAreaCode,
                                    cCityData,iStartTime))
                {
                    strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
                    strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                    if (0 == m_ODACClient.findProv(snapShot,
                                        atoi(cCityData.get_provCode()),
                                        cProvData,iStartTime))
                    {
                         p_pSNumber->m_iProvDefaultOp =
                                    cProvData.get_defaultOperatorId();
                    }
                    l_prov_flag = 1;
                }
            }

            return ANA_SUCCESS;
            //add end  
        }
        else 
        {
    if(m_ODACClient.findCityByAreaCode(snapShot,l_szNumber + l_excursion,cCityData,iStartTime)!=0)
    {
            if (l_acode_flag == 1)
            {
                goto AREA_MOBILE_CON;
            }
            if (l_ccode_flag == 0)
            {
                strcpy(p_pSNumber->m_szHomeCountryCode, ANA_CCODE_CHINA);
            }   
            p_pSNumber->m_iOperatorParty = cMobileSegData.get_operatorId();

            p_pSNumber->m_iNetWork = cMobileSegData.get_netType();
                
#ifdef __HLR_BE_LONG__
                strcpy(p_pSNumber->m_szHomeAreaCode,                "NNN");
#else                               
                strcpy(p_pSNumber->m_szHomeAreaCode,                p_pchCenterArea);
#endif              
            strcpy(p_pSNumber->m_szProvId ,                 m_szProvCode);
            //p_pSNumber->m_iNumberType = ANA_NUMBER_ERROR;  
            strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion );

            return ANA_SUCCESS;
    }
        }
    }

    if (l_acode_mob_flag == -1)
    {
        goto AREA_MOBILE_CON;
    }

    //  是否地区代码开头
    if ((l_ccode_flag == 1) ||
        (memcmp(l_szNumber + l_excursion, ANA_ACODE_HEAD,
                ANA_ACODE_HEAD_LEN) == 0))
    {
#ifdef __ZERO_BE_LONG__
        bMustBeLong = 1;        
#endif          
        goto ACODE_CON;
    }
    else
    {
        //qinhaiping add 20051022 没有地区代码开头也可能是有区号的,在待处理号码前加0，如果能找到区号，且去掉区号后号码长途大于7，或者虽然不大于7但是能在特服号码中找到。
        //则认为前面的是没有0的地区号，
        //否则无需处理，此方式来源于旧系统。
        //1104 只对主叫且交换机表中设置校正属性为"0" 的才假设为长途,目前只有诺基亚的交换机是这样的.
        if((nCallingType == 2 && cGsmMscData.get_calledModify()!=NULL&&(strcmp("0",cGsmMscData.get_calledModify())==0 ))
          ||(nCallingType==1 && cGsmMscData.get_callingModify()!=NULL&&(strcmp("0",cGsmMscData.get_callingModify())==0)) ) 
 {
            if(0 == m_ODACClient.findCityByAreaCode(snapShot,l_szNumber + l_excursion,
                        cCityData,iStartTime))
            {
                //后面接入的是特殊号码，可能长度小于7位。
                l_excursion += strlen(cCityData.get_areaCode());
                if(0 == m_ODACClient.strictfindSpecialNumber(snapShot, m_nServiceId,
                                p_pchCenterArea,
                                p_pchTemp,
                                l_szNumber + l_excursion,
                                cSpecialNumberData,
                                iStartTime))
                {
                    if(strcmp(cCityData.get_areaCode(),p_pchCenterArea)==0) //号码前的区号weiisis结算地区?  
                    {
                        l_acode_flag = 1;
                        strcpy(p_pSNumber->m_szHomeAreaCode,
                        cCityData.get_areaCode());
                        goto    SS_CON;     
                    }
                    else
                        l_excursion -= strlen(cCityData.get_areaCode());
                }   
                else if(strlen(l_szNumber + l_excursion) >=7)
                {
                    l_excursion -= strlen(cCityData.get_areaCode());    
                    goto ACODE_FINDCITY;
                }
                else
                {   
                    l_excursion -= strlen(cCityData.get_areaCode());
                }
            }
        //qinhaiping add end
        }

        goto DEF_CON;
    }

ACODE_CON:
    if ((l_ccode_flag != 1) &&
        (memcmp(l_szNumber + l_excursion, ANA_ACODE_HEAD,
                ANA_ACODE_HEAD_LEN) == 0))
    {
#ifdef __ZERO_BE_LONG__
        bMustBeLong = 1;        
#endif      
        l_excursion += ANA_ACODE_HEAD_LEN;
    }
    if (0 == m_ODACClient.findCityByAreaCode(snapShot,l_szNumber + l_excursion,
                cCityData,iStartTime))
    {
ACODE_FINDCITY:
        strcpy(p_pSNumber->m_szHomeAreaCode,
                cCityData.get_areaCode());
        strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());

        if (l_prov_flag == 0)
        {
            //  由地区长途区号得到省代码
            if (0 == m_ODACClient.findCityByAreaCode(snapShot,
                        p_pSNumber->m_szHomeAreaCode,
                        cCityData,iStartTime))
            {
                strcpy(p_pSNumber->m_szProvId,cCityData.get_provCode());
                strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                if (0 == m_ODACClient.findProv(snapShot,
                        atoi(cCityData.get_provCode()),
                        cProvData,iStartTime))
                {
                    p_pSNumber->m_iProvDefaultOp =
                            cProvData.get_defaultOperatorId();
                }
                l_prov_flag = 1;
            }
        }
    if(memcmp(l_szNumber + l_excursion, ANA_ACODE_HEAD, ANA_ACODE_HEAD_LEN) == 0)
            l_excursion += strlen(p_pSNumber->m_szHomeAreaCode) + 1;
    else
        l_excursion += strlen(p_pSNumber->m_szHomeAreaCode);
        l_acode_flag = 1;

        //  判断地区号码后是否为特服
        l_ss_flag = -1;
#ifdef __EXECUTION_SEQUENCE__                    //浙江先接入号在特服
                        goto AREA_SS_CON;
#endif
        goto SS_CON;
AREA_SS_CON:
        l_ss_flag = 0;

        //  判断地区号码后是否为卡接入
        l_ldk_flag = -1;
        goto LD_CON;
AREA_LDK_CON:
        l_ldk_flag = 0;

        //  判断地区号码后是否为手机号码
        l_acode_mob_flag = -1;
        goto MOBILE;
AREA_MOBILE_CON:
        l_acode_mob_flag = 0;
    }
// 缺省本地  20051116 qinhaiping 
//  else
//          return ANA_ERROR;
// modify end 20051116
    else
    {
        if(bMustBeLong) //号码前面有0，一定判为长途
        {
            strcpy(p_pSNumber->m_szHomeAreaCode,"NNN");
            if(0 == m_ODACClient.findCityByAreaCode(snapShot,
                        p_pSNumber->m_szHomeAreaCode,
                        cCityData,iStartTime))
            {
                strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
                strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
                if (0 == m_ODACClient.findProv(snapShot,
                        atoi(cCityData.get_provCode()),
                        cProvData,iStartTime))
                {
                    p_pSNumber->m_iProvDefaultOp =
                            cProvData.get_defaultOperatorId();
                }
            }
            p_pSNumber->m_iNetWork = ANA_NET_TYPE_PSTN;
            if (p_pSNumber->m_iOperatorParty == 0)
            {
                p_pSNumber->m_iOperatorParty = p_pSNumber->m_iProvDefaultOp;
            }           
            return ANA_SUCCESS;
        }       
    }
DEF_CON:

    if (l_ccode_flag == 0)
    {
        strcpy(p_pSNumber->m_szHomeCountryCode, ANA_CCODE_CHINA);
    }

    if (l_acode_flag == 0)
    {
#ifdef __SHORT_BE_LONG__
            if((strlen(l_szNumber + l_excursion)<=6 || strlen(l_szNumber + l_excursion)>8)&&nCallingType == 1)
                strcpy( p_pSNumber->m_szHomeAreaCode,"NNN");
            else
                strcpy(p_pSNumber->m_szHomeAreaCode,                p_pchCenterArea);   
#else
        strcpy(p_pSNumber->m_szHomeAreaCode,
                p_pchCenterArea);                   
#endif      

    }
    strcpy(p_pSNumber->m_szOriginalNumber, l_szNumber + l_excursion);

    if (l_prov_flag == 0)
    {
        //  由地区长途区号得到省代码
        if (0== m_ODACClient.findCityByAreaCode(snapShot,
                    p_pSNumber->m_szHomeAreaCode,
                    cCityData,iStartTime))
        {
            strcpy(p_pSNumber->m_szProvId, cCityData.get_provCode());
            strcpy(p_pSNumber->m_szHomeRegionCode, cCityData.get_regionCode());
            if (0 == m_ODACClient.findProv(snapShot,
                    atoi(cCityData.get_provCode()),
                    cProvData,iStartTime))
            {
                p_pSNumber->m_iProvDefaultOp =
                        cProvData.get_defaultOperatorId();
            }
            l_prov_flag = 1;
        }
    }

    if (p_pSNumber->m_iOperatorParty == 0)
    {
        p_pSNumber->m_iOperatorParty = p_pSNumber->m_iProvDefaultOp;
    }

    //  如果省代码为本省，需要分析固话的营业区间信息，网络信息。
    if (strcmp(p_pSNumber->m_szProvId, m_szProvCode) == 0)
    {
        if (0 == m_ODACClient.findPstnNumSeg(snapShot,p_pSNumber->m_szHomeAreaCode,
                        l_szNumber + l_excursion,
                        cPstnNumsegData,iStartTime))
        {
            strcpy(p_pSNumber->m_szHomeBusinessCode,
                    cPstnNumsegData.get_businessAreaCode());

            p_pSNumber->m_iOperatorParty =
                    cPstnNumsegData.get_operatorId();
            p_pSNumber->m_iNetWork = cPstnNumsegData.get_netType();
        }
    }

    //如果没有给network赋值，默认为本地PSTN
    if (p_pSNumber->m_iNetWork == 0)
    {
        p_pSNumber->m_iNetWork = ANA_NET_TYPE_PSTN;
    }

    return ANA_SUCCESS;
}

int getLongType(xc::CSnapshot & snapShot, 
            const char* p_FromAreaCode,    
            const char* p_ToAreaCode,
            const time_t tm)
{
    char szFromProvCode[5+1],szToProvCode[5+1];
    
    if(strcmp(p_FromAreaCode,p_ToAreaCode)==0)
        return ANA_TOLL_LAT;
    if(memcmp(p_FromAreaCode,ANA_CCODE_HEAD,ANA_CCODE_HEAD_LEN)==0)
    {
        if(memcmp(p_FromAreaCode+ANA_CCODE_HEAD_LEN,ANA_CCODE_HONGKONG,ANA_CCODE_HONGKONG_LEN)==0)
            return ANA_TOLL_HG;
        else if(memcmp(p_FromAreaCode+ANA_CCODE_HEAD_LEN,ANA_CCODE_MACAO,ANA_CCODE_MACAO_LEN)==0)
            return ANA_TOLL_AM;
        else if(memcmp(p_FromAreaCode+ANA_CCODE_HEAD_LEN,ANA_CCODE_TAIWAN,ANA_CCODE_TAIWAN_LEN)==0)
          return ANA_TOLL_TW;
      else
            return ANA_TOLL_IN;
    }
    else if(memcmp(p_ToAreaCode,ANA_CCODE_HEAD,ANA_CCODE_HEAD_LEN)==0)
    {   
        if(memcmp(p_FromAreaCode+ANA_CCODE_HEAD_LEN,ANA_CCODE_HONGKONG,ANA_CCODE_HONGKONG_LEN)==0)
            return ANA_TOLL_HG;
        else if(memcmp(p_FromAreaCode+ANA_CCODE_HEAD_LEN,ANA_CCODE_MACAO,ANA_CCODE_MACAO_LEN)==0)
            return ANA_TOLL_AM;
        else if(memcmp(p_FromAreaCode+ANA_CCODE_HEAD_LEN,ANA_CCODE_TAIWAN,ANA_CCODE_TAIWAN_LEN)==0)
          return ANA_TOLL_TW;
      else
            return ANA_TOLL_IN;
    }
    else
    {
            CSysCity cCityData;
            
        if (0 != m_ODACClient.findCityByAreaCode(snapShot,
                        p_FromAreaCode,
                        cCityData,tm))        
            return ANA_TOLL_UNKOWN;
        
        strncpy(szFromProvCode,cCityData.get_provCode(),6);  
        if(0 != m_ODACClient.findCityByAreaCode(snapShot,
                        p_ToAreaCode,
                        cCityData,tm))
            return ANA_TOLL_UNKOWN;     
        strncpy(szToProvCode,cCityData.get_provCode(),6);    
        if(strcmp(szFromProvCode, szToProvCode)==0)
            return  ANA_TOLL_PROV;
        else
            return ANA_TOLL_DOM;
    }
}               

/*!
 * \func GetPstnSwitchInfo
 *
 * \brief 此函数只判断被叫漫游类型，对主叫漫游类型目前无法判断
 *
 */
int getRoamType(xc::CSnapshot & snapShot, 
            const char *m_szHomeCountryCode,const char *m_szAccessNumber,
            const char *m_szHomeAreaCode,const char *m_szProvId,
            const char *p_pRoamAreaCode,const char *p_pCallType)
{
    char m_szProvCode[64];
    snprintf(m_szProvCode,sizeof(m_szProvCode),"%s","571");
    if(strcmp(p_pCallType,"01")==0)  //入局
    {
        if((memcmp(m_szHomeCountryCode,ANA_CCODE_HEAD,ANA_CCODE_HEAD_LEN)==0)&&
            memcmp(m_szAccessNumber,"17951",5)!=0)
            return ANA_ROAM_TYPE_INTERROAMIN;
        
        if(memcmp(m_szHomeAreaCode,p_pRoamAreaCode,strlen(m_szHomeAreaCode))==0)
            return ANA_ROAM_TYPE_NOTROAM;
        
        if(strlen(p_pRoamAreaCode)==0)
            return  ANA_ROAM_TYPE_NOTROAM;
        
        if(memcmp(m_szProvId,m_szProvCode,strlen(m_szProvId))==0)
            return ANA_ROAM_TYPE_PROVROAM;
        else
            return ANA_ROAM_TYPE_NATIONALROAMIN;
    }
    return ANA_ROAM_TYPE_NOTROAM;
    
}  

/*!
 * \func GetPstnSwitchInfo
 *
 * \brief 由交换机代码得到该交换机信息
 *
 */
int GetGsmMscInfo(xc::CSnapshot & snapShot, 
            char       *p_pchSwitchId,
            GsmMscInfo *p_SSwitchInfo)
{
    if ((p_pchSwitchId == NULL) ||
        (p_SSwitchInfo == NULL) ||
        (strlen(p_pchSwitchId) == 0))
    {
        return ANA_ERROR;
    }

    memset(p_SSwitchInfo, 0, sizeof(p_SSwitchInfo));
    CBpsGsmMsc cGsmMscData;

    if (0 == m_ODACClient.findGsmMsc(snapShot,p_pchSwitchId,
                cGsmMscData,
                0))
    {
        strcpy(p_SSwitchInfo->m_szSwitchId, p_pchSwitchId);
        strcpy(p_SSwitchInfo->m_szProvCode,
                    cGsmMscData.get_provCode());
        strcpy(p_SSwitchInfo->m_szAreaCode,
                    cGsmMscData.get_areaCode());
    strcpy(p_SSwitchInfo->m_szSwitchType,
            cGsmMscData.get_mscType());
        strcpy(p_SSwitchInfo->m_szBureauCode,
                    cGsmMscData.get_bureauCode());
        p_SSwitchInfo->m_iLocationType =
                    cGsmMscData.get_locationType();
        p_SSwitchInfo->m_iSwitchSeq =
                    cGsmMscData.get_mscSeq();

        return ANA_SUCCESS;
    }else
    {
    memset(p_SSwitchInfo->m_szSwitchId,0,sizeof(p_SSwitchInfo->m_szSwitchId));
    memset(p_SSwitchInfo->m_szProvCode,0,sizeof(p_SSwitchInfo->m_szProvCode));
    memset(p_SSwitchInfo->m_szAreaCode,0,sizeof(p_SSwitchInfo->m_szAreaCode));
    memset(p_SSwitchInfo->m_szBureauCode,0,sizeof(p_SSwitchInfo->m_szBureauCode));
        p_SSwitchInfo->m_iLocationType = 0;
        p_SSwitchInfo->m_iSwitchSeq = 0;
    }

    return ANA_ERROR;
}

/*!
 * \func GetGsmMscInfoNew
 *
 * \brief 由交换机代码得到该交换机信息
 * hujie add parameter: p_pchDateTime
 */
int GetGsmMscInfoNew(xc::CSnapshot & snapShot, 
            const char    *p_pchSwitchId,
            int64 iDateTime,
            GsmMscInfo *p_SSwitchInfo)
{
    if ((p_pchSwitchId == NULL) ||
        (p_SSwitchInfo == NULL) ||
        (strlen(p_pchSwitchId) == 0))
    {
        return ANA_ERROR;
    }

    memset(p_SSwitchInfo, 0, sizeof(p_SSwitchInfo));
        CBpsGsmMsc cGsmMscData;
        
    if (0 == m_ODACClient.findGsmMsc(snapShot,p_pchSwitchId,
                cGsmMscData,
                iDateTime))
    {
        strcpy(p_SSwitchInfo->m_szSwitchId, p_pchSwitchId);
        strcpy(p_SSwitchInfo->m_szProvCode,
                    cGsmMscData.get_provCode());
        strcpy(p_SSwitchInfo->m_szAreaCode,
                    cGsmMscData.get_areaCode());
        strcpy(p_SSwitchInfo->m_szBureauCode,
                    cGsmMscData.get_bureauCode());
        p_SSwitchInfo->m_iLocationType =
                    cGsmMscData.get_locationType();
        p_SSwitchInfo->m_iSwitchSeq =
                    cGsmMscData.get_mscSeq();

        return ANA_SUCCESS;
    }

    return ANA_ERROR;
}

/*!
 * \func GetPstnRouterInfo
 *
 * \brief 由交换机代码、中继得到入中继和出中继信息
 *
 */
int GetGsmRouterInfo(xc::CSnapshot & snapShot, 
            const char    *p_pchSwitchId,
            const char    *p_pchTrunkId,
            int64 iDateTime,
            GsmRouterInfo *p_SRouterInfo)
{
    if ((p_pchSwitchId == NULL) ||
        (p_pchTrunkId == NULL) ||
        (iDateTime == 0) ||
        (p_SRouterInfo == NULL) ||
        (strlen(p_pchSwitchId) == 0) ||
        (strlen(p_pchTrunkId) == 0))
    {
        return ANA_ERROR;
    }

    memset(p_SRouterInfo, 0, sizeof(p_SRouterInfo));
    CBpsGsmRouter cBpsGsmRouterData;

    if (0 == m_ODACClient.findGsmRouter(snapShot,p_pchSwitchId,
                p_pchTrunkId,
                cBpsGsmRouterData,iDateTime))
    {
        p_SRouterInfo->m_bGetInfo = 1;
        strcpy(p_SRouterInfo->m_szSwitchId, p_pchSwitchId);
        strcpy(p_SRouterInfo->m_szTrunkId, p_pchTrunkId);
        p_SRouterInfo->m_iInTrunkBusiId = 
                    cBpsGsmRouterData.get_inTrunkBusiId();
        p_SRouterInfo->m_iOutTrunkBusiId = 
                    cBpsGsmRouterData.get_outTrunkBusiId();
        p_SRouterInfo->m_iSettlerId = 
                    cBpsGsmRouterData.get_settlerId();
        p_SRouterInfo->m_iTollType = 
                    cBpsGsmRouterData.get_tollType();
        strcpy(p_SRouterInfo->m_szAreaCode,
                    cBpsGsmRouterData.get_areaCode());
        /*strcpy(p_SRouterInfo->m_szValidDate, 
                    cBpsGsmRouterData.get_ValidDate());
        strcpy(p_SRouterInfo->m_szExpireDate,
                    cBpsGsmRouterData.get_ExpireDate());*/
        /*p_SPstnRouterInfo->m_iSpecialFlag =
                    m_BpsPstnRouterData.getSpecialFlag();
        strcpy(p_SPstnRouterInfo->m_szUserNumber,
                    m_BpsPstnRouterData.getUserNumber());*/
    
        return ANA_SUCCESS;
    }
    else
    {
        p_SRouterInfo->m_bGetInfo = 0;
/*
#ifndef __UNKNOWN_ROUTER_BY_MONTH__
        addUnknownGsmRouter(p_pchSwitchId,p_pchTrunkId);
#else
        //未知中断按月记录到BPS_ADD_UNKNOWN_GSM_ROUTER
        addUnknownGsmRouterByMonth(p_pchSwitchId,p_pchTrunkId);
#endif*/

        return ANA_ERROR;
    }   
}

/*!
 * \func GetPstnRouterInfoNew
 *  if no SwitchId, please set "0" to p_pchSwitchId
 * \brief 由交换机代码、中继得到入中继和出中继信息
 *
 */
int GetGsmRouterInfoNew(xc::CSnapshot & snapShot, 
            const char    *p_pchSwitchId,
            const char    *p_pchTrunkId,
            const char    *p_pchAreaCode,
            int64   iDateTime,
            GsmRouterInfo *p_SRouterInfo)
{
        if ((p_pchSwitchId == NULL) ||
                (p_pchTrunkId == NULL) ||
        (p_pchAreaCode == NULL) ||
                (p_SRouterInfo == NULL) ||
                (strlen(p_pchSwitchId) == 0) ||
                (strlen(p_pchTrunkId) == 0) ||
        (strlen(p_pchAreaCode) == 0))
        {
                return ANA_ERROR;
        }

        memset(p_SRouterInfo, 0, sizeof(GsmRouterInfo));
        CBpsGsmRouter cBpsGsmRouterData;

        if (0 == m_ODACClient.findGsmRouterByAreaCode(snapShot,p_pchSwitchId,
                                p_pchTrunkId,
                                p_pchAreaCode,
                                cBpsGsmRouterData,iDateTime))
        {
                p_SRouterInfo->m_bGetInfo = 1;
                strcpy(p_SRouterInfo->m_szSwitchId, p_pchSwitchId);
                strcpy(p_SRouterInfo->m_szTrunkId, p_pchTrunkId);
                p_SRouterInfo->m_iInTrunkBusiId =
                                        cBpsGsmRouterData.get_inTrunkBusiId();
                p_SRouterInfo->m_iOutTrunkBusiId =
                                        cBpsGsmRouterData.get_outTrunkBusiId();
                p_SRouterInfo->m_iSettlerId =
                                        cBpsGsmRouterData.get_settlerId();
                p_SRouterInfo->m_iTollType =
                                        cBpsGsmRouterData.get_tollType();
                strcpy(p_SRouterInfo->m_szAreaCode,
                                        cBpsGsmRouterData.get_areaCode());
                /*strcpy(p_SRouterInfo->m_szValidDate,
                                        cBpsGsmRouterData.get_ValidDate());
                strcpy(p_SRouterInfo->m_szExpireDate,
                                        cBpsGsmRouterData.get_ExpireDate());*/
                /*p_SPstnRouterInfo->m_iSpecialFlag =
                                        m_BpsPstnRouterData.getSpecialFlag();
                strcpy(p_SPstnRouterInfo->m_szUserNumber,
                                        m_BpsPstnRouterData.getUserNumber());*/

                return ANA_SUCCESS;
        }
        else
        {
                p_SRouterInfo->m_bGetInfo = 0;
/*
#ifndef __UNKNOWN_ROUTER_BY_MONTH__
            addUnknownGsmRouter(p_pchSwitchId,p_pchTrunkId);
#else
            //未知中断按月记录到BPS_ADD_UNKNOWN_GSM_ROUTER
            addUnknownGsmRouterByMonth(p_pchSwitchId,p_pchTrunkId);
#endif
*/
                return ANA_ERROR;
        }
}


/*!
 * \func GetPstnRouterInfoNew
 *  if no SwitchId, please set "0" to p_pchSwitchId
 * \brief 由交换机代码、中继得到入中继和出中继信息
 *
 */
//hujie add: add p_pchTrunkFlag to judge in or out trunk
int GetGsmRouterInfoNewByTrunk(xc::CSnapshot & snapShot, 
            char    *p_pchSwitchId,
            char    *p_pchTrunkId,
            char    *p_pchTrunkFlag,
            char    *p_pchAreaCode,
            int64   iDateTime,
            GsmRouterInfo *p_SRouterInfo)
{
        if ((p_pchSwitchId == NULL) ||
                (p_pchTrunkId == NULL) ||
                (p_pchTrunkFlag == NULL )||
                (p_pchAreaCode == NULL) ||
                (p_SRouterInfo == NULL) ||
                (strlen(p_pchSwitchId) == 0) ||
                (strlen(p_pchTrunkId) == 0) ||
                (strlen(p_pchAreaCode) == 0))
        {
                return ANA_ERROR;
        }

        memset(p_SRouterInfo, 0, sizeof(GsmRouterInfo));
        CBpsGsmRouter cGsmRouterData;

        if (0 == m_ODACClient.findGsmRouterByTrunkFlag(snapShot,p_pchSwitchId,
                                p_pchTrunkId,
                                p_pchTrunkFlag,
                                p_pchAreaCode,
                                cGsmRouterData,iDateTime))
        {
                p_SRouterInfo->m_bGetInfo = 1;
                strcpy(p_SRouterInfo->m_szSwitchId, p_pchSwitchId);
                strcpy(p_SRouterInfo->m_szTrunkId, p_pchTrunkId);
                p_SRouterInfo->m_iInTrunkBusiId =
                                        cGsmRouterData.get_inTrunkBusiId();
                p_SRouterInfo->m_iOutTrunkBusiId =
                                        cGsmRouterData.get_outTrunkBusiId();
                p_SRouterInfo->m_iSettlerId =
                                        cGsmRouterData.get_settlerId();
                p_SRouterInfo->m_iTollType =
                                        cGsmRouterData.get_tollType();
                strcpy(p_SRouterInfo->m_szAreaCode,
                                        cGsmRouterData.get_areaCode());
                /*strcpy(p_SRouterInfo->m_szValidDate,
                                        cGsmRouterData.get_ValidDate());
                strcpy(p_SRouterInfo->m_szExpireDate,
                                        cGsmRouterData.get_ExpireDate());*/
                /*p_SPstnRouterInfo->m_iSpecialFlag =
                                        m_BpsPstnRouterData.getSpecialFlag();
                strcpy(p_SPstnRouterInfo->m_szUserNumber,
                                        m_BpsPstnRouterData.getUserNumber());*/

                return ANA_SUCCESS;
        }
        else
        {
                p_SRouterInfo->m_bGetInfo = 0;
 /*               
#ifndef __UNKNOWN_ROUTER_BY_MONTH__
            addUnknownGsmRouter(p_pchSwitchId,p_pchTrunkId);
#else
            //未知中断按月记录到BPS_ADD_UNKNOWN_GSM_ROUTER
            addUnknownGsmRouterByMonth(p_pchSwitchId,p_pchTrunkId);
#endif
*/

                return ANA_ERROR;
        }
}

int GetRoamAreaCode(xc::CSnapshot & snapShot,
            char *p_pMsrnCode,
            char *p_pAreaCode)
{
    if (p_pMsrnCode == NULL || strlen(p_pMsrnCode) == 0) {
        //wanghl 20130603 add
        *p_pAreaCode = 0;
        return ANA_ERROR;
    }
        
    CBpsAddMmmCode cMmmCodeData;
    
    if(0 == m_ODACClient.findMmmCode(snapShot,p_pMsrnCode,
                   cMmmCodeData,
                   0) )
    {
        strcpy(p_pAreaCode,cMmmCodeData.get_areaCode());
        return ANA_SUCCESS;
    }
    *p_pAreaCode = 0;
    return ANA_ERROR;
}

//hujie add 
int GetRoamAreaCodeNew(xc::CSnapshot & snapShot,
            const char *p_pMsrnCode,
            int64 iDateTime,
             char *p_pAreaCode)
{
    if(p_pMsrnCode == NULL||strlen(p_pMsrnCode)==0) {
         *p_pAreaCode = 0;
        return ANA_ERROR;
    }
    
    CBpsAddMmmCode cMmmCodeData;
    
    if(0 == m_ODACClient.findMmmCode(snapShot,p_pMsrnCode,
                   cMmmCodeData,
                   iDateTime) )
    {
        strcpy(p_pAreaCode,cMmmCodeData.get_areaCode());
        return ANA_SUCCESS;
    }
    *p_pAreaCode = 0;
    return ANA_ERROR;
}

int SMSNumberAnalysis(xc::CSnapshot & snapShot,
            const char    *p_pchNumber,
            const char    *p_pArea,
            SMSNumberInfo *p_pSNumber)
{
    //短信号码分析首先判断是否是固定电话，如果是，则去掉1060，然后判断区号，在固定电话号段表中查找运营商等信息，如果找不到，按照南电北网的规则判断运营商
    //如果不是固定电话，则在HLR表中判断其运营商信息。
        int m_nDefaultOperId = 0;
        const int nNumberLen = 32;
        const char szCNCHead[4]="106";
            //const char szWLWHead[6] = "10648";/*物联网号头*/
        const char szWLWHead[5] = "1064";/*物联网号头*/
        const int l_mhead_len = 2;
        
        char l_szNumber[32];
        char l_szHlr[9+1];
        int  l_excursion;
        
        int  l_len;
        
        
        bzero(l_szNumber,nNumberLen); 
        l_len = strlen(p_pchNumber);
        
        //memset(p_pSNumber, 0, sizeof(NumberInfo));
        if(l_len == 0 ||p_pchNumber == NULL)
        {
            p_pSNumber->m_iNumberType = ANA_NUMBER_ERROR; 
            return ANA_SUCCESS;   
        }   
        if(l_len > nNumberLen)
            l_len = nNumberLen - 1;
        strncpy(l_szNumber,p_pchNumber,l_len);
        
        p_pSNumber->m_iNumberType = ANA_NUMBER_COMM;
        l_excursion = 0;
        //如果不是物联网号头并且满足前几位是106开头
        if(memcmp(l_szNumber + l_excursion ,szCNCHead,strlen(szCNCHead))==0  && memcmp(l_szNumber + l_excursion, szWLWHead, strlen( szWLWHead ))  != 0)
        {
            l_excursion +=(strlen(szCNCHead) + 1);  //去掉106 0
        }
        bzero(l_szHlr, sizeof(l_szHlr));
        memcpy(l_szHlr, l_szNumber + l_excursion , 9);  
        
        CBpsAddMobileSeg  cMobileSegData;
        CBpsHlr           cHlrData;
        CSysCity          cCityData;
        
    //判断运营商   
        if(0 == m_ODACClient.findMobileSeg(snapShot,l_szHlr ,cMobileSegData,0))
        {
                if (0 == m_ODACClient.findHlr(snapShot,l_szHlr, cHlrData,
                            0))
                {
                        p_pSNumber->m_iNetWork = cHlrData.get_netType();
                        p_pSNumber->m_iOperatorParty = cHlrData.get_operatorId();
                        strcpy(p_pSNumber->m_szHomeAreaCode,cHlrData.get_areaCode());
                }   
                else            
                {
                        p_pSNumber->m_iNetWork = cMobileSegData.get_netType();
                        p_pSNumber->m_iOperatorParty = cMobileSegData.get_operatorId();
                        strncpy(p_pSNumber->m_szHomeAreaCode,p_pArea,sizeof(p_pSNumber->m_szHomeAreaCode)-1);
                }       
        }
        else  //电信
        {
                if(0 != m_ODACClient.findCityByAreaCode(snapShot,l_szNumber + l_excursion,cCityData,0))
                {
                        strncpy(p_pSNumber->m_szHomeAreaCode,p_pArea,sizeof(p_pSNumber->m_szHomeAreaCode)-1);
                }
                else
                        strcpy(p_pSNumber->m_szHomeAreaCode,cCityData.get_areaCode());
                l_excursion += strlen(p_pSNumber->m_szHomeAreaCode);
                p_pSNumber->m_iNetWork = ANA_NET_TYPE_PHS;
                
              p_pSNumber->m_iOperatorParty =m_nDefaultOperId;
        }                   
                
        return ANA_SUCCESS;
}

//hujie add: SMSNumberAnalysisNew
int SMSNumberAnalysisNew(xc::CSnapshot & snapShot, 
            const char    *p_pchNumber,
            const char    *p_pArea,
            const int    iStartTime,
            SMSNumberInfo *p_pSNumber)
{
    //短信号码分析首先判断是否是固定电话，如果是，则去掉1060，然后判断区号，在固定电话号段表中查找运营商等信息，如果找不到，按照南电北网的规则判断运营商
    //如果不是固定电话，则在HLR表中判断其运营商信息。
    REPORT_TRACE("ANASYS SMSNumberAnalysisNew IN p_pchNumber=<%s>",p_pchNumber);
    REPORT_TRACE("ANASYS SMSNumberAnalysisNew IN p_pArea=<%s>",p_pArea);
    REPORT_TRACE("ANASYS SMSNumberAnalysisNew IN iStartTime=<%ld>",iStartTime);
    const int nNumberLen = 32;
    const char szCNCHead[4]="106";
    //const char szWLWHead[6] = "10648";/*物联网号头*/
    const char szWLWHead[5] = "1064";/*物联网号头*/
    const int l_mhead_len = 2;
    
    char l_szNumber[32];
    char l_szHlr[9+1];
    int  l_excursion;        
    int  l_len;
    bzero(l_szNumber,nNumberLen); 
    l_len = strlen(p_pchNumber);  
    if(l_len == 0 ||p_pchNumber == NULL)
    {
        p_pSNumber->m_iNumberType = ANA_NUMBER_ERROR;   
         return ANA_SUCCESS;   
    }   
    if(l_len > nNumberLen)
        l_len = nNumberLen - 1;
    strncpy(l_szNumber,p_pchNumber,l_len);
    
    p_pSNumber->m_iNumberType = ANA_NUMBER_COMM;
    l_excursion = 0;
    
    if(memcmp(l_szNumber + l_excursion ,szCNCHead,strlen(szCNCHead))==0 && memcmp(l_szNumber + l_excursion, szWLWHead, strlen( szWLWHead ))  != 0)
    {
        l_excursion +=(strlen(szCNCHead) + 1);  //去掉106 0
    }
    bzero(l_szHlr, sizeof(l_szHlr));
    memcpy(l_szHlr, l_szNumber + l_excursion , 9);  
    
    CBpsAddMobileSeg  cMobileSegData;
    CBpsHlr           cHlrData;
    CSysCity          cCityData;
        
    //判断运营商    
    //int32 odac_app_lookup_CBpsAddMobileSeg(const xc::CSnapshot& cSnapShot,const char * cszMCodeSeg,const time_t& tm,    CBpsAddMobileSeg &cVal);
    if(0==odac_app_lookup_CBpsAddMobileSeg(snapShot,p_pchNumber,iStartTime,cMobileSegData))
    {
//odac_app_lookup_CBpsHlr(const xc::CSnapshot& cSnapShot,const char * cszHlrCode,const time_t& tm,CBpsHlr &cVal)
        if (0 == odac_app_lookup_CBpsHlr(snapShot,l_szHlr,iStartTime,cHlrData))
        {
            p_pSNumber->m_iNetWork = cHlrData.get_netType();
            p_pSNumber->m_iOperatorParty = cHlrData.get_operatorId();
            strcpy(p_pSNumber->m_szHomeAreaCode,cHlrData.get_areaCode());
        }   
        else            
        {
                    p_pSNumber->m_iNetWork = cMobileSegData.get_netType();
                    p_pSNumber->m_iOperatorParty = cMobileSegData.get_operatorId();
                    strncpy(p_pSNumber->m_szHomeAreaCode,p_pArea,sizeof(p_pSNumber->m_szHomeAreaCode)-1);
        }       
    }
    else  //电信
    {
        if(0 != m_ODACClient.findCityByAreaCode(snapShot,l_szNumber + l_excursion,cCityData,iStartTime))
        {
            strncpy(p_pSNumber->m_szHomeAreaCode,p_pArea,sizeof(p_pSNumber->m_szHomeAreaCode)-1);
            }
            else
                    strcpy(p_pSNumber->m_szHomeAreaCode,cCityData.get_areaCode());
            l_excursion += strlen(p_pSNumber->m_szHomeAreaCode);
            p_pSNumber->m_iNetWork = ANA_NET_TYPE_PHS;
            //p_pSNumber->m_iOperatorParty =m_nDefaultOperId;
            p_pSNumber->m_iOperatorParty =571;
    }                   
    return ANA_SUCCESS;
}
//

int  SPNumberAnalysis(xc::CSnapshot & snapShot,
            const char *p_pchNumber,
            int64 iDateTime,
            SPNumberInfo *p_pSNumber)
{
    //SP号码分析直接在bps_add_sp_busi_desc中查找busi_type为0的SP
    
        const int nNumberLen = 25;
        char l_szNumber[25];
        int  l_len, l_excursion;
        
        bzero(l_szNumber,nNumberLen); 
        l_len = strlen(p_pchNumber);
        
        if(l_len == 0 ||p_pchNumber == NULL)
        {
            return ANA_ERROR;
        }   
        if(l_len > nNumberLen)
            l_len = nNumberLen - 1;
        strncpy(l_szNumber,p_pchNumber,l_len);
        
        p_pSNumber->m_iNumberType = ANA_NUMBER_COMM;
        l_excursion = 0;
        
      CBpsAddSpBusiDesc cSpBusiDescData; 
    
      if(0 != m_ODACClient.findSpBusiDescByServCode2(snapShot,l_szNumber+l_excursion, 
                                                                  0,
                                                                cSpBusiDescData,iDateTime))
      {
            p_pSNumber->m_iNumberType = ANA_NUMBER_SKIP;
            return ANA_SUCCESS;
      }
      else
      {
         p_pSNumber->m_iOperId = cSpBusiDescData.get_operId();
         strcpy(p_pSNumber->m_szSPCode, cSpBusiDescData.get_spCode());
         strcpy(p_pSNumber->m_szSPName, cSpBusiDescData.get_spName());
         strcpy(p_pSNumber->m_szServCode1,  cSpBusiDescData.get_servCode1());             
         strcpy(p_pSNumber->m_szServCode2,  cSpBusiDescData.get_servCode2()); 
         strcpy(p_pSNumber->m_szBusiCode,  cSpBusiDescData.get_busiCode()); 
         strcpy(p_pSNumber->m_szAccArea, cSpBusiDescData.get_accArea());
         p_pSNumber->m_iPalType = cSpBusiDescData.get_palType();
         p_pSNumber->m_iServRange = cSpBusiDescData.get_servRange();       
         p_pSNumber->m_iBusiType = cSpBusiDescData.get_busiType();
      }
      return ANA_SUCCESS;                                                                           
}   

//zhoushang add --for Bsms
int  ServCodeAnalysisNew(xc::CSnapshot & snapShot,
            const char *pszServType,
            const char *p_pchNumber,
            int64 iDateTime,
            const int32 iMacthType,
            BsmsInfo* p_pSNumber)
{

        const int nNumberLen = 25;
        char l_szNumber[25];
        int  l_len, l_excursion;
        bzero(l_szNumber,nNumberLen);
        l_len = strlen(p_pchNumber);

        if(l_len == 0 ||p_pchNumber == NULL)
        {
            return ANA_ERROR;
        }
        if(l_len > nNumberLen)
            l_len = nNumberLen - 1;
        strncpy(l_szNumber,p_pchNumber,l_len);

    //added by gai
    memset(p_pSNumber, 0, sizeof(BsmsInfo));

        p_pSNumber->m_iNumberType = ANA_NUMBER_COMM;
        l_excursion = 0;

        CBpsBsmsServicecode cBsmsServCodeData;

        if(0 != m_ODACClient.findBsmsServCode(snapShot,pszServType,
                                        l_szNumber+l_excursion,
                                        iMacthType,
                                        cBsmsServCodeData,iDateTime))
        {
                p_pSNumber->m_iNumberType = ANA_NUMBER_SKIP;
        //added by gai
        strcpy(p_pSNumber->m_szProcCode,"0");
        strcpy(p_pSNumber->m_szServType,"0");
        strcpy(p_pSNumber->m_szServCode,"0");
                return ANA_SUCCESS;
        }
        else
        {
                 strcpy(p_pSNumber->m_szServType, cBsmsServCodeData.get_servType());
                 strcpy(p_pSNumber->m_szServCode, cBsmsServCodeData.get_servCode());
                 strcpy(p_pSNumber->m_szProcCode, cBsmsServCodeData.get_provCode());
                strcpy(p_pSNumber->isBalPort, cBsmsServCodeData.get_isBalPort());
                p_pSNumber->settlementPrice1 = cBsmsServCodeData.get_settlementPrice1();
                p_pSNumber->settlementPrice2 = cBsmsServCodeData.get_settlementPrice2();
                p_pSNumber->settlementPrice3 = cBsmsServCodeData.get_settlementPrice3();
                p_pSNumber->settlementPrice4 = cBsmsServCodeData.get_settlementPrice4();
                p_pSNumber->settlementPrice5 = cBsmsServCodeData.get_settlementPrice5();
        }
      return ANA_SUCCESS;
}
  
int  SPNumberSpAnalysis(xc::CSnapshot & snapShot,
            const char   *p_pchNumber,
            int64 iDateTime,
            SPNumberInfo *p_pSNumber)
{
    
    
        const int nNumberLen = 25;
        char l_szNumber[25];
        int  l_len, l_excursion;
        
        bzero(l_szNumber,nNumberLen); 
        l_len = strlen(p_pchNumber);
        
        if(l_len == 0 ||p_pchNumber == NULL)
        {
            return ANA_ERROR;
        }   
        if(l_len > nNumberLen)
            l_len = nNumberLen - 1;
        strncpy(l_szNumber,p_pchNumber,l_len);
        
        p_pSNumber->m_iNumberType = ANA_NUMBER_COMM;
        l_excursion = 0;
    
        CBpsAddSpBusiDesc cSpBusiDescData; 
        
      if(0 != m_ODACClient.findSpBusiDescBySpCode(snapShot,l_szNumber+l_excursion, 
                                                                  -99999,
                                                                cSpBusiDescData,iDateTime))
      {
            p_pSNumber->m_iNumberType = ANA_NUMBER_SKIP;
            return ANA_SUCCESS;
      }
      else
      {
         p_pSNumber->m_iOperId = cSpBusiDescData.get_operId();
         strcpy(p_pSNumber->m_szSPCode, cSpBusiDescData.get_spCode());
         strcpy(p_pSNumber->m_szSPName, cSpBusiDescData.get_spName());
         strcpy(p_pSNumber->m_szServCode1,  cSpBusiDescData.get_servCode1());             
         strcpy(p_pSNumber->m_szServCode2,  cSpBusiDescData.get_servCode2()); 
         strcpy(p_pSNumber->m_szBusiCode,  cSpBusiDescData.get_busiCode()); 
         strcpy(p_pSNumber->m_szAccArea, cSpBusiDescData.get_accArea());
         p_pSNumber->m_iPalType = cSpBusiDescData.get_palType();
         p_pSNumber->m_iServRange = cSpBusiDescData.get_servRange();        
         p_pSNumber->m_iBusiType = cSpBusiDescData.get_busiType();             
      }
      return ANA_SUCCESS;                                                                           
}  

//add by tangyf fo jx:根据serv_code2得到SP_CODE,要求以失效时间最大的SP代码为准
int  SPNumberServCodeAnalysis(xc::CSnapshot & snapShot,
            const char *p_pchNumber,
            int64 iDateTime,
            SPNumberInfo *p_pSNumber)
{
        const int nNumberLen = 25;
        char l_szNumber[25];
        int  l_len, l_excursion;
        
        bzero(l_szNumber,nNumberLen); 
        l_len = strlen(p_pchNumber);
        
        if(l_len == 0 ||p_pchNumber == NULL)
        {
            return ANA_ERROR;
        }   
        if(l_len > nNumberLen)
            l_len = nNumberLen - 1;
        strncpy(l_szNumber,p_pchNumber,l_len);
        
        p_pSNumber->m_iNumberType = ANA_NUMBER_COMM;
        l_excursion = 0;
        
      CBpsAddSpBusiDesc cSpBusiDescData;
    
      if(0 != m_ODACClient.findSpBusiDescByServCode2Ex(snapShot,l_szNumber+l_excursion, 
                                               NULL,
                                               cSpBusiDescData,iDateTime))
      {
            p_pSNumber->m_iNumberType = ANA_NUMBER_SKIP;
            return ANA_SUCCESS;
      }
      else
      {
         p_pSNumber->m_iOperId = cSpBusiDescData.get_operId();
         strcpy(p_pSNumber->m_szSPCode, cSpBusiDescData.get_spCode());
         strcpy(p_pSNumber->m_szSPName, cSpBusiDescData.get_spName());
         strcpy(p_pSNumber->m_szServCode1,  cSpBusiDescData.get_servCode1());             
         strcpy(p_pSNumber->m_szServCode2,  cSpBusiDescData.get_servCode2()); 
         strcpy(p_pSNumber->m_szAccArea, cSpBusiDescData.get_accArea());
         p_pSNumber->m_iPalType = cSpBusiDescData.get_palType();
         p_pSNumber->m_iServRange = cSpBusiDescData.get_servRange();       
         p_pSNumber->m_iBusiType = cSpBusiDescData.get_busiType();
      }
      return ANA_SUCCESS;                                                                           
}   

int AccCodeAnalysis(xc::CSnapshot & snapShot, 
            const int32  iAccCode, 
            const char   *p_pchOperCode,
            PlatformInfo *p_pSNumber)
{
    CBpsAddPlatformBusiDesc cPlatformBusiDescData;
    
#ifdef __GANSU__
      if(0 != m_ODACClient.findPlatformBusiDesc.find(snapShot,iAccCode ,
                          NULL
                        cPlatformBusiDescData,0))
    {
#else
    typedef pair<int32, string> KeyType;

      
    //KeyType key1 = KeyType(iAccCode, string("NULL"));   
    //KeyType key2 = KeyType(iAccCode, string(p_pchOperCode));

    if(0 != m_ODACClient.findPlatformBusiDesc(snapShot,iAccCode ,
                                          "NULL",
                                        cPlatformBusiDescData,0))
    {
//printf("ServiceAnalysis::AccCodeAnalysis -> 11\n");
        if(0 != m_ODACClient.findPlatformBusiDesc(snapShot,iAccCode ,
                                          p_pchOperCode,
                                        cPlatformBusiDescData,0))
#endif                                      
        {
//printf("ServiceAnalysis::AccCodeAnalysis -> 22\n");
            p_pSNumber->m_bFind = 0;
            return ANA_SUCCESS;
        }
    }
//printf("ServiceAnalysis::AccCodeAnalysis -> 33\n");

    p_pSNumber->m_bFind = 1;
    p_pSNumber->m_iOperId = cPlatformBusiDescData.get_operId();
    p_pSNumber->m_iAccCode = iAccCode;
    p_pSNumber->m_iPalType = cPlatformBusiDescData.get_palType();
    p_pSNumber->m_iServRange = cPlatformBusiDescData.get_servRange();
    strcpy(p_pSNumber->m_szBusiName, cPlatformBusiDescData.get_busiName());

    return ANA_SUCCESS;                                                                         
}   

int OperCodeAnalysis(xc::CSnapshot & snapShot, 
            const char    *p_pchNumber,
            int64 iDateTime,
            AddOperatorInfo *p_pSNumber)
{
            CBpsAddOperator cOperatorData;
            
            if(0 != m_ODACClient.findOperator(snapShot,p_pchNumber,
                                                                       NULL,
                                                                     cOperatorData,iDateTime))
            {
                        p_pSNumber->m_bFind = 0;
            }           
      else
      {
                p_pSNumber->m_bFind = 1;
                p_pSNumber->m_iOperId = cOperatorData.get_operId();
                p_pSNumber->m_iOperType = cOperatorData.get_operType();
                strncpy(p_pSNumber->m_szOperCode,cOperatorData.get_operCode(),sizeof(p_pSNumber->m_szOperCode));
                strncpy(p_pSNumber->m_szOperName,cOperatorData.get_operName(),sizeof(p_pSNumber->m_szOperName));
                strncpy(p_pSNumber->m_szHomeArea,cOperatorData.get_homeArea(),sizeof(p_pSNumber->m_szHomeArea));
                p_pSNumber->m_iSts = cOperatorData.get_sts();
                strncpy(p_pSNumber->m_szStsTime,cOperatorData.get_stsTime(),sizeof(p_pSNumber->m_szStsTime));
        p_pSNumber->m_iSpcOperSeg=cOperatorData.get_spcOperSeg();
      }     
      return ANA_SUCCESS;       
}   

/**************************************************************************/
/* 函数: OperCodeMCCAnalysis 
/* 用途: 根据运营商代码和国家代码返回
         运营商信息
/* 参数: p_opercode:--输入  运营商代码
/*       p_homearea:--输入  国家代码
/*       p_pSNumber: --输出 运营商信息
/* 返回: ANA_SUCCESS 成功
/* 说明:
/**************************************************************************/
int  OperCodeMCCAnalysis(xc::CSnapshot & snapShot, 
            const char    *p_opercode,
            const char      *p_homearea,
            int64   iStartTime, 
            AddOperatorInfo *p_pSNumber
)
{
    CBpsAddOperator cOperatorData;
    
    if(0 != m_ODACClient.findOperator(snapShot,p_opercode,
                    p_homearea,    
                    cOperatorData,iStartTime))
    {
        p_pSNumber->m_bFind = 0;
    }
    else
    {
        p_pSNumber->m_bFind = 1;
        p_pSNumber->m_iOperId = cOperatorData.get_operId();
        p_pSNumber->m_iOperType = cOperatorData.get_operType();
        strncpy(p_pSNumber->m_szOperCode,cOperatorData.get_operCode(),sizeof(p_pSNumber->m_szOperCode));
        strncpy(p_pSNumber->m_szOperName,cOperatorData.get_operName(),sizeof(p_pSNumber->m_szOperName));
        p_pSNumber->m_iSts = cOperatorData.get_sts();
        strncpy(p_pSNumber->m_szStsTime,cOperatorData.get_stsTime(),sizeof(p_pSNumber->m_szStsTime));
        p_pSNumber->m_iSpcOperSeg=cOperatorData.get_spcOperSeg();
    }
    return ANA_SUCCESS;
} 

int IsmgCodeAnalysis(xc::CSnapshot & snapShot,
            const char *p_pchNumber,
            char       *pAreaCode)
{
    CBpsIsmg   cIsmgData;
    
            if(0 == m_ODACClient.findIsmg(snapShot,p_pchNumber,
                                                        cIsmgData,0))
            {
                        strcpy(pAreaCode, cIsmgData.get_areaCode());
            }
            else
                        strcpy(pAreaCode, "");
            return ANA_SUCCESS;
}

int VisitInfoAnalysis(xc::CSnapshot & snapShot,
            const char      *p_pchNumber,
            int64 iDateTime,
            AddOperatorInfo *p_pSNumber)
{
    
            char szProvCode[5];
            CSysCity cCityData;
            
            if(0 != m_ODACClient.findCityByAreaCode(snapShot,p_pchNumber,
                                                        cCityData,iDateTime))
            {
                        strcpy(szProvCode,p_pchNumber);
            }
            else
                        strncpy(szProvCode,cCityData.get_provCode(),sizeof(szProvCode));
            OperCodeAnalysis(snapShot,szProvCode,iDateTime,p_pSNumber);
      return ANA_SUCCESS;           
}

int ImsiAreaCodeAnalysis(xc::CSnapshot & snapShot,
            const char *p_pchImsiNumber,
            char *szAreaCode)
{
        char szHlrCode[8];
        
        bzero(szHlrCode,8);
        if(0 == m_ODACClient.findHlrByImsi(snapShot,p_pchImsiNumber,szHlrCode, 0))
        {
                CBpsHlr cHlrData;
            if (0 == m_ODACClient.findHlr(snapShot,szHlrCode, cHlrData,
                                0))
            {
                            strcpy(szAreaCode,cHlrData.get_areaCode());
                            return 0;
            }       
            else
            {
                            strcpy(szAreaCode,"0");
                            return -1;
            }               
        }
        else
        {
            strcpy(szAreaCode,"0");
            return -1;
        }
         return ANA_ERROR;
}   

//hujie add: ImsiAreaCodeAnalysisNew 
int ImsiAreaCodeAnalysisNew(xc::CSnapshot & snapShot,
            const char  *p_pchImsiNumber,
            int64 iDateTime,
            char        *szAreaCode)
{
        char szHlrCode[8];
        
        bzero(szHlrCode,8);
        if(0 == m_ODACClient.findHlrByImsi(snapShot,p_pchImsiNumber,szHlrCode, iDateTime))
        {
            CBpsHlr cHlrData;
            if (0 == m_ODACClient.findHlr(snapShot,szHlrCode,cHlrData,
                                iDateTime))
            {
                            strcpy(szAreaCode,cHlrData.get_areaCode());
                            return 0;
            }       
            else
            {
                            strcpy(szAreaCode,"0");
                            return -1;
            }      
        }
        else
        {
            strcpy(szAreaCode,"0");
            return -1;
        }
        return ANA_ERROR;         
}//

int GetMscIdFromFileName(xc::CSnapshot & snapShot,
            const char *p_pchFileName,
            char *p_pchMscId)
{
        CBpsAddFilenametomsc cFilenametomscData;
        
    if(0 == m_ODACClient.findMscByFileName(snapShot,p_pchFileName,
        cFilenametomscData,0))
    {
        strcpy(p_pchMscId,cFilenametomscData.get_mscId());
    }
    else
        strcpy(p_pchMscId,"");
        
    return ANA_SUCCESS; 
} 

int FindGprsIpaddr(xc::CSnapshot & snapShot,
            const char *p_pchProvCode,
            const char *p_pchIpaddr,
            int64 iDateTime)
{
    if(0 == m_ODACClient.findGprsIpaddrInfo(snapShot,p_pchProvCode,p_pchIpaddr,iDateTime))
        return 1;
    else
        return  0;
}

int SpecialUserAnalysis(xc::CSnapshot & snapShot,
            const char *p_pchUserNumber, 
            const char *p_pchAreaCode, 
            int64 iDateTime, 
            SpecialUserInfo *p_pSNumber)
{
    if ((p_pchUserNumber == NULL) ||
        (p_pchAreaCode == NULL))
    {
        return ANA_ERROR;
    }

    //BpsSpecialUserMgr::KeyType key = 
        //m_BpsSpecialUserMgr.getKey(p_pchAreaCode, p_pchUserNumber);
    REPORT_TRACE("ANASYS SpecialUserAnalysis IN p_pchNumber=<%s>",p_pchUserNumber);
    REPORT_TRACE("ANASYS SpecialUserAnalysis IN p_pchNumber=<%s>",p_pchAreaCode);
    REPORT_TRACE("ANASYS SpecialUserAnalysis IN p_pchNumber=<%ld>",iDateTime);
    CBpsSpecialUser cSpecialUserData;
    
    if (0 == m_ODACClient.findSpecialUser(snapShot,p_pchAreaCode,
                  p_pchUserNumber,
                cSpecialUserData,iDateTime))
    {
        p_pSNumber->m_bGetInfo = 1;
        p_pSNumber->m_iNumberType = cSpecialUserData.get_numberType();
        p_pSNumber->m_iOperatorId = cSpecialUserData.get_operatorId();
        strcpy(p_pSNumber->m_szBureauCode,cSpecialUserData.get_bureauCode());    
        strcpy(p_pSNumber->m_szAreaCode,cSpecialUserData.get_areaCode());    
        return ANA_SUCCESS;
    }
    
    p_pSNumber->m_bGetInfo = 0;
    return ANA_SUCCESS;
}

//湖北专网，根据地区，局代码，号码查找，wanghl2 add on 20111205
int SpecialUserAnalysisSN(xc::CSnapshot & snapShot,
            const char *p_pchUserNumber, 
            const char *p_pchAreaCode, 
            const char *p_pchBureauCode, 
            int64 iDateTime, 
            SpecialUserInfo *p_pSNumber)
{
    if ((p_pchUserNumber == NULL) ||
        (p_pchAreaCode == NULL) ||
        (p_pchBureauCode == NULL) )
    {
        return ANA_ERROR;
    }
    
    CBpsSpecialNet cSpecialNetData;

    if (0 == m_ODACClient.findSpecialUserByBureauCode(snapShot,
                  p_pchAreaCode,
    	          p_pchBureauCode,
    	          p_pchUserNumber,
                  cSpecialNetData, iDateTime))
    {
        p_pSNumber->m_bGetInfo = 1;
        p_pSNumber->m_iNumberType = cSpecialNetData.get_numberType();
        p_pSNumber->m_iOperatorId = cSpecialNetData.get_operatorId();
		    strcpy(p_pSNumber->m_szBureauCode,cSpecialNetData.get_bureauCode());	
		    strcpy(p_pSNumber->m_szAreaCode,cSpecialNetData.get_areaCode());	
        return ANA_SUCCESS;
    }
    
    p_pSNumber->m_bGetInfo = 0;
    return ANA_SUCCESS;
}


int FindSpRatio(xc::CSnapshot & snapShot,
            const char     *p_pchSpCode,
            const char     *p_pchOperatorCode,  //业务代码
            const int      nType,
            int64 iDateTime,
            SSpSettleRatio *p_pRatioInfo)  
{                        
    p_pRatioInfo->nSpRatio = -1;
    p_pRatioInfo->nCMRatio = -1;
    p_pRatioInfo->nThirdRatio = -1;    
    p_pRatioInfo->nAccSettleId = 0;
    p_pRatioInfo->nChargeDir = 0;        
    //added by gaijy 20181218
  p_pRatioInfo->nSettleMode = -1;
  p_pRatioInfo->nSettleAmount = -1;        
    CVSpRatio cSpRatioData;    
            
    if (0 == m_ODACClient.findSpRatio(snapShot,p_pchSpCode, p_pchOperatorCode, nType, cSpRatioData,iDateTime) )
    {
        p_pRatioInfo->nSpRatio     = cSpRatioData.get_spRatio();
        p_pRatioInfo->nCMRatio     = cSpRatioData.get_cmRatio();
        p_pRatioInfo->nThirdRatio  = cSpRatioData.get_thirdRatio();
        p_pRatioInfo->nAccSettleId = cSpRatioData.get_accSettleId();
        p_pRatioInfo->nChargeDir   = cSpRatioData.get_chargeDir();
        //added by gaijy 20181218
    p_pRatioInfo->nSettleMode = cSpRatioData.get_settleMode();
    p_pRatioInfo->nSettleAmount = cSpRatioData.get_settleAmount();
        return 1;
    }        
            
    return -1;
}                      

int getProvByAreaCode(xc::CSnapshot & snapShot,
            const char* pchAreaCode,
            char* pchProv,int64   iStartTime) 
{
        CSysCity cCityData;

        if (0 == m_ODACClient.findCityByAreaCode(snapShot,pchAreaCode,cCityData,iStartTime) )
        {
                strcpy(pchProv, cCityData.get_provCode());
                return ANA_SUCCESS;
        } else {
                strcpy(pchProv, "");
                return ANA_ERROR;
        }
}
#ifdef __NP_QUERY__

int SplitIpPort(const char *szPort,const char *szIp)
{
  /*              string strPort(szPort);
                string strIp(szIp);
                int32 nPosPort = strPort.find('/');
                int32 nPosIp = strIp.find('/');
                if(nPosPort==string::npos && nPosIp==string::npos)//一个ip/port
                {
                                CSockConnStr sTemp;
                                sTemp.szIp = strIp;
                                sTemp.iPort = atoi(strPort.c_str());
                                m_versSockStr.push_back(sTemp);
                }
                else if((strPort.length()-1)>nPosPort && (strIp.length()-1)>nPosIp)
                {
                                CSockConnStr sTemp;
                                sTemp.szIp = strIp.substr(0,nPosIp);
                                sTemp.iPort = atoi((strPort.substr(0,nPosPort)).c_str());
                                m_versSockStr.push_back(sTemp);

                                sTemp.szIp = strIp.substr(nPosIp+1,strIp.length()-nPosIp-1);
                                sTemp.iPort = atoi((strPort.substr(nPosPort+1,strPort.length()-nPosPort-1)).c_str());
                                m_versSockStr.push_back(sTemp);
                }
                else
                {
                        return -1;
                }
    */
                return 0;
}
/*

//携号转网查询
int FindNpInfo(xc::CSnapshot & snapShot,const char *szNumber,int64 iDateTime,SNpInfo *p_pNpInfo)
{
                p_pNpInfo->nFlag = 0;
                if(strlen(szNumber)!=11)
                      return -1;
                AISTD string strRecTime(szDate);
                if (strRecTime.size() < 14)
                {
                        string strTemp("20000101000000");
                        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
                }
                time_t t_Time  = str2time(strRecTime);//其他省份查询时间
                int64 lTime = atol(strRecTime.c_str());//浙江查询时间
        int32 iNum=0;
            while(1)
            {
                m_qryProxy.setNotify(6101);
                m_qryProxy.setNotifyData(szNumber,lTime,lTime);
                
                vector<CNpDetail> vecNpDetail;
                int nRet = m_qryProxy.sendAndRecvNotify(vecNpDetail);
                if(nRet == 0)
                {
                    vector<CNpDetail>::iterator iter = vecNpDetail.begin();
                    for(;iter!=vecNpDetail.end();iter++)
                    {
                        p_pNpInfo->nFlag = 1;
                        strcpy(p_pNpInfo->szPhoneId , iter->m_szPhoneId);
                        string strTemp(iter->m_szDstNetId);
                        string strNet = strTemp.substr(3,1);
                        if(strNet == "1"||strNet == "2"||strNet == "5")
                                p_pNpInfo->nNetWork = 3;//CDMA
                        else if(strNet == "4")
                                p_pNpInfo->nNetWork = 8;//TD
                        else if(strNet == "3")
                        p_pNpInfo->nNetWork = 2;//GSM
                        else
                        p_pNpInfo->nNetWork = 0;//未知
                        p_pNpInfo->nOperatorParty = atoi((strTemp.substr(2,1)).c_str());

            			//网络类型无法确定，联通确认g网，电信默认为cdma，移动默认是g网
                        if(p_pNpInfo->nNetWork == 0)
                        {
                            if(p_pNpInfo->nOperatorParty == 1)
                                p_pNpInfo->nNetWork = 3;//CDMA
                            else
                                p_pNpInfo->nNetWork = 2;//GSM
                        }
                    }
                    break;
                }
                else
                {
                    iNum++;
                }
        if(iNum==3)
            exit(1);
            }
                return 0;
}
*/
#endif


int findBpsCardHomeProv(xc::CSnapshot & snapShot,
            const char* szCardCode,
            int64 iDateTime,
            char* szProv)
{
    strcpy(szProv,"");
    CBpsCardHomeProv cBpsCardHomeProv;
    if(0 == m_ODACClient.findBpsCardHomeProv(snapShot,szCardCode,cBpsCardHomeProv,iDateTime))
        strcpy(szProv,cBpsCardHomeProv.get_provCode());
    return ANA_SUCCESS;
}

int IvrRatioAnalysis(xc::CSnapshot & snapShot,
            const char *szOper,
            const char *szSpCode,
            int64 iDateTime,
            const char *szServ,
            const int32 ifee,
            const int32 ikey,
            SIvrRatio *pSIvrRatio)
{
    strcpy(pSIvrRatio->szServType,"");
    strcpy(pSIvrRatio->szOperCode,"");
    strcpy(pSIvrRatio->szOperCode,"");
    pSIvrRatio->nInProp=0;
    pSIvrRatio->nOurProp=0;
    pSIvrRatio->nSettleMode=0;
    pSIvrRatio->lSettleAmount=0;
    CVIvrRatio cIvrRatio;
    if (0 == m_ODACClient.findIvrRatio(snapShot,szOper,szSpCode,szServ,ifee,ikey,cIvrRatio,iDateTime))
    {
        strcpy(pSIvrRatio->szServType,cIvrRatio.get_servType());
        strcpy(pSIvrRatio->szSpCode,cIvrRatio.get_spCode());
        strcpy(pSIvrRatio->szOperCode,cIvrRatio.get_operCode());
        pSIvrRatio->nInProp=cIvrRatio.get_inProp();
        pSIvrRatio->nOurProp=cIvrRatio.get_outProp();
        pSIvrRatio->nSettleMode=cIvrRatio.get_settleMode();
        pSIvrRatio->lSettleAmount=cIvrRatio.get_settleAmount();
    }
    else
    {
        pSIvrRatio->nSettleMode =-1;
    }
    return ANA_SUCCESS;
}

int ImsiOperInfoAnalysis(xc::CSnapshot & snapShot,
            const char *piImsiCode,
            int64 iDateTime,
            ImsiOperInfo *poImsiOperInfo)
{
    CBpsImsiOperInfo cBpsImsiOperInfo;

    memset(poImsiOperInfo, 0, sizeof(ImsiOperInfo));
    memset(&cBpsImsiOperInfo, 0, sizeof(cBpsImsiOperInfo));
    
    if (piImsiCode == NULL || strlen(piImsiCode) == 0 )
    {
        return ANA_ERROR;
    }

    if (0 == m_ODACClient.findBpsImsiOperInfo(snapShot,piImsiCode,cBpsImsiOperInfo,iDateTime))
    {
        poImsiOperInfo->nIsGetInfo = 1;
        return ANA_SUCCESS;
    }
    poImsiOperInfo->nIsGetInfo = 0;
    
    return ANA_SUCCESS;
}

int ImsiNumberAnalysis(xc::CSnapshot & snapShot,
            char *piImsi,
            int64 iDateTime,
            ImsiNumberInfo *poImsiNumberInfo)
{
    CBpsImsiNumber cBpsGsmRouterData;

    memset(poImsiNumberInfo, 0, sizeof(poImsiNumberInfo));
    memset(&cBpsGsmRouterData, 0, sizeof(cBpsGsmRouterData));
    
    if (piImsi == NULL || strlen(piImsi) == 0)
    {
        return ANA_ERROR;
    }

    if (0 == m_ODACClient.findBpsImsiNumber(snapShot,piImsi,cBpsGsmRouterData,iDateTime))
    {
        strcpy(poImsiNumberInfo->szStartImsi ,cBpsGsmRouterData.get_startImsi());
        strcpy(poImsiNumberInfo->szEndImsi,cBpsGsmRouterData.get_endImsi());
        strcpy(poImsiNumberInfo->szAreaCode,cBpsGsmRouterData.get_areaCode());

        poImsiNumberInfo->nProductType = cBpsGsmRouterData.get_productType();
        poImsiNumberInfo->nClassify = cBpsGsmRouterData.get_classify();
    poImsiNumberInfo->nIsGetInfo = 1;
        return ANA_SUCCESS;
    }
    poImsiNumberInfo->nIsGetInfo = 0;
    return ANA_SUCCESS;
}

int getUserLocation(xc::CSnapshot & snapShot,const char *pszLacId,const char *pszCellId,int64 iDateTime, UserLocationInfo *pUserLocationInfo)
{
    pUserLocationInfo->m_iFlag = 0;
    pUserLocationInfo->m_szAreaCode[0] = 0;
    pUserLocationInfo->m_szCountyCode[0] = 0;
    
    if ((pszLacId == NULL) || (strlen(pszLacId) == 0) )
        return ANA_ERROR;

    CVBpsLacCeilCountyRel cLacCeilCountyRelData;

    if ( (pszCellId != NULL) && (strlen(pszCellId) != 0) )
    {
        if(0 == m_ODACClient.findCeilCountyRel(snapShot,pszLacId,pszCellId,cLacCeilCountyRelData,iDateTime) )
        {
            strcpy(pUserLocationInfo->m_szAreaCode, cLacCeilCountyRelData.get_areaCode());
            strcpy(pUserLocationInfo->m_szCountyCode, cLacCeilCountyRelData.get_countyCode());
            pUserLocationInfo->m_iFlag = 1;
            return ANA_SUCCESS;
        }
    }

    // 按lac + cell id 找不到，按lac再查
    CVBpsLacAreaRel cLacAreaRelData;

    if(0 == m_ODACClient.findLacAreaRel(snapShot,pszLacId,cLacAreaRelData,iDateTime) )
    {
        strcpy(pUserLocationInfo->m_szAreaCode, cLacAreaRelData.get_areaCode());
        pUserLocationInfo->m_szCountyCode[0] = 0;
        pUserLocationInfo->m_iFlag = 2;
        return ANA_SUCCESS;
    }
    

    return ANA_ERROR;
}

int32 getDaysInMonth(
    const    string&    v
)
{
    static        int32    monthDays[12] = {31,28,31,30,31,30,31,31,30,31,30,31};
    
    int32        yy = atoi(v.substr(0, 4).c_str());
    int32        mm = atoi(v.substr(4, 2).c_str());
    
    if (mm != 2)
    {
        return monthDays[mm - 1];
    }
    else
    {
        if (((yy % 400) == 0) || (((yy % 100) != 0) && ((yy % 4) == 0)))
            return 29;
        else
            return 28;
    }
}

int getDateFromFileName(char *pszFileName, char *fileNameDate)
{
    int iLen = strlen(pszFileName);
    int iStartPos = -1;
    
    for (int i =0; i < iLen; i++)
    {
        if ( *(pszFileName + i) >= '0' && *(pszFileName + i) <= '9' && i != iLen - 1 )
        {
            if (iStartPos == -1)
                iStartPos = i;
        }
        else
        {
            if (iStartPos >= 0)
            {
                char szDate[9];
                memset(szDate, 0, 9);

                if ((i - iStartPos) > 8)
                {
                    char *pszDate = pszFileName + iStartPos;
                    char *pszNewPos = strstr(pszDate, "20");
                    if (pszNewPos != NULL)
                        iStartPos = pszNewPos - pszFileName;
                }
                    
                if ((i - iStartPos) >= 8)
                {
                    strncpy(szDate, pszFileName + iStartPos, 8);
                    
                    int iDate  = atoi(szDate);
                    int iYear  = iDate / 10000;
                    int iMonth = (iDate - iYear * 10000) / 100;
                    int iDay   =  iDate - iYear * 10000 - iMonth * 100;
                    
                    if (iYear > 1752 && iMonth > 0 && iMonth <=12)
                    {
                        string strDate(szDate);
                        if (iDay > 0 && iDay <= getDaysInMonth(strDate))
                        {
                            strncpy(fileNameDate, szDate,sizeof(fileNameDate));
                            break;
                        }
                    }
                }

                iStartPos = -1;
            }
        }
    }
    
    return 0;
}

int FindNpInfo(xc::CSnapshot & snapShot,const char    * szNumber,
            const char *szDate,SNpInfo &p_pNpInfo)
{
    LOG_TRACE("in function FindNpInfo szNumber=<%s>",szNumber);
    LOG_TRACE("in function FindNpInfo szDate=<%s>",szDate);
    if(0 == m_ODACClient.FindNpInfo(szNumber,szDate,p_pNpInfo))
    {
        return 0;
    }
    else
    {
        return -1;
    }
    return 0;
}

int IpAddressAnalysis(xc::CSnapshot & snapShot, const char *address, int64 iDateTime, int findType, const char *ipType,char *areaCode)
{
    CVBpsIpv4Address cIpv4Address;
    CBpsIpv4AddressRange cBpsIpv4AddressRange;
    CBpsIpv6AddressRange cBpsIpv6AddressRange;

    if (address == NULL || strlen(address) == 0 || ipType == NULL || strlen(ipType) == 0)
    {
        return ANA_ERROR;
    }

    if(findType == 1){
        if(strcmp(ipType,"IPV4") == 0){
            if (0 == m_ODACClient.FindVBpsIpv4Address(snapShot,address,iDateTime,cIpv4Address)){
                strcpy(areaCode,cIpv4Address.get_areaCode());
                LOG_TRACE("areaCode = %s\n", areaCode);
                return ANA_SUCCESS;
            }else{
                LOG_TRACE("FindVBpsIpv4AddressInfo not find");
            }
        }else{
            LOG_TRACE("error ipType");
        }
    }else if (findType == 2){
        if(strcmp(ipType,"IPV4") == 0){
            if (0 == m_ODACClient.FindBpsIpv4AddressRange(snapShot,address,iDateTime,cBpsIpv4AddressRange)){
                strcpy(areaCode,cBpsIpv4AddressRange.get_landCity());
                LOG_TRACE("areaCode = %s\n", areaCode);
                return ANA_SUCCESS;
            }else{
                LOG_TRACE("FindBpsIpv4AddressRangeInfo not find");
            }
        }else if(strcmp(ipType,"IPV6") == 0){
            if (0 == m_ODACClient.FindBpsIpv6AddressRange(snapShot,address,iDateTime,cBpsIpv6AddressRange)){
                strcpy(areaCode,cBpsIpv6AddressRange.get_landCity());
                LOG_TRACE("areaCode = %s\n", areaCode);
                return ANA_SUCCESS;
            }else{
                LOG_TRACE("FindBpsIpv6AddressRangeInfo not find");
            }
        }else{
            LOG_TRACE("error ipType");
        }
    }else if (findType == 3){
        if(strcmp(ipType,"IPV4") == 0){
            if (0 == m_ODACClient.FindBpsIpv4AddressRange(snapShot,address,iDateTime,cBpsIpv4AddressRange)){
                strcpy(areaCode,cBpsIpv4AddressRange.get_landCity());
                LOG_TRACE("areaCode = %s\n", areaCode);
                return ANA_SUCCESS;
            }else{
                if (0 == m_ODACClient.FindVBpsIpv4Address(snapShot,address,iDateTime,cIpv4Address)){
                    strcpy(areaCode,cIpv4Address.get_areaCode());
                    return ANA_SUCCESS;
                }else{
                    LOG_TRACE("FindBpsIpv4AddressRange and FindBpsIpv4AddressRangeInfo not find");
                }
            }
        }else if(strcmp(ipType,"IPV6") == 0){
            if (0 == m_ODACClient.FindBpsIpv6AddressRange(snapShot,address,iDateTime,cBpsIpv6AddressRange)){
                strcpy(areaCode,cBpsIpv6AddressRange.get_landCity());
                LOG_TRACE("areaCode = %s\n", areaCode);
                return ANA_SUCCESS;
            }else{
                LOG_TRACE("FindBpsIpv6AddressRangeInfo other not find");
            }
        }else{
            LOG_TRACE("error ipType");
        }
    }else{
        LOG_TRACE("error findType");
    }

    return ANA_SUCCESS;
}

int getSjDefaultPriceInfo(xc::CSnapshot & snapShot, const char* settlementType, int64 iDateTime, int32 &settlementPrice)
{
    CBpsSjSmsDefaultPrice cBpsSjSmsDefaultPrice;
    if(0 == m_ODACClient.findBpsSjDefaultPrice(snapShot,settlementType,cBpsSjSmsDefaultPrice,iDateTime))
    {
        settlementPrice = cBpsSjSmsDefaultPrice.get_settlementPrice();
        return ANA_SUCCESS;
    }else{
        LOG_TRACE("find bps_sj_sms_default_price is not record");
        return ANA_ERROR;
    }
}

