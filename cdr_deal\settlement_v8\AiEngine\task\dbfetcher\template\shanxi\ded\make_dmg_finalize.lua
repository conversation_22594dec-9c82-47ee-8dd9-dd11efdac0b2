require "dbfetcher_util"

function main(args)
    -- 获取数据库连接
    local conn = getConnect()
    -- 获取任务 ID
    local inTaskId = getCode(args, "TASK_ID")

    writeLog("开始处理")

    -- 获取 BILL_MONTH 参数
    billMonth = getBillMonth(conn, inTaskId)
    
    -- 如果获取不到，返回错误
    if not billMonth then
        writeLog("未提供 BILL_MONTH 参数")
        return -1
    end

    -- 实际结算月份等于出账月份前两月，取YYYYMM
    settleMonth = string.sub(getAdjustDate(billMonth, -2), 1, 6)  

    writeLog("BILL_MONTH:" .. billMonth .. "  实际结算月份:" .. settleMonth)

    -- 获取当前月份（两位数）
    local currentMonth = os.date("%m")
    
    -- 获取当前时间完整格式
    local currentTime = os.date("%Y%m%d%H%M%S")

    -- 删除SEQ_ID包含非数字字符的数据
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                 WHERE TRIM(TRANSLATE(h.seq_id, '0123456789', ' ')) IS NOT NULL]]
    writeLog("删除SEQ_ID包含非数字字符的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除MSISDN格式不正确的数据
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                 WHERE TRIM(TRANSLATE(h.msisdn, '0123456789', ' ')) IS NOT NULL
                    OR LENGTH(h.msisdn) <> 11]]
    writeLog("删除MSISDN格式不正确的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除SP_CODE为698042且CHANNEL_CODE为空的测试数据
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                 WHERE h.sp_code = '698042'
                   AND h.channel_code IS NULL]]
    writeLog("删除SP_CODE为698042且CHANNEL_CODE为空的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除结束时间超过90天的05类型记录
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                 WHERE SUBSTR(h.use_time, 1, 8) <= TO_CHAR(SYSDATE - 90, 'YYYYMMDD')
                   AND h.audit_type = '05']]
    writeLog("删除结束时间超过90天的05类型记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除PROPERTY_CODE不为空且AUDIT_FEE大于10000的记录
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                 WHERE h.property_code IS NOT NULL
                   AND h.audit_fee > 10000]]
    writeLog("删除PROPERTY_CODE不为空且AUDIT_FEE大于10000的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除AUDIT_FEE小于等于0的记录
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                 WHERE h.audit_fee <= 0]]
    writeLog("删除AUDIT_FEE小于等于0的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 更新不符合条件的CDR_TYPE为'02'
    local sql = [[UPDATE jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                 SET h.cdr_type = '02'
                 WHERE h.cdr_type NOT IN ('02','07','21','22')]]
    writeLog("更新不符合条件的CDR_TYPE为02的记录")
    executeSQL(conn, sql)
    conn:commit()
    
    -- 删除CDR_TYPE为07且SP_CODE不等于698042的记录
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                 WHERE h.cdr_type = '07'
                   AND h.sp_code <> '698042']]
    writeLog("删除CDR_TYPE为07且SP_CODE不等于698042的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除CDR_TYPE为21或22且SP_CODE不等于698041的记录
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                 WHERE h.cdr_type IN ('21','22')
                   AND h.sp_code <> '698041']]
    writeLog("删除CDR_TYPE为21或22且SP_CODE不等于698041的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除CHARGE_TYPE不为03且不符合交易ID格式的记录
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                 WHERE h.charge_type <> '03'
                   AND (h.content_id IS NULL OR
                       TRIM(TRANSLATE(NVL(h.transaction_id, 'a'), '0123456789', ' ')) IS NOT NULL OR
                       LENGTH(h.transaction_id) <> 22)]]
    writeLog("删除CHARGE_TYPE不为03且不符合交易ID格式的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除CDR_SEQ长度不等于22或包含非数字字符的记录
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                 WHERE LENGTH(h.cdr_seq) <> 22
                    OR TRIM(TRANSLATE(NVL(h.cdr_seq, 'a'), '0123456789', ' ')) IS NOT NULL]]
    writeLog("删除CDR_SEQ格式不正确的记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除与历史数据重复的05类型记录
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ a
                 WHERE a.month_id = ']] .. currentMonth .. [['
                   AND a.audit_type = '05'
                   AND a.audit_time LIKE ']] .. currentTime .. [[%'
                   AND (a.busi_type, a.audit_type, a.msisdn, a.sp_code, a.oper_code,
                        a.use_time) IN
                       (SELECT h.busi_type,
                               h.audit_type,
                               h.msisdn,
                               h.sp_code,
                               h.oper_code,
                               h.use_time
                          FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                         WHERE h.month_id <> ']] .. currentMonth .. [['
                           AND h.audit_type = '05'
                           AND h.audit_time NOT LIKE ']] .. currentTime .. [[%'
                         GROUP BY h.busi_type,
                                  h.audit_type,
                                  h.msisdn,
                                  h.sp_code,
                                  h.oper_code,
                                  h.property_code,
                                  h.use_time,
                                  h.transaction_id)]]
    writeLog("删除与历史数据重复的05类型记录")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除重复记录，保留每组重复记录中的第一条
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ a
                 WHERE seq_id NOT IN (SELECT MIN(seq_id)
                                     FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h
                                    GROUP BY h.busi_type,
                                             h.audit_type,
                                             h.msisdn,
                                             h.sp_code,
                                             h.oper_code,
                                             h.use_time)]]
    writeLog("删除重复记录，保留每组重复记录中的第一条")
    executeSQL(conn, sql)
    conn:commit()
    ------------------------------------------------------
    writeLog("处理完成")
    conn:close()
    return 0
end

environment = os.getenv("ENVIRONMENT")
if environment == "TEST" then
    main("TASK_ID=17990|BILL_MONTH=202502")
    os.exit()
end
