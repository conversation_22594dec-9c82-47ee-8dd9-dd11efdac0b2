#include "table_op_define.h"

// ============================================================================
// OpTableBaseBase 实现
// ============================================================================

int OpTableBaseBase::run_querySql(otl_connect &db_conn, const std::string& qrySql, 
                                  std::vector<CTabelStructBase*>& resDataList, int32 iLimit) {
    otl_stream db_stream;

    resDataList.clear();
    std::string finalSql = qrySql;
    if (iLimit > 0) {
        finalSql = finalSql + " LIMIT " + std::to_string(iLimit);
    }
#ifdef  DEBUG_PRINT
    std::cout << "querySql: " << finalSql << std::endl;
#endif
    try {
        db_stream.open(100, finalSql.c_str(), db_conn);

        while (!db_stream.eof()) {
            CTabelStructBase* data = createDataObject(); // 使用工厂方法创建对象
            db_stream >> *data;
            resDataList.push_back(data);
        }

        db_stream.flush();
        db_stream.close();
        return resDataList.size();
    } catch (otl_exception& e) {
        std::cerr << "OTL Exception: " << e.msg << std::endl;
        std::cerr << "VAR_INFO:" << e.var_info << std::endl;
        db_conn.rollback();
        return -1;
    }
}

int OpTableBaseBase::run_deleteSql(otl_connect& db_conn, const std::string& delSql)
{
#ifdef  DEBUG_PRINT
    std::cout << "delSql: " << delSql << std::endl;
#endif
    try {
        otl_cursor::direct_exec(db_conn, delSql.c_str());
        return 0;
    } catch (otl_exception& e) {
        std::cerr << "OTL Exception: " << e.msg << std::endl;
        std::cerr << "VAR_INFO:" << e.var_info << std::endl;
        db_conn.rollback();
        return -1;
    }
}

int OpTableBaseBase::run_insertSql(otl_connect& db_conn, const std::vector<CTabelStructBase*>& pDataList)
{
    otl_stream db_stream;
    std::string insertSql = get_insertSql();

    std::cout << "insertSql: " << insertSql << std::endl;
    std::cout << "pDataList.size= " << pDataList.size() << std::endl;

    try {
        db_stream.open(100, insertSql.c_str(), db_conn);

        for (const auto& pData : pDataList) {
            std::cout<<"pData: " << *pData << std::endl;
            db_stream <<  *pData; // 使用重载的 << 操作符
        }

        db_stream.close();
        std::cout << "run_insertSql out pDataList.size= " << pDataList.size() << std::endl;
        return pDataList.size();
    } catch (otl_exception& e) {
        std::string err_msg(reinterpret_cast<const char*>(e.msg), sizeof(e.msg)); //  unsigned char[] 到 string

        if (err_msg.find("duplicate key") != std::string::npos ||
            err_msg.find("Duplicate entry") != std::string::npos) 
        {          
            db_stream.close();
            return 0;
        }
        else
        {
            std::cerr << "OTL Exception code: " << e.code << std::endl;
            std::cerr << "OTL Exception: " << e.msg << std::endl;
            std::cerr << "VAR_INFO:"<< e.var_info << std::endl;
            db_stream.close();

            db_conn.rollback();
            return -1;
        }
    }
}


// ============================================================================
// OpIDataIndex 实现
// ============================================================================

std::string OpIDataIndex::get_querySql(const std::string condition) {
    return "SELECT PHONE_ID, UP_FIELD, SYNC_FLAG, BUSI_CODE, REGION_CODE, COUNTY_CODE, OP_ID, \
            TO_CHAR(COMMIT_TIME, 'YYYYMMDD') as COMMIT_DATE, EXTRACT(EPOCH FROM COMMIT_TIME) AS COMMIT_TIME, \
            SO_NBR, REMARK FROM " + table_name + " WHERE " + condition;
}

std::string OpIDataIndex::get_insertSql() {
    return "INSERT INTO " + table_name +
           "(PHONE_ID, UP_FIELD, SYNC_FLAG, BUSI_CODE, REGION_CODE, COUNTY_CODE, OP_ID, COMMIT_TIME, SO_NBR, REMARK) " +
           "VALUES (:1<char[28]>, :2<char[16]>, :3<int>, :4<bigint>, :5<int>, :6<int>, :7<bigint>, :8<timestamp>, :9<bigint>, :10<char[128]>)";
}

std::string OpIDataIndex::get_deleteSql(const std::string condition) {
    return "DELETE FROM " + table_name + " WHERE " + condition;
}

std::string OpIDataIndex::get_updateSql(const std::string condition) {
    return "UPDATE " + table_name + " SET SYNC_FLAG = :1<int> WHERE " + condition;
}

CTabelStructBase* OpIDataIndex::createDataObject() {
    return new CIDataIndex();
}

// ============================================================================
// OpIDataIndexHis 实现
// ============================================================================

std::string OpIDataIndexHis::get_querySql(const std::string condition) {
    return "SELECT PHONE_ID, UP_FIELD, SYNC_FLAG, BUSI_CODE, REGION_CODE, COUNTY_CODE, OP_ID, \
            EXTRACT(EPOCH FROM COMMIT_TIME) AS COMMIT_TIME, SO_NBR, \
            EXTRACT(EPOCH FROM DEAL_TIME) AS DEAL_TIME, REMARK " 
           "FROM " + table_name + " WHERE " + condition;
}

std::string OpIDataIndexHis::get_insertSql() {
    return "INSERT INTO " + table_name +
           "(PHONE_ID, UP_FIELD, SYNC_FLAG, BUSI_CODE, REGION_CODE, COUNTY_CODE, OP_ID, COMMIT_TIME, SO_NBR, DEAL_TIME, REMARK) " +
           "VALUES (:1<char[28]>, :2<char[16]>, :3<int>, :4<bigint>, :5<int>, :6<int>, :7<bigint>, :8<timestamp>, :9<bigint>, :10<timestamp>, :11<char[128]>)";
}

std::string OpIDataIndexHis::get_deleteSql(const std::string condition) {
    return "DELETE FROM " + table_name + " WHERE " + condition;
}

std::string OpIDataIndexHis::get_updateSql(const std::string condition) {
    return "UPDATE " + table_name + " SET SYNC_FLAG = :1<int> WHERE " + condition;
}

CTabelStructBase* OpIDataIndexHis::createDataObject() {
    return new CIDataIndexHis();
}

// ============================================================================
// OpIDataIndexErr 实现
// ============================================================================

std::string OpIDataIndexErr::get_querySql(const std::string condition) {
    return "SELECT PHONE_ID, UP_FIELD, SYNC_FLAG, BUSI_CODE, REGION_CODE, COUNTY_CODE, OP_ID, \
            EXTRACT(EPOCH FROM COMMIT_TIME) AS COMMIT_TIME, SO_NBR, ERROR_CODE, ERROR_MSG, \
            EXTRACT(EPOCH FROM DEAL_TIME) AS DEAL_TIME, REMARK " 
           "FROM " + table_name + " WHERE " + condition;
}

std::string OpIDataIndexErr::get_insertSql() {
    return "INSERT INTO " + table_name +
           "(PHONE_ID, UP_FIELD, SYNC_FLAG, BUSI_CODE, REGION_CODE, COUNTY_CODE, OP_ID, COMMIT_TIME, SO_NBR, ERROR_CODE, ERROR_MSG, DEAL_TIME, REMARK) " + 
           "VALUES (:1<char[28]>, :2<char[16]>, :3<int>, :4<bigint>, :5<int>, :6<int>, :7<bigint>, :8<timestamp>, :9<bigint>, :10<int>, :11<char[255]>, :12<timestamp>, :13<char[128]>)";
}

std::string OpIDataIndexErr::get_deleteSql(const std::string condition) {
    return "DELETE FROM " + table_name + " WHERE " + condition;
}

std::string OpIDataIndexErr::get_updateSql(const std::string condition) {
    return "UPDATE " + table_name + " SET SYNC_FLAG = :1<int> WHERE " + condition;
}

CTabelStructBase* OpIDataIndexErr::createDataObject() {
    return new CIDataIndexErr();
}

// ============================================================================
// OpIMsisdnInfo 实现
// ============================================================================

std::string OpIMsisdnInfo::get_querySql(const std::string condition) {
    return "SELECT PHONE_ID, COUNTY_CODE, GRID_CODE, AREA_CODE, BUREAU_CODE, USER_TYPE, \
            EXTRACT(EPOCH FROM VALID_DATE) AS VALID_DATE, EXTRACT(EPOCH FROM EXPIRE_DATE) AS EXPIRE_DATE, \
            RESERVED1, RESERVED2, RESERVED3, SO_NBR, SYNC_FLAG, EXTRACT(EPOCH FROM COMMIT_TIME) AS COMMIT_TIME, \
            REMARK FROM " + table_name + " WHERE " + condition;
}

std::string OpIMsisdnInfo::get_insertSql() {
    return "INSERT INTO " + table_name +
           "(PHONE_ID, COUNTY_CODE, GRID_CODE, AREA_CODE, BUREAU_CODE, USER_TYPE, " +
           "VALID_DATE, EXPIRE_DATE, RESERVED1, RESERVED2, RESERVED3, SO_NBR, SYNC_FLAG, COMMIT_TIME, REMARK) " +
           "VALUES (:1<char[28]>, :2<char[5]>, :3<char[16]>, :4<int>, :5<char[16]>, :6<int>, " +
           ":7<timestamp>, :8<timestamp>, :9<char[32]>, :10<char[32]>, :11<char[32]>, " +
           ":12<bigint>, :13<int>, :14<timestamp>, :15<char[512]>)";
}

std::string OpIMsisdnInfo::get_updateSql(const std::string condition) {
    return "UPDATE " + table_name + " SET SYNC_FLAG = :1<int> WHERE " + condition;
}

std::string OpIMsisdnInfo::get_deleteSql(const std::string condition) {
    return "DELETE FROM " + table_name + " WHERE " + condition;
}

CTabelStructBase* OpIMsisdnInfo::createDataObject() {
    return new CIMsisdnInfo();
}

// ============================================================================
// OpINationalMnp 实现
// ============================================================================

std::string OpINationalMnp::get_querySql(const std::string condition) {
    return "SELECT PHONE_ID, SRC_NETID, DES_NETID, OWN_NETID, \
            EXTRACT(EPOCH FROM VALID_DATE) AS VALID_DATE, \
            EXTRACT(EPOCH FROM EXPIRE_DATE) AS EXPIRE_DATE, \
            SYNC_FLAG, SO_NBR, EXTRACT(EPOCH FROM COMMIT_TIME) AS COMMIT_TIME, \
            REMARK FROM " + table_name + " WHERE " + condition;
}

std::string OpINationalMnp::get_insertSql() {
    return "INSERT INTO " + table_name +
           "(PHONE_ID, SRC_NETID, DES_NETID, OWN_NETID, VALID_DATE, EXPIRE_DATE, " +
           "SYNC_FLAG, SO_NBR, COMMIT_TIME, REMARK) " +
           "VALUES (:1<char[28]>, :2<char[10]>, :3<char[10]>, :4<char[10]>, " +
           ":5<timestamp>, :6<timestamp>, :7<int>, :8<bigint>, :9<timestamp>, :10<char[512]>)";
}

std::string OpINationalMnp::get_updateSql(const std::string condition) {
    return "UPDATE " + table_name + " SET SYNC_FLAG = :1<int> WHERE " + condition;
}

std::string OpINationalMnp::get_deleteSql(const std::string condition) {
    return "DELETE FROM " + table_name + " WHERE " + condition;
}

CTabelStructBase* OpINationalMnp::createDataObject() {
    return new CINationalMnp();
}

// ============================================================================
// OpBpsMsisdnInfo 实现
// ============================================================================

std::string OpBpsMsisdnInfo::get_querySql(const std::string condition) {
    return "SELECT MSISDN, COUNTY_CODE, GRID_CODE, AREA_CODE, BUREAU_CODE, USER_TYPE, \
            VALID_DATE, EXPIRE_DATE, RESERVED1, RESERVED2, RESERVED3 \
            FROM " + table_name + " WHERE " + condition;
}

std::string OpBpsMsisdnInfo::get_insertSql() {
    return "INSERT INTO " + table_name +
           "(MSISDN, COUNTY_CODE, GRID_CODE, AREA_CODE, BUREAU_CODE, USER_TYPE, " +
           "VALID_DATE, EXPIRE_DATE, RESERVED1, RESERVED2, RESERVED3) " +
           "VALUES (:1<char[15]>, :2<char[5]>, :3<char[16]>, :4<int>, :5<char[16]>, :6<int>, " +
           ":7<int>, :8<int>, :9<char[32]>, :10<char[32]>, :11<char[32]>)";
}

std::string OpBpsMsisdnInfo::get_updateSql(const std::string condition) {
    return "UPDATE " + table_name + " SET " +
           "COUNTY_CODE = :1<char[5]>, " +
           "GRID_CODE = :2<char[16]>, " +
           "AREA_CODE = :3<int>, " +
           "BUREAU_CODE = :4<char[16]>, " +
           "USER_TYPE = :5<int>, " +
           "EXPIRE_DATE = :6<int>, " +
           "RESERVED1 = :7<char[32]>, " +
           "RESERVED2 = :8<char[32]>, " +
           "RESERVED3 = :9<char[32]> " +
           "WHERE " + condition;
}

std::string OpBpsMsisdnInfo::get_deleteSql(const std::string condition) {
    return "DELETE FROM " + table_name + " WHERE " + condition;
}

CTabelStructBase* OpBpsMsisdnInfo::createDataObject() {
    return new CBpsMsisdnInfo();
}

// ============================================================================
// OpBpsNationMnp 实现
// ============================================================================

std::string OpBpsNationMnp::get_querySql(const std::string condition) {
    return "SELECT PHONE_NUMBER, SRC_NET_ID, DEST_NET_ID, OWN_NET_ID, VALID_DATE, EXPIRE_DATE \
            FROM " + table_name + " WHERE " + condition;
}

std::string OpBpsNationMnp::get_insertSql() {
    return "INSERT INTO " + table_name +
           "(PHONE_NUMBER, SRC_NET_ID, DEST_NET_ID, OWN_NET_ID, VALID_DATE, EXPIRE_DATE) " +
           "VALUES (:1<char[28]>, :2<char[16]>, :3<char[16]>, :4<char[16]>, :5<int>, :6<int>)";
}

std::string OpBpsNationMnp::get_updateSql(const std::string condition) {
    return "UPDATE " + table_name + " SET " +
           "SRC_NET_ID = :1<char[16]>, " +
           "DEST_NET_ID = :2<char[16]>, " +
           "OWN_NET_ID = :3<char[16]>, " +
           "VALID_DATE = :4<int>, " +
           "EXPIRE_DATE = :5<int> " +
           "WHERE " + condition;
}

std::string OpBpsNationMnp::get_deleteSql(const std::string condition) {
    return "DELETE FROM " + table_name + " WHERE " + condition;
}

CTabelStructBase* OpBpsNationMnp::createDataObject() {
    return new CBpsNationMnp();
}
