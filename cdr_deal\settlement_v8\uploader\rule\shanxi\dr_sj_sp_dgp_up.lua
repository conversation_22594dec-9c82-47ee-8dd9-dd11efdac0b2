--*****************************************************************************
-- *	file:	mread.expr
-- *  brief:
-- *		define the rule of uploader gsm's "mread" file.
-- *		定义上发 全网手机阅读业务 文件通用分析模板
-- *
-- *	Copyright (c) 2003,2004,2005 Asiainfo Technologies(China),Inc.
-- *
-- *	RCS: Id: mread.expr,v 1.1.2.55 2014/01/15 07:15:36 nijj Exp 
-- *
-- *	History:
-- *		2023-10-12  yangwq  create
-- * 		2025-06-27  qiangpj modify for shanxi
-- ****************************************************************************
require "libluabaseD"
require "libluadebugD"
	
	local logtrace = libluadebugD.report_trace
 	local logerror = libluadebugD.report_error
	
	<% use("settle_Xdr_app.MXdr::SXdr") %>
    --为支持reset模板可以使用，定义为全局变量，去掉local
    pSubCommon = nil;               --SXdr.SSubXdr.SSubCommon
    pSP = nil;                 --SXdr.SSubXdr.SGsmInfo

    pFileOpInfo = nil;
    pOriCharge = nil;
    local iDoPrintInfo	= 1;	            --1:打印调试信息到log,	0:不打印调试信息到log
	
	t_sEmpty		= " ";
	t_sDealDateTime	= "";
	t_sOutRecord	= "";
	t_sProvCode		= g_sThisProvCode;

	--!组织头、尾、空记录，统计、监控信息
	t_iProcNo	= 0;
	t_iValidNumber	= 0;
	t_iErrorNumber	= 0;
	t_iRecordNumber	= 0;
	t_iDupNumber	= 0;
	t_iLateNumber	= 0;
	t_iSecondCdr	= 0;
	t_iErrLater		= 0;
	t_iErrLater     = 0;
	t_iErrLaterOnce = 0;
	t_iProcNo       = t_iFileNo
	t_sBeginTime	= "20300101000000";
	t_sEndTime	= "20000101000000";

	t_lTotalInfoFee		= 0;
	t_lTotalDiscountFee     = 0;

	--Get Next Upload Time.
	t_sNextUploadTime = "";
	iRes,t_sDealDateTime		= gethosttime();
	t_sDate		= "";
	
  

--[[============================================================================
--函数名称: init_getsdlval
--作    用: 初始化指针
--输入参数:
--输出参数:
--返回值：
--==========================================================================--]]
function init_getsdlval()
	if iDoPrintInfo == 1
	then	
        logtrace('----------init_getsdlval begin-------------');
    end

    pSubXdr = <%get_struct_value('PXdr',"MXdr::SXdr.TOTAL_XDR") %>
        
	if pSubXdr == nil
	then
		if iDoPrintInfo == 1
		then
            logtrace('----------init_getsdlval failed.-------------');
        end
        return -1;
    end
        
	------  init pCommon  ------  
	pSP = <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SP_INFO62") %>;
	pSubCommon		= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SUB_COMMON") %>
	pFileOpInfo  	= <%get_struct_value('pSubCommon', "MXdr::SSubCommon.FILE_OP_INFO")%>;
	if pSP == nil or pSubCommon == nil or pFileOpInfo == nil
	then
		if iDoPrintInfo == 1
		then
			logtrace('----------init_getsdlval failed.-------------');
		end
		return -1;
	end;

	if iDoPrintInfo == 1
	then
		logtrace('----------init_getsdlval end-------------');
	end
	return 0;
end

local function head_record()
	iRes, t_sDealDateTime = GetHostTimeR();
	
	if(t_iDate < tonumber(string.sub(t_sDealDateTime,0,8)))
	then
		t_sDealDateTime	= t_iDate.."235501";
	else
		if(tonumber(t_iDate) > tonumber(string.sub(t_sDealDateTime,0,8)))
		then
			t_sDealDateTime = t_iDate.."000000"
		end
	end
	t_iValidNumber  = 0;
	t_sBeginTime    = "20200101000000";
	t_sEndTime      = "20000101000000";
	
	t_iTotalFee     = 0;
	t_sOutRecord = "1046000"
			.. lpad(t_sProvCode, " ", 3)
			.. rpad(t_sEmpty, " ", 2)
			.. "46000000"
			.. rpad(t_sEmpty, " ", 2)
			.. lpad(tostring(t_iProcNo), "0", 3)
			.. lpad(t_sDealDateTime, " ", 14)
			.. "01"
			.. rpad(t_sEmpty, " ", 157)
                        ;
	t_iResult      = 0;
	return;
end
	
local function file_name()
	------------start reset---------
	t_sDealDateTime="";
	
	--!组织头、尾、空记录，统计、监控信息
	sFileType       = ""..t_sFileType;
	t_sDate         = "";
	t_iProcNo	= 0;
	t_iProcNo       = t_iFileNo
	t_sDate         = tostring(t_iDate);
	t_sOutFileName = sFileType
					.. t_sDate
					.. lpad(tostring(t_iProcNo), "0", 3)
					.. "."
					.. t_sProvCode;
	t_iResult      = 0;
	return;
end

local function tail_record()


	t_sOutRecord   = "9046000000" 
			.. rpad(t_sEmpty, " ", 2)
			.. "46000"
			.. lpad(t_sProvCode, " ", 3)
			.. rpad(t_sEmpty, " ", 2)
			.. lpad(tostring(t_iProcNo), "0", 3)
			.. lpad(tostring(t_iValidNumber), "0", 9)
			.. lpad(tostring(t_iTotalFee), "0", 12)
			.. rpad(t_sEmpty, " ", 152);
		t_iResult	= 0;
	return;
end

local function null_body_record()
	t_sOutRecord	= "";
	t_iResult	= 0;

	return;
end
	
	
local function body_record()

	pSP = <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SP_INFO62") %>;
	pSubCommon		= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SUB_COMMON") %>

	t_sBillMonth  		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.CONFIRM_TIME")%>;
	t_iDedHead    		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.REC_TYPE")%>;
 	-- t_sServType		=  <%get_struct_value('pSP', "MXdr::SSjSpInfo.RPT_TYPE")%>;
	t_sServType		=  <%get_struct_value('pSP', "MXdr::SSjSpInfo.SERVICE_TYPE")%>;
	t_sDedType   		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.USER_TYPE")%>;
	t_sDoneCode		=  <%get_struct_value('pSP', "MXdr::SSjSpInfo.AREA_CODE")%>;
	t_sBillId		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.RING_ID")%>;
	t_sSpCode		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.SP_CODE")%>;
	t_sOperatorCode		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.OPER_CODE")%>;
	t_sPropsCode 		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.SERV_CODE")%>;
	t_sChannelCode 		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.CH_CODE")%>;
	t_sBillType		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.CHARGE_TYPE")%>;
	t_sUseTime		=    <%get_struct_value('pSubCommon', "MXdr::SSubCommon.START_TIME")%>;
	t_sDedTime		=   <%get_struct_value('pSubCommon', "MXdr::SSubCommon.FINISH_TIME")%>;
	t_sDedFee		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.INFO_FEE")%>;
	t_sCallType		=  <%get_struct_value('pSP', "MXdr::SSjSpInfo.DEAL_TYPE")%>;
	t_sContentId   		=   <%get_struct_value('pSP', "MXdr::SSjSpInfo.SONG_ID")%>;
	t_sSerialNumber   =    <%get_struct_value('pSP', "MXdr::SSjSpInfo.MSG_ID")%>;

	iTempUseTime = libluabaseD.gettime(t_sUseTime);
	if(iTempUseTime == -1)
	then
		t_sErrNo = "F050";
		return error_deal(pSubCommon,t_sErrNo);
	end
	iTempDedTime = libluabaseD.gettime(t_sDedTime);
	if(iTempDedTime == -1)
	then
		t_sErrNo = "F050";
		return error_deal(pSubCommon,t_sErrNo);
	end
	if(string.len(t_sDedType) == 1)
	then
		t_sDedType = "0"..t_sDedType
	end
	if(string.len(t_sBillType) == 1)
	then
		t_sBillType = "0"..t_sBillType
	end
	
--[[
	sTempKey = t_sServType + t_sDedType + t_sDoneCode + t_sUseTime + $g_sProvCode;

	t_iCheckFlag = checkdup(sTempKey,substr(t_sUseTime,0,8),strlen(sTempKey),$DupDir);
	if(t_iCheckFlag == -1)
	then
		t_sErrNo = "F200";
		error_deal(pSubCommon,t_sErrNo);
	end
	if(t_iCheckFlag < 0)
	then
		t_sErrNo = "F888";
		error_deal(pSubCommon,t_sErrNo);
	end
--]]
	--!组织体记录
	
	t_sOutRecord = rpad(tostring(t_iDedHead), "0", 2)
		.. rpad(t_sServType, " ", 10)
		.. rpad(t_sDedType, " ", 2)
		.. rpad(t_sDoneCode, " ", 20)
		.. rpad(t_sBillId, " ", 15)
		.. rpad(t_sSpCode, " ", 20)
		.. rpad(t_sOperatorCode," ",20)
		.. rpad(t_sPropsCode," ",12)
		.. rpad(t_sChannelCode,"0",8)
		.. rpad(t_sBillType,"0",2)
		.. rpad(t_sUseTime, " ", 14)
		.. rpad(t_sDedTime, " ", 14)
		.. lpad(t_sDedFee, "0", 6)
		.. lpad(t_sCallType, "0", 2)
		.. rpad(t_sContentId," ",12)
		.. rpad(t_sSerialNumber," ",22)
		.. rpad(t_sEmpty," ",17)
		;
		t_iValidNumber	= t_iValidNumber + 1;
		t_iTotalFee = t_iTotalFee + tonumber(t_sDedFee);
		t_iResult	= 0;

	return;
	
end


function upload_main()
	if iDoPrintInfo == 1 then
		logtrace('------------------------------upload_mread_main begin--------------------------------');
	end

	--0:head 1:tail 2:body 3:null body 4:filename 
	if(t_iLocation == 0) then
		head_record();
	elseif(t_iLocation == 1) then
		tail_record();
	elseif(t_iLocation == 2) then
		init_getsdlval();
		body_record();
	elseif(t_iLocation == 3) then
		null_body_record();
	elseif(t_iLocation == 4) then
		file_name();
	end

	if iDoPrintInfo == 1 then
		logtrace('------------------------------upload_mread_main end--------------------------------');
	end
end


function error_deal(pSubCommon,sErrorCode)
	<%set_struct_value('pFileOpInfo', "MFileOp::SFileOp.TREAT_FLAG",1)%>
	<%set_struct_value('pFileOpInfo',"MFileOp::SFileOp.ERROR_CODE",sErrorCode) %>
	logtrace("error_deal,errocode:"..sErrorCode)
	
	--!累加错单记录
	t_iErrorNumber	= t_iErrorNumber + 1;
	t_sOutRecord =rpad(tostring(t_iDedHead), "0", 2)
		.. rpad(t_sServType, " ", 10)
		.. rpad(t_sDedType, " ", 2)
		.. rpad(t_sDoneCode, " ", 20)
		.. rpad(t_sBillId, " ", 15)
		.. rpad(t_sSpCode, " ", 20)
		.. rpad(t_sOperatorCode," ",20)
		.. rpad(t_sPropsCode," ",12)
		.. rpad(t_sChannelCode,"0",8)
		.. rpad(t_sBillType,"0",2)
		.. rpad(t_sUseTime, " ", 14)
		.. rpad(t_sDedTime, " ", 14)
		.. lpad(t_sDedFee, "0", 6)
		.. lpad(t_sCallType, "0", 2)
		.. rpad(t_sContentId," ",12)
		.. rpad(t_sSerialNumber," ",22)
		.. rpad(t_sEmpty," ",17)		
			;
		 	
	t_iResult	= 1;
end

