--*****************************************************************************
-- *	file:	mread.expr
-- *  brief:
-- *		define the rule of uploader gsm's "mread" file.
-- *		定义上发 全网手机阅读业务 文件通用分析模板
-- *
-- *	Copyright (c) 2003,2004,2005 Asiainfo Technologies(China),Inc.
-- *
-- *	RCS: Id: mread.expr,v 1.1.2.55 2014/01/15 07:15:36 nijj Exp 
-- *
-- *	History:
-- *		2009-09-07  hexb  create
-- ****************************************************************************
require "libluabaseD"
require "libluadebugD"
	
	local logtrace = libluadebugD.report_trace
 	local logerror = libluadebugD.report_error
	
	<% use("settle_Xdr_app.MXdr::SXdr") %>
    --为支持reset模板可以使用，定义为全局变量，去掉local
    pSubCommon = nil;               --SXdr.SSubXdr.SSubCommon
    pGsmInfo = nil;                 --SXdr.SSubXdr.SGsmInfo

    pFileOpInfo = nil;
    pOriCharge = nil;
    local iDoPrintInfo	= 1;	            --1:打印调试信息到log,	0:不打印调试信息到log
	
	t_sEmpty		= " ";
	t_sDealDateTime	= "";
	t_sOutRecord		= "";
	t_sProvCode		= "571"; --省代码

	--!组织头、尾、空记录，统计、监控信息
	t_iProcNo	= 0;
	t_iProcNo	= t_iFileNo;
	t_iValidNumber	= 0;
	t_iErrorNumber	= 0;
	t_iRecordNumber	= 0;
	t_iDupNumber	= 0;
	t_iLateNumber	= 0;
	t_iSecondCdr	= 0;
	t_iErrLater		= 0;
	t_iErrLater     = 0;
	t_iErrLaterOnce = 0;
	t_iProcNo	= t_iFileNo;
	t_sBeginTime	= "20300101000000";
	t_sEndTime	= "20000101000000";

	t_lTotalInfoFee		= 0;
	t_lTotalDiscountFee     = 0;

	--Get Next Upload Time.
	t_sNextUploadTime = "";
	iRes,t_sDealDateTime		= gethosttime();
	t_sDate		= "";
	
  

--[[============================================================================
--函数名称: init_getsdlval
--作    用: 初始化指针
--输入参数:
--输出参数:
--返回值：
--==========================================================================--]]
function init_getsdlval()
	if iDoPrintInfo == 1
	then	
        logtrace('----------init_getsdlval begin-------------');
    end

    pSubXdr = <%get_struct_value('PXdr',"MXdr::SXdr.TOTAL_XDR") %>
        
	if pSubXdr == nil
	then
		if iDoPrintInfo == 1
		then
            logtrace('----------init_getsdlval failed.-------------');
        end
        return -1;
    end
        
	------  init pCommon  ------  
	pSP = <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SP_INFO62") %>;
	pSubCommon		= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SUB_COMMON") %>
	pFileOpInfo  	= <%get_struct_value('pSubCommon', "MXdr::SSubCommon.FILE_OP_INFO")%>;
	if pSP == nil or pSubCommon == nil or pFileOpInfo == nil
	then
		if iDoPrintInfo == 1
		then
			logtrace('----------init_getsdlval failed.-------------');
		end
		return -1;
	end;

	if iDoPrintInfo == 1
	then
		logtrace('----------init_getsdlval end-------------');
	end
	return 0;
end

local function head_record()
	iRes, t_sDealDateTime = GetHostTimeR();
	
	if(t_iDate < tonumber(string.sub(t_sDealDateTime,0,8)))
	then
		t_sDealDateTime	= t_iDate.."235501";
	else
		if(tonumber(t_iDate) > tonumber(string.sub(t_sDealDateTime,0,8)))
		then
			t_sDealDateTime = t_iDate.."000000"
		end
	end

	t_iValidNumber  = 0;
	t_sBeginTime    = "20200101000000";
	t_sEndTime      = "20000101000000";
	t_iTotalFee     = 0;
	t_sOutRecord = "1046000"
			.. lpad(t_sProvCode, " ", 3)
			.. rpad(t_sEmpty, " ", 2)
			.. "46000000"
			.. rpad(t_sEmpty, " ", 2)
			.. lpad(tostring(t_iProcNo), "0", 3)
			.. lpad(t_sDealDateTime, " ", 14)
			.. "01"
			.. rpad(t_sEmpty, " ", 237)
                        ;

	return;
end
	
local function file_name()
	------------start reset---------
	t_sDealDateTime="";
	
	--!组织头、尾、空记录，统计、监控信息
	t_iProcNo	= 0;
	t_iProcNo	= t_iFileNo;
	sFileType       = ""..t_sFileType;
	t_sDate         = "";
	t_sDate         = tostring(t_iDate);
	t_sOutFileName = sFileType
					.. t_sDate
					.. lpad(tostring(t_iProcNo), "0", 3)
					.. "."
					.. t_sProvCode;
	t_iResult      = 0;

	return;
end

local function tail_record()

	t_iProcNo	= 0;
	t_iProcNo	= t_iFileNo;
	t_sOutRecord   = "9046000000" 
			.. rpad(t_sEmpty, " ", 2)
			.. "46000"
			.. lpad(t_sProvCode, " ", 3)
			.. rpad(t_sEmpty, " ", 2)
			.. lpad(tostring(t_iProcNo), "0", 3)
			.. lpad(tostring(t_iValidNumber), "0", 9)
			.. lpad(tostring(t_iTotalFee), "0", 12)
			.. rpad(t_sEmpty, " ", 232);
		t_iResult	= 0;
	return;
end

local function null_body_record()
	t_sOutRecord	= "";
	t_iResult	= 0;

	return;
end
	
	
local function body_record()

	pSP = <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SP_INFO62") %>;
	pSubCommon		= <%get_struct_value('pSubXdr',"MXdr::SSubXdr.SUB_COMMON") %>

	t_sBillMonth            =   <%get_struct_value('pSP', "MXdr::SSjSpInfo.CONFIRM_TIME")%>;
	if(t_sBillMonth == nil)
	then
		t_sBillMonth = "";
	end
	t_iRecType	=	<%get_struct_value('pSP', "MXdr::SSjSpInfo.REC_TYPE")%>;--中间记录标记
	if(t_iRecType == nil)
	then
		t_iRecType = 0;
	end
	t_sBusiType	=	<%get_struct_value('pSP', "MXdr::SSjSpInfo.RPT_TYPE")%>;--业务类型
	if(t_sBusiType == nil)
	then
		t_sBusiType = 0;
	end
	t_sDedType	=	<%get_struct_value('pSP', "MXdr::SSjSpInfo.USER_TYPE")%>; --核减类型
	if(t_sDedType == nil)
	then
		t_sDedType = 0;
	end
	t_sDealSeq	=	<%get_struct_value('pSP', "MXdr::SSjSpInfo.AREA_CODE")%>;	--工单流水号
	if(t_sDealSeq == nil)
	then
		t_sDealSeq = "";
	end
	t_sChargeDN 	=	<%get_struct_value('pSubCommon', "MXdr::SSubCommon.ODN")%>;--计费用户号码
	if(t_sChargeDN == nil)
	then
		t_sChargeDN = "";
	end
	t_sSpCode	=	<%get_struct_value('pSP', "MXdr::SSjSpInfo.SP_CODE")%>;--SP代码
	if(t_sSpCode == nil)
	then
		t_sSpCode = "";
	end
	t_sOperCode 	=	<%get_struct_value('pSP', "MXdr::SSjSpInfo.OPER_CODE")%>;--业务代码
	if(t_sOperCode == nil)
	then
		t_sOperCode = "";
	end
	t_sPropCode 	=<%get_struct_value('pSP', "MXdr::SSjSpInfo.SERV_CODE")%>;--道具代码
	if(t_sPropCode == nil)
	then
		t_sPropCode = "";
	end
	t_sChannelCode 	=<%get_struct_value('pSP', "MXdr::SSjSpInfo.CH_CODE")%>;--渠道代码
	if(t_sChannelCode == nil)
	then
		t_sChannelCode = "";
	end
	t_sChargeType	=	<%get_struct_value('pSP', "MXdr::SSjSpInfo.CHARGE_TYPE")%>;--计费类型
	if(t_sChargeType == nil)
	then
		t_sChargeType = 0;
	end
	t_sStartTime	=	<%get_struct_value('pSubCommon', "MXdr::SSubCommon.START_TIME")%>;	--业务使用时间
	if(t_sStartTime == nil)
	then
		t_sStartTime = "";
	end
	t_sDedTime	=	<%get_struct_value('pSubCommon', "MXdr::SSubCommon.FINISH_TIME")%>; --核减时间
	if(t_sDedTime == nil)
	then
		t_sDedTime = "";
	end
	t_sDedFee	=	<%get_struct_value('pSP', "MXdr::SSjSpInfo.INFO_FEE")%>; --核减金额
	if(t_sDedFee == nil)
	then
		t_sDedFee = 0;
	end
	t_sCdrtype	=	<%get_struct_value('PXdr', "MXdr::SXdr.ORI_FILE_TYPE")%>;--话单类型
	if(t_sCdrtype == nil)
	then
		t_sCdrtype = 0;
	end
	t_sContendCode	=	<%get_struct_value('pSP', "MXdr::SSjSpInfo.DEV_CODE")%>;--内容编码
	if(t_sContendCode == nil)
	then
		t_sContendCode = "";
	end
	t_sOrdernum	=	<%get_struct_value('pSP', "MXdr::SSjSpInfo.SONG_ID")%>;--订单编号
	if(t_sOrdernum == nil)
	then
		t_sOrdernum = "";
	end
	t_sBillcode	=	<%get_struct_value('pSP', "MXdr::SSjSpInfo.SMSC_CODE")%>;--计费事务代码
	if(t_sBillcode == nil)
	then
		t_sBillcode = "";
	end
	t_sChargeId =   <%get_struct_value('pSP', "MXdr::SSjSpInfo.RING_PLATFORM_ID")%>;--充值序列号
	if(t_sChargeId == nil)
	then
		t_sChargeId = "";
	end
	t_sMsgId =   <%get_struct_value('pSP', "MXdr::SSjSpInfo.MSG_ID")%>;--话单序列号
	if(t_sMsgId == nil)
	then
		t_sMsgId = "";
	end
	t_sMarkId =   <%get_struct_value('pSP', "MXdr::SSjSpInfo.RING_ID")%>;--营销ID
	if(t_sMarkId == nil)
	then
		t_sMarkId = "";
	end

		t_iResult	= 0;
	return;
	
end


function upload_main()
	if iDoPrintInfo == 1 then
		logtrace('------------------------------upload_mread_main begin--------------------------------');
	end

	--0:head 1:tail 2:body 3:null body 4:filename 
	if(t_iLocation == 0) then
		head_record();
	elseif(t_iLocation == 1) then
		tail_record();
	elseif(t_iLocation == 2) then
		init_getsdlval();
		body_record();
	elseif(t_iLocation == 3) then
		null_body_record();
	elseif(t_iLocation == 4) then
		file_name();
	end

	if iDoPrintInfo == 1 then
		logtrace('------------------------------upload_mread_main end--------------------------------');
	end
end


function error_deal(pSubCommon,sErrorCode)
	<%set_struct_value('pFileOpInfo', "MFileOp::SFileOp.TREAT_FLAG",1)%>
	<%set_struct_value('pFileOpInfo',"MFileOp::SFileOp.ERROR_CODE",sErrorCode) %>
	logtrace("error_deal,errocode:"..sErrorCode)
	if(t_iRecType == nil)
	then
		t_iRecType = 0;
	end
	
	if(t_sBusiType == nil)
	then
		t_sBusiType = 0;
	end
	if(t_sDedType == nil)
	then
		t_sDedType = 0;
	end
	if(t_sDealSeq == nil)
	then
		t_sDealSeq = "";
	end
	if(t_sChargeDN == nil)
	then
		t_sChargeDN = "";
	end
	if(t_sSpCode == nil)
	then
		t_sSpCode = "";
	end
	if(t_sOperCode == nil)
	then
		t_sOperCode = "";
	end
	if(t_sPropCode == nil)
	then
		t_sPropCode = "";
	end
	if(t_sChannelCode == nil)
	then
		t_sChannelCode = "";
	end
	if(t_sChargeType == nil)
	then
		t_sChargeType = 0;
	end
	if(t_sStartTime == nil)
	then
		t_sStartTime = "";
	end
	if(t_sDedTime == nil)
	then
		t_sDedTime = "";
	end
	if(t_sDedFee == nil)
	then
		t_sDedFee = 0;
	end
	if(t_sCdrtype == nil)
	then
		t_sCdrtype = 0;
	end
	if(t_sContendCode == nil)
	then
		t_sContendCode = "";
	end
	if(t_sOrdernum == nil)
	then
		t_sOrdernum = "";
	end
	if(t_sBillcode == nil)
	then
		t_sBillcode = "";
	end
	if(t_sChargeId == nil)
	then
		t_sChargeId = "";
	end
	if(t_sMsgId == nil)
	then
		t_sMsgId = "";
	end
	if(t_sMarkId == nil)
	then
		t_sMarkId = "";
	end
	
	--!累加错单记录
	t_iErrorNumber	= t_iErrorNumber + 1;
	t_sOutRecord = tostring(t_iRecType)
			.. rpad(t_sBusiType, " ",10)
			.. lpad(t_sDedType, "0", 2)
			.. rpad(t_sDealSeq, " ",20)
			.. rpad(t_sChargeDN, " ",15)
			.. rpad(t_sSpCode, " ",20)			
			.. rpad(t_sOperCode, " ",20)
			.. rpad(t_sPropCode, " ",12)
			.. rpad(t_sChannelCode, " ",40)
			.. lpad(t_sChargeType, "0",2)
			.. rpad(t_sStartTime, " ", 14)
			.. rpad(t_sDedTime, " ", 14)
			.. lpad(t_sDedFee, "0", 6)
			.. rpad(t_sCdrtype, " ", 2)
			.. rpad(t_sContendCode, " ", 12)
			.. rpad(t_sOrdernum, " ", 64)	
			.. rpad(t_sBillcode, " ", 22)	
			.. rpad(t_sChargeId," ",32)	
			.. rpad(t_sMsgId," ",25)
			.. rpad(t_sMarkId, " ", 20)
			.. rpad(t_sEmpty, " ", 16)			
			;
		 	
	t_iResult	= 1;
end

