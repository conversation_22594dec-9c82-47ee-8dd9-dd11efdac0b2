# -*- coding: utf-8 -*-
import psycopg2
from datetime import datetime
from cmi_common_util import *

# 目标表
m_targettable = 'BPS_ADD_OPERATOR'

# 获取 IN条件中的值, sts_type区分客户还是供应商1-供应商,2-客户
def getExistingOperCodes(gsConn, sts_type):
    gs_cursor = gsConn.cursor()
    query_operCode = "SELECT oper_code FROM bps_add_operator WHERE oper_type = 70 AND sts in ({0}) AND oper_code IS NOT NULL".format(sts_type)
    gs_cursor.execute(query_operCode)
    oper_codes = gs_cursor.fetchall()
    # 转换为集合以提高查找效率
    return {str(row[0]) for row in oper_codes}


def queryData(oraConn, querySql):
    # print("squerySql:", querySql)
    ora_cursor = oraConn.cursor()
    ora_cursor.execute(querySql)
    data = ora_cursor.fetchall()
    ora_cursor.close()

    print("select data:", len(data))
    return data

def insertData(gsConn, data):
    gs_cursor = gsConn.cursor()
    insert_query = """
        INSERT INTO {target_table} (
                oper_id, oper_type, oper_code, oper_name, sts, run_sts, numeric, alpha_numeric, short_code, delivery_receipt, sts_time
            ) VALUES (nextval('bps_add_operator$seq'), %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """.format(target_table=m_targettable)

    fliter_count = 0
    for row in data:
        unique_id, party_name, sts = row
        if len(party_name) > 100:
            party_name = party_name[:100]
            fliter_count += 1
            # print("party_name to long, truncate to 100:", party_name)
        gs_cursor.execute(insert_query, (
            70,
            unique_id,
            party_name,
            sts,
            1,
            1,
            1,
            1,
            1,
            datetime.now().strftime('%Y%m%d')
        ))
    
    gs_cursor.close()
    gsConn.commit()

    # 获取插入的记录数
    print("Inserted {} {} records. filter_count: {}".format(m_targettable, len(data)-fliter_count, fliter_count  ))

def proc_sync_sms_operator_hour(gsConn, oraConn):  
    try:
        # 1. 获取已存在的供应商unique_id  
        existing_supplier_uids = getExistingOperCodes(gsConn, "0,1")

        # 2. O库查源数据（移除IN条件）
        querySql = """
            SELECT  UNIQUE_ID, PARTY_NAME, sts
            FROM (
                SELECT DISTINCT UNIQUE_ID, PARTY_NAME, 0 AS sts
                FROM cus.CM_STL_SMS_PARTY 
                UNION ALL
                SELECT DISTINCT UNIQUE_ID, ENTERPRISE_NAME AS PARTY_NAME, 1 AS sts
                from (select distinct decode(nvl(a.owner_name,'0'),'0', b.enterprise_name,b.enterprise_name || ' - ' || a.owner_name) enterprise_name, a.unique_id
                    from cus.cm_supplier_unique a,
                        cus.cm_enterprise      b,
                        cus.cm_party_role      c
                    left join cus.cm_acct d
                        on c.party_role_id = d.acct_id
                    and d.owner_type = 1
                    where a.supplier_id = c.party_role_id
                    and b.enterprise_id = c.party_id
                    and a.STATUS = '1'
                    and c.party_role_spec_id = 1)
                where unique_id is not null
            ) 
            WHERE UNIQUE_ID <> '1'
        """
        all_data = queryData(oraConn, querySql)
        
        # 在Python中过滤数据
        new_data = [row for row in all_data if str(row[0]) not in existing_supplier_uids]
        print("p2p and a2p supplier new_data:", len(new_data))
        # 3. 按条插入目标表
        insertData(gsConn, new_data)

        # 4. 处理A2P客户数据时也需要类似修改
        existing_cust_uids = getExistingOperCodes(gsConn, 2)  # 重新获取最新的已存在codes
        
        # 4.1 O库查 A2P客户相关源数据（移除IN条件）
        querySql = """
            SELECT  UNIQUE_ID, PARTY_NAME, sts
            FROM (
                select distinct UNIQUE_ID, enterprise_name as PARTY_NAME, 2 as sts
                from (select distinct decode(nvl(a.owner_name,'0'),'0', b.enterprise_name,b.enterprise_name || ' - ' || a.owner_name) enterprise_name, a.unique_id
                        from cus.cm_cust_unique a,
                            cus.cm_enterprise      b,
                            cus.cm_party_role      c
                        left join cus.cm_acct d
                            on c.party_role_id = d.acct_id
                        and d.owner_type = 0   --客户
                        where a.cust_id = c.party_role_id
                        and b.enterprise_id = c.party_id
                        and a.STATUS != '0'
                        and c.party_role_spec_id = 0)
                where unique_id is not null
            ) 
            WHERE UNIQUE_ID <> '1'
        """
        all_data = queryData(oraConn, querySql)
        
        # 在Python中过滤数据
        new_data = [row for row in all_data if str(row[0]) not in existing_cust_uids]
        print("A2P customer new_data:", len(new_data))
        
        # 4.2 按条插入目标表
        # 会引起批价MO转换,取消入表 -- by liwj 0626
        #insertData(gsConn, new_data)

    except (cx_Oracle.Error, psycopg2.Error) as exc:
        if isinstance(exc, cx_Oracle.Error):
            error, = exc.args
            print("Oracle-Error-Code:", error.code)
            print("Oracle-Error-Message:", error.message)
        elif isinstance(exc, psycopg2.Error):
            print("Gauss-Error-Code:", exc.pgcode)
            print("Gauss-Error-Message:", exc.pgerror)
    except Exception as e:
        print("An error occurred:", e)


if __name__ == "__main__":
    start_time = datetime.now()  
    print("start run proc_sync_sms_operator_hour: {}".format(start_time.strftime('%Y-%m-%d %H:%M:%S')))  
    # 连接到PostgreSQL数据库
    gsConn = get_gs_connect("GS_DBLINK")
    if gsConn is None:
        print("get Gaussdb connect failed!!!")
        exit(-1)

    # 连接到Oracle数据库
    oraConn = get_o_connect()
    if oraConn is None:
        print("get Oracle connect failed!!!")
        exit(-1)
    
    # 调用函数
    proc_sync_sms_operator_hour(gsConn, oraConn)
    
    # 关闭数据库连接
    gsConn.close()
    oraConn.close()

    end_time = datetime.now()  
    print("proc_sync_sms_operator_hour end: {}".format(end_time.strftime('%Y-%m-%d %H:%M:%S')))  

    # 计算总耗时  
    total_time = end_time - start_time  
    total_seconds = total_time.total_seconds() 
    print("proc_sync_sms_operator_hour cost time: {}s".format(total_seconds)) 
