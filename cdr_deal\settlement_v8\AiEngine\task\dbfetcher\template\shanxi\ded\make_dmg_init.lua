require "dbfetcher_util"

function main(args)
    -- 获取数据库连接
    local conn = getConnect()
    -- 获取任务 ID
    local inTaskId = getCode(args, "TASK_ID")

    writeLog("开始处理")

    -- 获取 BILL_MONTH 参数
    billMonth = getBillMonth(conn, inTaskId)
    
    -- 如果获取不到，返回错误
    if not billMonth then
        writeLog("未提供 BILL_MONTH 参数")
        return -1
    end

    -- 实际结算月份等于出账月份前两月，取YYYYMM
    settleMonth = string.sub(getAdjustDate(billMonth, -2), 1, 6)  

    writeLog("BILL_MONTH:" .. billMonth .. "  实际结算月份:" .. settleMonth)

    -- 删除旧数据 为后续的make_dmg_day和make_dmg_month预清理表数据
    local sql = [[DELETE FROM jsdr.dr_dmg_audit_inner_]] .. settleMonth .. [[ h 
                 WHERE h.audit_type = '05']]
    writeLog("删除audit_type=05的旧数据")
    executeSQL(conn, sql)
    conn:commit()

    -- 删除bps_user_platsvc_stash中当前结算月的旧数据
    local deleteSql = [[DELETE FROM jsbd.bps_user_platsvc_stash h WHERE h.settle_month = ']] .. settleMonth .. [[';]]
    writeLog("开始删除jsbd.bps_user_platsvc_stash中settle_month=" .. settleMonth .. "的旧数据")
    executeSQL(conn, deleteSql)
    conn:commit()
    writeLog("jsbd.bps_user_platsvc_stash旧数据删除完成")

    -- 计算上个月的年月（YYYYMM）
    local upMonth = string.sub(getAdjustDate(settleMonth, -2), 1, 6)
    writeLog("当前结算月:" .. settleMonth .. "  上溯两个月:" .. upMonth)

    -- 插入bps_user_platsvc_stash数据  为make_dmg_day准备中间数据
    local insertSql = [[
        INSERT INTO jsbd.bps_user_platsvc_stash
          (settle_month, biz_code, biz_name, biz_type, sp_code, user_id)
        SELECT ]] .. settleMonth .. [[, t.biz_code, t.biz_name, t.biz_type, t.sp_code, t.user_id 
        FROM (
            SELECT a.biz_code, a.biz_name, a.biz_type, a.sp_code, c.user_id, c.start_date
            FROM jsbd.bps_pm_sp_service      a,
                 jsbd.bps_pm_offer           b,
                 jsbd.bps_tf_f_user_platsvc_]] .. settleMonth .. [[ c
            WHERE a.biz_code IN ('10083505','10000410','69804051000221')
              AND b.offer_id = a.sp_service_id
              AND c.service_id = b.offer_code
              AND SUBSTR(TO_CHAR(c.start_date, 'YYYYMMDDHH24MISS'), 1, 6) <= ']] .. settleMonth .. [['
              AND SUBSTR(TO_CHAR(c.start_date, 'YYYYMMDDHH24MISS'), 1, 6) >= ']] .. upMonth .. [['
              AND SUBSTR(TO_CHAR(c.end_date, 'YYYYMMDDHH24MISS'), 1, 6) >= ']] .. settleMonth .. [['    
            GROUP BY a.biz_code, a.biz_name, a.biz_type, a.sp_code, c.user_id, c.start_date    
        ) t
        GROUP BY t.biz_code, t.biz_name, t.biz_type, t.sp_code, t.user_id
    ]]
    writeLog("开始插入bps_user_platsvc_stash数据")
    executeSQL(conn, insertSql)
    conn:commit()
    writeLog("bps_user_platsvc_stash数据插入完成")
    ------------------------------------------------------
    writeLog("处理完成")
    conn:close()
    return 0
end

environment = os.getenv("ENVIRONMENT")
if environment == "TEST" then
    main("TASK_ID=17990|BILL_MONTH=202502")
    os.exit()
end
