#include "table_struct_define.h"

// ============================================================================
// 工具函数实现
// ============================================================================

// 将时间戳转换为 otl_datetime
otl_datetime intToOtlDatetime(int timestamp) {
    time_t rawtime = static_cast<time_t>(timestamp);
    struct tm *timeinfo = localtime(&rawtime);

    otl_datetime datetime;
    datetime.year = timeinfo->tm_year + 1900;
    datetime.month = timeinfo->tm_mon + 1;
    datetime.day = timeinfo->tm_mday;
    datetime.hour = timeinfo->tm_hour;
    datetime.minute = timeinfo->tm_min;
    datetime.second = timeinfo->tm_sec;

    return datetime;
}

// ============================================================================
// CTabelStructBase 友元函数实现
// ============================================================================

otl_stream& operator>>(otl_stream& is, CTabelStructBase& data) {
    data.readFromStream(is);
    return is;
}

otl_stream& operator<<(otl_stream& os, const CTabelStructBase& data) {
    data.writeToStream(os);
    return os;
}

std::ostream& operator<<(std::ostream& os, const CTabelStructBase& data) {
    data.printToStream(os);
    return os;
}

// ============================================================================
// CIDataIndex 实现
// ============================================================================

int32 CIDataIndex::get_syncFlag() {
    return m_iSyncFlag;
}

AISTD string CIDataIndex::get_strPhoneID() {
    return m_strPhoneID;
}

void CIDataIndex::readFromStream(otl_stream& is) {
    is >> m_strPhoneID
        >> m_strUpField
        >> m_iSyncFlag
        >> m_lBusiCode
        >> m_iRegionCode
        >> m_iCountyCode
        >> m_lOpID
        >> m_strCommitDate
        >> m_lCommitTime
        >> m_lSoNbr
        >> m_strRemark;
}

void CIDataIndex::writeToStream(otl_stream& os) const {
    os << m_strPhoneID
        << m_strUpField
        << m_iSyncFlag
        << m_lBusiCode
        << m_iRegionCode
        << m_iCountyCode
        << m_lOpID
        << intToOtlDatetime(m_lCommitTime)
        << m_lSoNbr
        << m_strRemark;
}

void CIDataIndex::printToStream(std::ostream& os) const {
    os << "m_strPhoneID=" << m_strPhoneID << ';'
       << " m_strUpField=" << m_strUpField << ';'
       << " m_iSyncFlag=" << m_iSyncFlag << ';'
       << " m_lBusiCode=" << m_lBusiCode << ';'
       << " m_iRegionCode=" << m_iRegionCode << ';'
       << " m_iCountyCode=" << m_iCountyCode << ';'
       << " m_lOpID=" << m_lOpID << ';'
       << " m_lCommitTime=" << m_lCommitTime << ';'
       << " m_lSoNbr=" << m_lSoNbr << ';'
       << " m_strRemark=" << m_strRemark << ';'
       << '\n';
}

// CIDataIndex 友元函数实现
otl_stream& operator>>(otl_stream& os, CIDataIndex& data) {
    data.readFromStream(os);
    return os;
}

otl_stream& operator<<(otl_stream& os, const CIDataIndex& data) {
    data.writeToStream(os);
    return os;
}

std::ostream& operator<<(std::ostream& os, const CIDataIndex& data) {
    data.printToStream(os);
    return os;
}

// ============================================================================
// CIDataIndexHis 实现
// ============================================================================

int32 CIDataIndexHis::get_syncFlag() {
    return m_iSyncFlag;
}

AISTD string CIDataIndexHis::get_strPhoneID() {
    return m_strPhoneID;
}

void CIDataIndexHis::readFromStream(otl_stream& is) {
    is >> m_strPhoneID >> m_strUpField >> m_iSyncFlag >>
       m_lBusiCode >> m_iRegionCode >> m_iCountyCode >>
       m_lOpID >> m_lCommitTime >> m_lSoNbr >>
       m_lDealTime >> m_strRemark;
}

void CIDataIndexHis::writeToStream(otl_stream& os) const {
    os << m_strPhoneID << m_strUpField << m_iSyncFlag
       << m_lBusiCode << m_iRegionCode << m_iCountyCode
       << m_lOpID << intToOtlDatetime(m_lCommitTime) << m_lSoNbr
       << intToOtlDatetime(m_lDealTime) << m_strRemark;
}

void CIDataIndexHis::printToStream(std::ostream& os) const {
    os << "m_strPhoneID=" << m_strPhoneID << ';'
       << " m_strUpField=" << m_strUpField << ';'
       << " m_iSyncFlag=" << m_iSyncFlag << ';'
       << " m_lBusiCode=" << m_lBusiCode << ';'
       << " m_iRegionCode=" << m_iRegionCode << ';'
       << " m_iCountyCode=" << m_iCountyCode << ';'
       << " m_lOpID=" << m_lOpID << ';'
       << " m_lCommitTime=" << m_lCommitTime << ';'
       << " m_lSoNbr=" << m_lSoNbr << ';'
       << " m_lDealTime=" << m_lDealTime << ';'
       << " m_strRemark=" << m_strRemark << ';'
       << '\n';
}

// CIDataIndexHis 友元函数实现
otl_stream& operator>>(otl_stream& os, CIDataIndexHis& data) {
    data.readFromStream(os);
    return os;
}

otl_stream& operator<<(otl_stream& os, const CIDataIndexHis& data) {
    data.writeToStream(os);
    return os;
}

std::ostream& operator<<(std::ostream& os, const CIDataIndexHis& data) {
    data.printToStream(os);
    return os;
}

// ============================================================================
// CIDataIndexErr 实现
// ============================================================================

int32 CIDataIndexErr::get_syncFlag() {
    return m_iSyncFlag;
}

AISTD string CIDataIndexErr::get_strPhoneID() {
    return m_strPhoneID;
}

void CIDataIndexErr::readFromStream(otl_stream& is) {
    is >> m_strPhoneID >> m_strUpField >> m_iSyncFlag >>
       m_lBusiCode >> m_iRegionCode >> m_iCountyCode >>
       m_lOpID >> m_lCommitTime >> m_lSoNbr >>
       m_iErrCode >> m_strErrMsg >> m_lDealTime >> m_strRemark;
}

void CIDataIndexErr::writeToStream(otl_stream& os) const {
    os << m_strPhoneID
       << m_strUpField
       << m_iSyncFlag
       << m_lBusiCode
       << m_iRegionCode
       << m_iCountyCode
       << m_lOpID
       << intToOtlDatetime(m_lCommitTime)
       << m_lSoNbr
       << m_iErrCode
       << m_strErrMsg
       << intToOtlDatetime(m_lDealTime)
       << m_strRemark;
}

void CIDataIndexErr::printToStream(std::ostream& os) const {
    os << "m_strPhoneID=" << m_strPhoneID << ';'
       << " m_strUpField=" << m_strUpField << ';'
       << " m_iSyncFlag=" << m_iSyncFlag << ';'
       << " m_lBusiCode=" << m_lBusiCode << ';'
       << " m_iRegionCode=" << m_iRegionCode << ';'
       << " m_iCountyCode=" << m_iCountyCode << ';'
       << " m_lOpID=" << m_lOpID << ';'
       << " m_lCommitTime=" << m_lCommitTime << ';'
       << " m_lSoNbr=" << m_lSoNbr << ';'
       << " m_iErrCode=" << m_iErrCode << ';'
       << " m_strErrMsg=" << m_strErrMsg << ';'
       << " m_lDealTime=" << m_lDealTime << ';'
       << " m_strRemark=" << m_strRemark << ';'
       << '\n';
}

// CIDataIndexErr 友元函数实现
otl_stream& operator>>(otl_stream& os, CIDataIndexErr& data) {
    data.readFromStream(os);
    return os;
}

otl_stream& operator<<(otl_stream& os, const CIDataIndexErr& data) {
    data.writeToStream(os);
    return os;
}

std::ostream& operator<<(std::ostream& os, const CIDataIndexErr& data) {
    data.printToStream(os);
    return os;
}

// ============================================================================
// CIMsisdnInfo 实现
// ============================================================================

int32 CIMsisdnInfo::get_syncFlag() {
    return m_iSyncFlag;
}

AISTD string CIMsisdnInfo::get_strPhoneID() {
    return m_strPhoneID;
}

int32 CIMsisdnInfo::get_validDate() 
{
    return m_lValidDate;
}

void CIMsisdnInfo::readFromStream(otl_stream& is) {
    is >> m_strPhoneID >> m_strCountyCode >> m_strGridCode >>
       m_iAreaCode >> m_strBureauCode >> m_iUserType >>
       m_lValidDate >> m_lExpireDate >> m_strReserved1 >>
       m_strReserved2 >> m_strReserved3 >> m_lSoNbr >>
       m_iSyncFlag >> m_lCommitTime >> m_strRemark;
}

void CIMsisdnInfo::writeToStream(otl_stream& os) const {
    os << m_strPhoneID << m_strCountyCode << m_strGridCode
       << m_iAreaCode << m_strBureauCode << m_iUserType
       << intToOtlDatetime(m_lValidDate) << intToOtlDatetime(m_lExpireDate)
       << m_strReserved1 << m_strReserved2 << m_strReserved3
       << m_lSoNbr << m_iSyncFlag << intToOtlDatetime(m_lCommitTime) << m_strRemark;
}

void CIMsisdnInfo::printToStream(std::ostream& os) const {
    os << "m_strPhoneID=" << m_strPhoneID << ';'
       << " m_strCountyCode=" << m_strCountyCode << ';'
       << " m_strGridCode=" << m_strGridCode << ';'
       << " m_iAreaCode=" << m_iAreaCode << ';'
       << " m_strBureauCode=" << m_strBureauCode << ';'
       << " m_iUserType=" << m_iUserType << ';'
       << " m_lValidDate=" << m_lValidDate << ';'
       << " m_lExpireDate=" << m_lExpireDate << ';'
       << " m_strReserved1=" << m_strReserved1 << ';'
       << " m_strReserved2=" << m_strReserved2 << ';'
       << " m_strReserved3=" << m_strReserved3 << ';'
       << " m_lSoNbr=" << m_lSoNbr << ';'
       << " m_iSyncFlag=" << m_iSyncFlag << ';'
       << " m_lCommitTime=" << m_lCommitTime << ';'
       << " m_strRemark=" << m_strRemark << ';'
       << '\n';
}

// CIMsisdnInfo 友元函数实现
otl_stream& operator>>(otl_stream& os, CIMsisdnInfo& data) {
    data.readFromStream(os);
    return os;
}

otl_stream& operator<<(otl_stream& os, const CIMsisdnInfo& data) {
    data.writeToStream(os);
    return os;
}

std::ostream& operator<<(std::ostream& os, const CIMsisdnInfo& data) {
    data.printToStream(os);
    return os;
}

// ============================================================================
// CINationalMnp 实现
// ============================================================================

int32 CINationalMnp::get_syncFlag() {
    return m_iSyncFlag;
}

AISTD string CINationalMnp::get_strPhoneID() {
    return m_strPhoneID;
}

int32 CINationalMnp::get_validDate(){
    return m_lValidDate;
}


void CINationalMnp::readFromStream(otl_stream& is) {
    is >> m_strPhoneID >> m_strSrcNetID >> m_strDesNetID >>
       m_strOwnNetID >> m_lValidDate >> m_lExpireDate >>
       m_iSyncFlag >> m_lSoNbr >> m_lCommitTime >> m_strRemark;
}

void CINationalMnp::writeToStream(otl_stream& os) const {
    os << m_strPhoneID << m_strSrcNetID << m_strDesNetID << m_strOwnNetID
       << intToOtlDatetime(m_lValidDate) << intToOtlDatetime(m_lExpireDate)
       << m_iSyncFlag << m_lSoNbr << intToOtlDatetime(m_lCommitTime) << m_strRemark;
}

void CINationalMnp::printToStream(std::ostream& os) const {
    os << "m_strPhoneID=" << m_strPhoneID << ';'
       << " m_strSrcNetID=" << m_strSrcNetID << ';'
       << " m_strDesNetID=" << m_strDesNetID << ';'
       << " m_strOwnNetID=" << m_strOwnNetID << ';'
       << " m_lValidDate=" << m_lValidDate << ';'
       << " m_lExpireDate=" << m_lExpireDate << ';'
       << " m_iSyncFlag=" << m_iSyncFlag << ';'
       << " m_lSoNbr=" << m_lSoNbr << ';'
       << " m_lCommitTime=" << m_lCommitTime << ';'
       << " m_strRemark=" << m_strRemark << ';'
       << '\n';
}

// CINationalMnp 友元函数实现
otl_stream& operator>>(otl_stream& os, CINationalMnp& data) {
    data.readFromStream(os);
    return os;
}

otl_stream& operator<<(otl_stream& os, const CINationalMnp& data) {
    data.writeToStream(os);
    return os;
}

std::ostream& operator<<(std::ostream& os, const CINationalMnp& data) {
    data.printToStream(os);
    return os;
}

// ============================================================================
// CBpsMsisdnInfo 实现
// ============================================================================

AISTD string CBpsMsisdnInfo::get_strPhoneID() {
    return m_strMsisdn; // 使用 m_strMsisdn 作为 PhoneID
}

void CBpsMsisdnInfo::convertI2Bps(const CTabelStructBase& iData)
{
    const CIMsisdnInfo* pIMsisdn = dynamic_cast<const CIMsisdnInfo*>(&iData);
    if (pIMsisdn) {
        m_strMsisdn     = pIMsisdn->m_strPhoneID;
        m_strCountyCode = pIMsisdn->m_strCountyCode;
        m_strGridCode   = pIMsisdn->m_strGridCode;
        m_iAreaCode     = pIMsisdn->m_iAreaCode;
        m_strBureauCode = pIMsisdn->m_strBureauCode;
        m_iUserType     = pIMsisdn->m_iUserType;
        m_iValidDate    = pIMsisdn->m_lValidDate;
        m_iExpireDate   = pIMsisdn->m_lExpireDate;
        m_strReserved1  = pIMsisdn->m_strReserved1;
        m_strReserved2  = pIMsisdn->m_strReserved2;
        m_strReserved3  = pIMsisdn->m_strReserved3;
    } else {
        LOG_ERROR(0, "Invalid type conversion in CBpsMsisdnInfo::convertFromBase");
    }
}

void CBpsMsisdnInfo::readFromStream(otl_stream& is) {
    is >> m_strMsisdn >> m_strCountyCode >> m_strGridCode >>
       m_iAreaCode >> m_strBureauCode >> m_iUserType >>
       m_iValidDate >> m_iExpireDate >> m_strReserved1 >>
       m_strReserved2 >> m_strReserved3;
}

void CBpsMsisdnInfo::writeToStream(otl_stream& os) const {
    os << m_strMsisdn
       << m_strCountyCode
       << m_strGridCode
       << m_iAreaCode
       << m_strBureauCode
       << m_iUserType
       << m_iValidDate
       << m_iExpireDate
       << m_strReserved1
       << m_strReserved2
       << m_strReserved3;
}

void CBpsMsisdnInfo::printToStream(std::ostream& os) const {
    os << "m_strMsisdn=" << m_strMsisdn << ';'
       << " m_strCountyCode=" << m_strCountyCode << ';'
       << " m_strGridCode=" << m_strGridCode << ';'
       << " m_iAreaCode=" << m_iAreaCode << ';'
       << " m_strBureauCode=" << m_strBureauCode << ';'
       << " m_iUserType=" << m_iUserType << ';'
       << " m_iValidDate=" << m_iValidDate << ';'
       << " m_iExpireDate=" << m_iExpireDate << ';'
       << " m_strReserved1=" << m_strReserved1 << ';'
       << " m_strReserved2=" << m_strReserved2 << ';'
       << " m_strReserved3=" << m_strReserved3 << ';'
       << '\n';
}

// CBpsMsisdnInfo 友元函数实现
otl_stream& operator>>(otl_stream& os, CBpsMsisdnInfo& data) {
    data.readFromStream(os);
    return os;
}

otl_stream& operator<<(otl_stream& os, const CBpsMsisdnInfo& data) {
    data.writeToStream(os);
    return os;
}

std::ostream& operator<<(std::ostream& os, const CBpsMsisdnInfo& data) {
    data.printToStream(os);
    return os;
}

// ============================================================================
// CBpsNationMnp 实现
// ============================================================================


AISTD string CBpsNationMnp::get_strPhoneID() {
    return m_strPhoneNumber; // 使用 m_strPhoneNumber 作为 PhoneID
}

void CBpsNationMnp::convertI2Bps(const CTabelStructBase& iData){
    const CINationalMnp* pINationalMnp = dynamic_cast<const CINationalMnp*>(&iData);
    if (pINationalMnp) {
        m_strPhoneNumber = pINationalMnp->m_strPhoneID;
        m_strSrcNetID    = pINationalMnp->m_strSrcNetID;
        m_strDestNetID   = pINationalMnp->m_strDesNetID;
        m_strOwnNetID    = pINationalMnp->m_strOwnNetID;
        m_iValidDate     = pINationalMnp->m_lValidDate;
        m_iExpireDate    = pINationalMnp->m_lExpireDate;
    } else {
        LOG_ERROR(0, "Invalid type conversion in CBpsNationMnp::convertFromBase");
    }
}

void CBpsNationMnp::readFromStream(otl_stream& is) {
    is >> m_strPhoneNumber >> m_strSrcNetID >> m_strDestNetID >>
       m_strOwnNetID >> m_iValidDate >> m_iExpireDate;
}

void CBpsNationMnp::writeToStream(otl_stream& os) const {
    os << m_strPhoneNumber
       << m_strSrcNetID
       << m_strDestNetID
       << m_strOwnNetID
       << m_iValidDate
       << m_iExpireDate;
}

void CBpsNationMnp::printToStream(std::ostream& os) const {
    os << "m_strPhoneNumber=" << m_strPhoneNumber << ';'
       << " m_strSrcNetID=" << m_strSrcNetID << ';'
       << " m_strDestNetID=" << m_strDestNetID << ';'
       << " m_strOwnNetID=" << m_strOwnNetID << ';'
       << " m_iValidDate=" << m_iValidDate << ';'
       << " m_iExpireDate=" << m_iExpireDate << ';'
       << '\n';
}

// CBpsNationMnp 友元函数实现
otl_stream& operator>>(otl_stream& os, CBpsNationMnp& data) {
    data.readFromStream(os);
    return os;
}

otl_stream& operator<<(otl_stream& os, const CBpsNationMnp& data) {
    data.writeToStream(os);
    return os;
}

std::ostream& operator<<(std::ostream& os, const CBpsNationMnp& data) {
    data.printToStream(os);
    return os;
}
