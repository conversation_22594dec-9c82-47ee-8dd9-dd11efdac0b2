﻿#include "expr_comm_analyse_func.h"
#include "rpl_tls_var.h"
#include "expr_common_func.h"
#include "expr_rpl_type_def.h"
#include <base_func.h>
#include <string.h>

const    int    const_oppNumberSuit    = 1;
const    int    const_aNumberSuit    = 4;
const     int     const_promListLen    = 1024;

using namespace std;

// 20070705 b huangyl add
time_t str2time (const AISTD string& dt)
{
    struct tm tt;
    memset(&tt,0,sizeof(tt));
    sscanf (dt.c_str (), "%4d%2d%2d%2d%2d%2d",
        &tt.tm_year,
        &tt.tm_mon,
        &tt.tm_mday,
        &tt.tm_hour,
        &tt.tm_min,
        &tt.tm_sec);

    tt.tm_year = tt.tm_year - 1900;
    tt.tm_mon = tt.tm_mon - 1;

    return mktime(&tt);
}
char* ltoa(long n) {static char s[21]; sprintf(s, "%ld", n); return s;}
time_t StringToDatetime(std::string str)
{
        tm tm_;                                    
        int year, month, day, hour, minute, second;
        year = atoi((str.substr(0, 4)).c_str());
        month = atoi((str.substr(4, 2)).c_str());
        day = atoi((str.substr(6, 2)).c_str());
        hour = atoi((str.substr(8, 2)).c_str());
        minute = atoi((str.substr(10, 2)).c_str());
        second = atoi((str.substr(12, 2)).c_str());

        tm_.tm_year = year - 1900;        
        tm_.tm_mon = month - 1;   
        tm_.tm_mday = day;
        tm_.tm_hour = hour; 
        tm_.tm_min = minute;
        tm_.tm_sec = second;
        tm_.tm_isdst = 0; 
        time_t t_ = mktime(&tm_); 
        return t_;
}

int32 LogAppend_lua(lua_State *L)
{
    //lua param begin
    const    char*    pLogMsg = lua_tostring(L, 1);
    //end
        
    const int iResultCount = 0;
                
    REPORT_TRACE(pLogMsg);
    
    //lua out    begin
    //end
    return iResultCount;
}

int32   SMSNumberAnalysis_lua(lua_State *L)
{
        int32 iLuaParamIdx = 1;
        const char * p_pchNumber= get_str_param(L,iLuaParamIdx);
        const char * p_pArea= get_str_param(L,iLuaParamIdx);
        //int32 iStartTime = get_str_param(L,iLuaParamIdx);
        int64   iStartTime = get_int64_param(L,iLuaParamIdx);
        REPORT_TRACE("ANASYS SMSNumberAnalysis_lua IN p_pchNumber=<%s>",p_pchNumber);
        REPORT_TRACE("ANASYS SMSNumberAnalysis_lua IN p_pArea=<%s>",p_pArea);
        REPORT_TRACE("ANASYS SMSNumberAnalysis_lua IN iStartTime=<%ld>",iStartTime);    
       
        AISTD string strRecTime(ltoa(iStartTime));
        if (strRecTime.size() < 14)
        {
            string strTemp("20000101000000");
            strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
        }
        time_t tm=StringToDatetime(strRecTime);
        SMSNumberInfo p_pSNumber;
        memset(&p_pSNumber,0,sizeof(SMSNumberInfo));
        int iRes=SMSNumberAnalysisNew(GetCurrentSnap(L),p_pchNumber,p_pArea,tm,&p_pSNumber);
        iLuaParamIdx = 0;
        REPORT_TRACE("ANASYS SMSNumberAnalysis_lua out iRes=<%d>",iRes);
        REPORT_TRACE("ANASYS SMSNumberAnalysis_lua out m_iNetWork=<%d>",p_pSNumber.m_iNetWork);
        REPORT_TRACE("ANASYS SMSNumberAnalysis_lua out m_iOperatorParty=<%d>",p_pSNumber.m_iOperatorParty);
        REPORT_TRACE("ANASYS SMSNumberAnalysis_lua out m_szHomeAreaCode=<%s>",p_pSNumber.m_szHomeAreaCode);
        //lua out       BEGIN
        put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iNumberType);
        put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iNetWork);
        put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szProvId);
        put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iOperatorParty);
        put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szHomeAreaCode);
        //lua out       END
        return iLuaParamIdx;
}

int32    NumberAnalysis_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char * p_pchNumber= get_str_param(L,iLuaParamIdx);
    int nCallingType= get_int64_param(L,iLuaParamIdx);
    const char * p_pMscId= get_str_param(L,iLuaParamIdx);
    //int32    iStartTime = get_str_param(L,iLuaParamIdx);
    int64    iStartTime = get_int64_param(L,iLuaParamIdx);
    const char * p_pchCenterArea= get_str_param(L,iLuaParamIdx);
    int32 m_nServiceId = get_int64_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS NumberAnalysis_lua IN p_pchNumber=<%s>",p_pchNumber);
    REPORT_TRACE("ANASYS NumberAnalysis_lua IN nCallingType=<%d>",nCallingType);
    REPORT_TRACE("ANASYS NumberAnalysis_lua IN p_pMscId=<%s>",p_pMscId);
    REPORT_TRACE("ANASYS NumberAnalysis_lua IN iStartTime=<%ld>",iStartTime);
    REPORT_TRACE("ANASYS NumberAnalysis_lua IN p_pchCenterArea=<%s>",p_pchCenterArea);
    NumberInfo p_pSNumber;
    AISTD string strRecTime(ltoa(iStartTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&p_pSNumber,0,sizeof(NumberInfo));
    int iRes=NumberAnalysisNew(GetCurrentSnap(L),p_pchNumber,nCallingType,p_pMscId,tm,p_pchCenterArea,&p_pSNumber,m_nServiceId);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS NumberAnalysis_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_iNumberType=<%d>",p_pSNumber.m_iNumberType);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_iAccessFlag=<%d>",p_pSNumber.m_iAccessFlag);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_iAccessOperator=<%d>",p_pSNumber.m_iAccessOperator);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_szAccessNumber=<%s>",p_pSNumber.m_szAccessNumber);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_iNetWork=<%d>",p_pSNumber.m_iNetWork);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_iSpecialType=<%d>",p_pSNumber.m_iSpecialType);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_iSpecialId=<%d>",p_pSNumber.m_iSpecialId);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_szSpecialNumber=<%s>",p_pSNumber.m_szSpecialNumber);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_szHomeCountryCode=<%s>",p_pSNumber.m_szHomeCountryCode);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_iCountryType=<%d>",p_pSNumber.m_iCountryType);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_szProvId=<%s>",p_pSNumber.m_szProvId);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_iProvDefaultOp=<%d>",p_pSNumber.m_iProvDefaultOp);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_szHomeAreaCode=<%s>",p_pSNumber.m_szHomeAreaCode);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_szHomeBureauCode=<%s>",p_pSNumber.m_szHomeBureauCode);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_szHomeBusinessCode=<%s>",p_pSNumber.m_szHomeBusinessCode);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_szHomeRegionCode=<%s>",p_pSNumber.m_szHomeRegionCode);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_szOriginalNumber=<%s>",p_pSNumber.m_szOriginalNumber);
    REPORT_TRACE("ANASYS NumberAnalysis_lua out m_iOperatorParty=<%d>",p_pSNumber.m_iOperatorParty);
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iNumberType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iAccessFlag);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iAccessOperator);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szAccessNumber);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iNetWork);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iSpecialType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iSpecialId);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szSpecialNumber);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szHomeCountryCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iCountryType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szProvId);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iProvDefaultOp);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szHomeAreaCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szHomeBureauCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szHomeBusinessCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szHomeRegionCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szOriginalNumber);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iOperatorParty);
    
    //lua out    END

    return iLuaParamIdx;
}


int32    GetLongType_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    NumberInfo p_pSNumber;
    const char* p_FromAreaCode= get_str_param(L,iLuaParamIdx);
    const char* p_ToAreaCode= get_str_param(L,iLuaParamIdx);
    int64    iStartTime = get_int64_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS GetLongType_lua IN p_FromAreaCode=<%s>",p_FromAreaCode);
    REPORT_TRACE("ANASYS GetLongType_lua IN p_ToAreaCode=<%s>",p_ToAreaCode);
    REPORT_TRACE("ANASYS GetLongType_lua IN iStartTime=<%ld>",iStartTime);
    AISTD string strRecTime(ltoa(iStartTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    int iRes=getLongType(GetCurrentSnap(L),p_FromAreaCode,p_ToAreaCode,tm);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GetLongType_lua out iRes=<%d>",iRes);
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,iRes);
        
    //lua out    END
    return iLuaParamIdx;

}

int32    GetRoamType_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    NumberInfo p_pSNumber;
    const char * p_pRoamAreaCode= get_str_param(L,iLuaParamIdx);
    const char *p_pCallType= get_str_param(L,iLuaParamIdx);
    const char * m_szHomeCountryCode= get_str_param(L,iLuaParamIdx);
    const char *m_szAccessNumber= get_str_param(L,iLuaParamIdx);
    const char * m_szHomeAreaCode= get_str_param(L,iLuaParamIdx);
    const char *m_szProvId= get_str_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS GetRoamType_lua IN p_pRoamAreaCode=<%s>",p_pRoamAreaCode);
    REPORT_TRACE("ANASYS GetRoamType_lua IN p_pCallType=<%s>",p_pCallType);
    REPORT_TRACE("ANASYS GetRoamType_lua IN m_szHomeCountryCode=<%s>",m_szHomeCountryCode);
    REPORT_TRACE("ANASYS GetRoamType_lua IN m_szAccessNumber=<%s>",m_szAccessNumber);
    REPORT_TRACE("ANASYS GetRoamType_lua IN m_szHomeAreaCode=<%s>",m_szHomeAreaCode);
    REPORT_TRACE("ANASYS GetRoamType_lua IN m_szProvId=<%s>",m_szProvId);

    int iRes=getRoamType(GetCurrentSnap(L),m_szHomeCountryCode,m_szAccessNumber,m_szHomeAreaCode,m_szProvId,p_pRoamAreaCode,p_pCallType);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GetRoamType_lua out iRes=<%d>",iRes);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,iRes);
    //lua out    END
    return iLuaParamIdx;
}

int32    GsmSwitchInfo_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char * p_pchSwitchId= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS GsmSwitchInfo_lua IN p_pchSwitchId=<%s>",p_pchSwitchId);
    REPORT_TRACE("ANASYS GsmSwitchInfo_lua IN iDateTime=<%ld>",iDateTime);
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    GsmMscInfo          p_SSwitchInfo;
    memset(&p_SSwitchInfo,0,sizeof(GsmMscInfo));
    //int iRes=GetGsmMscInfoNew(GetCurrentSnap(L),p_pchSwitchId,iDateTime,&p_SSwitchInfo);
    int iRes=GetGsmMscInfoNew(GetCurrentSnap(L),p_pchSwitchId,tm,&p_SSwitchInfo);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GsmSwitchInfo_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS GsmSwitchInfo_lua out m_iLocationType=<%d>",p_SSwitchInfo.m_iLocationType);
    REPORT_TRACE("ANASYS GsmSwitchInfo_lua out m_iSwitchSeq=<%d>",p_SSwitchInfo.m_iSwitchSeq);
    REPORT_TRACE("ANASYS GsmSwitchInfo_lua out m_szAreaCode=<%s>",p_SSwitchInfo.m_szAreaCode);
    REPORT_TRACE("ANASYS GsmSwitchInfo_lua out m_szBureauCode=<%s>",p_SSwitchInfo.m_szBureauCode);
    REPORT_TRACE("ANASYS GsmSwitchInfo_lua out m_szProvCode=<%s>",p_SSwitchInfo.m_szProvCode);
    REPORT_TRACE("ANASYS GsmSwitchInfo_lua out m_szSwitchId=<%s>",p_SSwitchInfo.m_szSwitchId);
    REPORT_TRACE("ANASYS GsmSwitchInfo_lua out m_szSwitchType=<%s>",p_SSwitchInfo.m_szSwitchType);
    REPORT_TRACE("ANASYS GsmSwitchInfo_lua out m_szVersion=<%s>",p_SSwitchInfo.m_szVersion);
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,iRes);
    put_lua_param(L,iLuaParamIdx,p_SSwitchInfo.m_iLocationType);
    put_lua_param(L,iLuaParamIdx,p_SSwitchInfo.m_iSwitchSeq);
    put_lua_param(L,iLuaParamIdx,p_SSwitchInfo.m_szAreaCode);
    put_lua_param(L,iLuaParamIdx,p_SSwitchInfo.m_szBureauCode);
    put_lua_param(L,iLuaParamIdx,p_SSwitchInfo.m_szProvCode);
    put_lua_param(L,iLuaParamIdx,p_SSwitchInfo.m_szSwitchId);
    put_lua_param(L,iLuaParamIdx,p_SSwitchInfo.m_szSwitchType);
    put_lua_param(L,iLuaParamIdx,p_SSwitchInfo.m_szVersion);
    
    //lua out    END
    return iLuaParamIdx;

}

int32    GsmRouterInfoNew_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char * p_pchSwitchId= get_str_param(L,iLuaParamIdx);
    const char *p_pchTrunkId= get_str_param(L,iLuaParamIdx);
    const char * p_pchAreaCode= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua IN p_pchSwitchId=<%s>",p_pchSwitchId);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua IN p_pchTrunkId=<%s>",p_pchTrunkId);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua IN p_pchAreaCode=<%s>",p_pchAreaCode);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua IN iDateTime=<%ld>",iDateTime);
    GsmRouterInfo p_SRouterInfo;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&p_SRouterInfo,0,sizeof(GsmRouterInfo));
    int iRes=GetGsmRouterInfoNew(GetCurrentSnap(L),p_pchSwitchId,p_pchTrunkId,p_pchAreaCode,tm,&p_SRouterInfo);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua out m_bGetInfo=<%d>",p_SRouterInfo.m_bGetInfo);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua out m_iInTrunkBusiId=<%d>",p_SRouterInfo.m_iInTrunkBusiId);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua out m_iOutTrunkBusiId=<%d>",p_SRouterInfo.m_iOutTrunkBusiId);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua out m_iSettlerId=<%d>",p_SRouterInfo.m_iSettlerId);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua out m_iTollType=<%s>",p_SRouterInfo.m_iTollType);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua out m_szAreaCode=<%s>",p_SRouterInfo.m_szAreaCode);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua out m_szExpireDate=<%s>",p_SRouterInfo.m_szExpireDate);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua out m_szSwitchId=<%s>",p_SRouterInfo.m_szSwitchId);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua out m_szTrunkId=<%s>",p_SRouterInfo.m_szTrunkId);
    REPORT_TRACE("ANASYS GsmRouterInfoNew_lua out m_szValidDate=<%s>",p_SRouterInfo.m_szValidDate);
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_bGetInfo);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_iInTrunkBusiId);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_iOutTrunkBusiId);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_iSettlerId);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_iTollType);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_szAreaCode);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_szExpireDate);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_szSwitchId);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_szTrunkId);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_szValidDate);
    
    //lua out    END
    return iLuaParamIdx;

}

int32    GsmRouterInfo_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char * p_pchSwitchId= get_str_param(L,iLuaParamIdx);
    const char *p_pchTrunkId= get_str_param(L,iLuaParamIdx);
    //const char * p_pchAreaCode= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua IN p_pchSwitchId=<%s>",p_pchSwitchId);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua IN p_pchTrunkId=<%s>",p_pchTrunkId);
    //REPORT_TRACE("ANASYS GsmRouterInfo_lua IN p_pchAreaCode=<%s>",p_pchAreaCode);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua IN iDateTime=<%ld>",iDateTime);
    GsmRouterInfo p_SRouterInfo;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&p_SRouterInfo,0,sizeof(GsmRouterInfo));
    int iRes=GetGsmRouterInfo(GetCurrentSnap(L),p_pchSwitchId,p_pchTrunkId,tm,&p_SRouterInfo);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GsmRouterInfo_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua out m_bGetInfo=<%d>",p_SRouterInfo.m_bGetInfo);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua out m_iInTrunkBusiId=<%d>",p_SRouterInfo.m_iInTrunkBusiId);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua out m_iOutTrunkBusiId=<%d>",p_SRouterInfo.m_iOutTrunkBusiId);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua out m_iSettlerId=<%d>",p_SRouterInfo.m_iSettlerId);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua out m_iTollType=<%s>",p_SRouterInfo.m_iTollType);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua out m_szAreaCode=<%s>",p_SRouterInfo.m_szAreaCode);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua out m_szExpireDate=<%s>",p_SRouterInfo.m_szExpireDate);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua out m_szSwitchId=<%s>",p_SRouterInfo.m_szSwitchId);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua out m_szTrunkId=<%s>",p_SRouterInfo.m_szTrunkId);
    REPORT_TRACE("ANASYS GsmRouterInfo_lua out m_szValidDate=<%s>",p_SRouterInfo.m_szValidDate);
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_bGetInfo);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_iInTrunkBusiId);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_iOutTrunkBusiId);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_iSettlerId);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_iTollType);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_szAreaCode);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_szExpireDate);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_szSwitchId);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_szTrunkId);
    put_lua_param(L,iLuaParamIdx,p_SRouterInfo.m_szValidDate);
    
    //lua out    END
    return iLuaParamIdx;

}

int32    GetRoamAreaCode_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    char p_pAreaCode[64];
    const char * p_pMsrnCode= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    //const char *p_pAreaCode = get_str_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS GetRoamAreaCode_lua IN p_pMsrnCode=<%s>",p_pMsrnCode);
    REPORT_TRACE("ANASYS GetRoamAreaCode_lua IN iDateTime=<%ld>",iDateTime);
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    int iRes=GetRoamAreaCodeNew(GetCurrentSnap(L),p_pMsrnCode,tm,p_pAreaCode);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GetRoamAreaCode_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS GetRoamAreaCode_lua out p_pAreaCode=<%s>",p_pAreaCode);
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pAreaCode);

    
    //lua out    END
    return iLuaParamIdx;

}

int32    SPNumberAnalyze_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char * p_pchNumber= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua IN p_pchNumber=<%s>",p_pchNumber);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua IN iDateTime=<%ld>",iDateTime);
    SPNumberInfo p_pSNumber;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&p_pSNumber,0,sizeof(SPNumberInfo));
    int iRes=SPNumberAnalysis(GetCurrentSnap(L),p_pchNumber,tm,&p_pSNumber);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua out m_iBusiType=<%d>",p_pSNumber.m_iBusiType);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua out m_iInTrunkBusiId=<%d>",p_pSNumber.m_iNumberType);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua out m_iOutTrunkBusiId=<%d>",p_pSNumber.m_iOperId);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua out m_iSettlerId=<%d>",p_pSNumber.m_iPalType);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua out m_iTollType=<%d>",p_pSNumber.m_iServRange);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua out m_szAreaCode=<%s>",p_pSNumber.m_szAccArea);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua out m_szExpireDate=<%s>",p_pSNumber.m_szBusiCode);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua out m_szSwitchId=<%s>",p_pSNumber.m_szServCode1);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua out m_szTrunkId=<%s>",p_pSNumber.m_szServCode2);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua out m_szValidDate=<%s>",p_pSNumber.m_szSPCode);
    REPORT_TRACE("ANASYS SPNumberAnalyze_lua out m_szValidDate=<%s>",p_pSNumber.m_szSPName);
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iBusiType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iNumberType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iOperId);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iPalType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iServRange);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szAccArea);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szBusiCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szServCode1);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szServCode2);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szSPCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szSPName);
    
    //lua out    END
    return iLuaParamIdx;

}

int32    SPNumberSpAnalyze_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char * p_pchNumber= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua IN p_pchNumber=<%s>",p_pchNumber);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua IN iDateTime=<%ld>",iDateTime);
    SPNumberInfo p_pSNumber;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&p_pSNumber,0,sizeof(SPNumberInfo));
    int iRes=SPNumberSpAnalysis(GetCurrentSnap(L),p_pchNumber,tm,&p_pSNumber);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua out m_iBusiType=<%d>",p_pSNumber.m_iBusiType);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua out m_iInTrunkBusiId=<%d>",p_pSNumber.m_iNumberType);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua out m_iOutTrunkBusiId=<%d>",p_pSNumber.m_iOperId);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua out m_iSettlerId=<%d>",p_pSNumber.m_iPalType);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua out m_iTollType=<%d>",p_pSNumber.m_iServRange);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua out m_szAreaCode=<%s>",p_pSNumber.m_szAccArea);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua out m_szExpireDate=<%s>",p_pSNumber.m_szBusiCode);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua out m_szSwitchId=<%s>",p_pSNumber.m_szServCode1);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua out m_szTrunkId=<%s>",p_pSNumber.m_szServCode2);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua out m_szValidDate=<%s>",p_pSNumber.m_szSPCode);
    REPORT_TRACE("ANASYS SPNumberSpAnalyze_lua out m_szValidDate=<%s>",p_pSNumber.m_szSPName);
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iBusiType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iNumberType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iOperId);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iPalType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iServRange);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szAccArea);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szBusiCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szServCode1);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szServCode2);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szSPCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szSPName);
    
    //lua out    END
    return iLuaParamIdx;

}

int32    SPNumberServCodeAnalyze_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char * p_pchNumber= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua IN p_pchNumber=<%s>",p_pchNumber);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua IN iDateTime=<%ld>",iDateTime);
    SPNumberInfo p_pSNumber;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&p_pSNumber,0,sizeof(SPNumberInfo));
    int iRes=SPNumberServCodeAnalysis(GetCurrentSnap(L),p_pchNumber,tm,&p_pSNumber);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua out m_iBusiType=<%d>",p_pSNumber.m_iBusiType);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua out m_iNumberType=<%d>",p_pSNumber.m_iNumberType);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua out m_iOperId=<%d>",p_pSNumber.m_iOperId);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua out m_iPalType=<%d>",p_pSNumber.m_iPalType);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua out m_iServRange=<%d>",p_pSNumber.m_iServRange);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua out m_szAccArea=<%s>",p_pSNumber.m_szAccArea);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua out m_szBusiCode=<%s>",p_pSNumber.m_szBusiCode);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua out m_szServCode1=<%s>",p_pSNumber.m_szServCode1);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua out m_szServCode2=<%s>",p_pSNumber.m_szServCode2);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua out m_szSPCode=<%s>",p_pSNumber.m_szSPCode);
    REPORT_TRACE("ANASYS SPNumberServCodeAnalyze_lua out m_szSPName=<%s>",p_pSNumber.m_szSPName);
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iBusiType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iNumberType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iOperId);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iPalType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iServRange);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szAccArea);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szBusiCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szServCode1);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szServCode2);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szSPCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szSPName);
    
    //lua out    END
    return iLuaParamIdx;

}

int32    BsmsByServCode_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char * pszServType= get_str_param(L,iLuaParamIdx);
    const char *p_pchNumber= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    int32 iMacthType= get_int64_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS BsmsByServCode_lua IN pszServType=<%s>",pszServType);
    REPORT_TRACE("ANASYS BsmsByServCode_lua IN p_pchNumber=<%s>",p_pchNumber);
    REPORT_TRACE("ANASYS BsmsByServCode_lua IN iDateTime=<%ld>",iDateTime);
    REPORT_TRACE("ANASYS BsmsByServCode_lua IN iMacthType=<%d>",iMacthType);
    BsmsInfo p_pSNumber;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&p_pSNumber,0,sizeof(BsmsInfo));
    int iRes=ServCodeAnalysisNew(GetCurrentSnap(L),pszServType,p_pchNumber,tm,iMacthType,&p_pSNumber);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS BsmsByServCode_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS BsmsByServCode_lua out m_iNumberType=<%d>",p_pSNumber.m_iNumberType);
    REPORT_TRACE("ANASYS BsmsByServCode_lua out m_szCreateDate=<%s>",p_pSNumber.m_szCreateDate);
    REPORT_TRACE("ANASYS BsmsByServCode_lua out m_szProcCode=<%s>",p_pSNumber.m_szProcCode);
    REPORT_TRACE("ANASYS BsmsByServCode_lua out m_szServCode=<%s>",p_pSNumber.m_szServCode);
    REPORT_TRACE("ANASYS BsmsByServCode_lua out m_szServType=<%s>",p_pSNumber.m_szServType);
    REPORT_TRACE("ANASYS BsmsByServCode_lua out m_szSmsType=<%s>",p_pSNumber.m_szSmsType);
    REPORT_TRACE("ANASYS BsmsByServCode_lua out isBalPort=<%s>",p_pSNumber.isBalPort);
    REPORT_TRACE("ANASYS BsmsByServCode_lua out settlementPrice1=<%d>",p_pSNumber.settlementPrice1);
    REPORT_TRACE("ANASYS BsmsByServCode_lua out settlementPrice2=<%d>",p_pSNumber.settlementPrice2);
    REPORT_TRACE("ANASYS BsmsByServCode_lua out settlementPrice3=<%d>",p_pSNumber.settlementPrice3);
    REPORT_TRACE("ANASYS BsmsByServCode_lua out settlementPrice4=<%d>",p_pSNumber.settlementPrice4);
    REPORT_TRACE("ANASYS BsmsByServCode_lua out settlementPrice5=<%d>",p_pSNumber.settlementPrice5);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iNumberType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szCreateDate);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szProcCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szServCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szServType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szSmsType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.isBalPort);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.settlementPrice1);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.settlementPrice2);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.settlementPrice3);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.settlementPrice4);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.settlementPrice5);
    
    //lua out    END
    return iLuaParamIdx;

}

int32    AccCodeAnalyze_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const int32 iAccCode= get_int64_param(L,iLuaParamIdx);
    const char* p_pchOperCode= get_str_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS AccCodeAnalyze_lua IN pszServType=<%d>",iAccCode);
    REPORT_TRACE("ANASYS AccCodeAnalyze_lua IN p_pchNumber=<%s>",p_pchOperCode);

    PlatformInfo p_pSNumber;
    /*
    AISTD string strRecTime(ltoa(iStartTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&p_pSNumber,0,sizeof(PlatformInfo));
    */
    int iRes=AccCodeAnalysis(GetCurrentSnap(L),iAccCode,p_pchOperCode,&p_pSNumber);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS AccCodeAnalyze_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS AccCodeAnalyze_lua out m_iNumberType=<%d>",p_pSNumber.m_bFind);
    REPORT_TRACE("ANASYS AccCodeAnalyze_lua out m_szCreateDate=<%s>",p_pSNumber.m_iAccCode);
    REPORT_TRACE("ANASYS AccCodeAnalyze_lua out m_szProcCode=<%s>",p_pSNumber.m_iOperId);
    REPORT_TRACE("ANASYS AccCodeAnalyze_lua out m_szServCode=<%s>",p_pSNumber.m_iPalType);
    REPORT_TRACE("ANASYS AccCodeAnalyze_lua out m_szServType=<%s>",p_pSNumber.m_iServRange);
    REPORT_TRACE("ANASYS AccCodeAnalyze_lua out m_szSmsType=<%s>",p_pSNumber.m_szBusiName);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_bFind);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iAccCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iOperId);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iPalType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iServRange);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szBusiName);
    
    //lua out    END
    return iLuaParamIdx;

}


int32    OperCodeAnalyze_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char *p_pchNumber= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS OperCodeAnalyze_lua IN p_pchNumber=<%s>",p_pchNumber);
    REPORT_TRACE("ANASYS OperCodeAnalyze_lua IN iDateTime=<%ld>",iDateTime);

     AddOperatorInfo p_pSNumber;
     AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);

    int iRes=OperCodeAnalysis(GetCurrentSnap(L),p_pchNumber,tm,&p_pSNumber);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS OperCodeAnalyze_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS OperCodeAnalyze_lua out m_bFind=<%d>",p_pSNumber.m_bFind);
    REPORT_TRACE("ANASYS OperCodeAnalyze_lua out m_iOperId=<%d>",p_pSNumber.m_iOperId);
    REPORT_TRACE("ANASYS OperCodeAnalyze_lua out m_iOperType=<%d>",p_pSNumber.m_iOperType);
    REPORT_TRACE("ANASYS OperCodeAnalyze_lua out m_iSpcOperSeg=<%d>",p_pSNumber.m_iSpcOperSeg);
    REPORT_TRACE("ANASYS OperCodeAnalyze_lua out m_iSts=<%d>",p_pSNumber.m_iSts);
    REPORT_TRACE("ANASYS OperCodeAnalyze_lua out m_szHomeArea=<%s>",p_pSNumber.m_szHomeArea);
    REPORT_TRACE("ANASYS OperCodeAnalyze_lua out m_szOperCode=<%s>",p_pSNumber.m_szOperCode);
    REPORT_TRACE("ANASYS OperCodeAnalyze_lua out m_szOperName=<%s>",p_pSNumber.m_szOperName);
    REPORT_TRACE("ANASYS OperCodeAnalyze_lua out m_szStsTime=<%s>",p_pSNumber.m_szStsTime);


    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_bFind);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iOperId);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iOperType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iSpcOperSeg);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iSts);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szHomeArea);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szOperCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szOperName);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szStsTime);
    
    //lua out    END
    return iLuaParamIdx;

}

int32    OperCodeMCCAnalyze_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char *p_opercode= get_str_param(L,iLuaParamIdx);
    const char* p_homearea= get_str_param(L,iLuaParamIdx);
    int64   iStartTime = get_int64_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua IN p_opercode=<%s>",p_opercode);
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua IN p_homearea=<%s>",p_homearea);
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua IN iStartTime=<%ld>",iStartTime);    
       
    AISTD string strRecTime(ltoa(iStartTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);    

    AddOperatorInfo p_pSNumber;

    int iRes=OperCodeMCCAnalysis(GetCurrentSnap(L),p_opercode,p_homearea,tm,&p_pSNumber);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua out m_iNumberType=<%d>",p_pSNumber.m_bFind);
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua out m_iOperId=<%d>",p_pSNumber.m_iOperId);
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua out m_iOperType=<%d>",p_pSNumber.m_iOperType);
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua out m_iSpcOperSeg=<%d>",p_pSNumber.m_iSpcOperSeg);
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua out m_iSts=<%d>",p_pSNumber.m_iSts);
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua out m_szHomeArea=<%s>",p_pSNumber.m_szHomeArea);
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua out m_szOperCode=<%s>",p_pSNumber.m_szOperCode);
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua out m_szOperName=<%s>",p_pSNumber.m_szOperName);
    REPORT_TRACE("ANASYS OperCodeMCCAnalyze_lua out m_szStsTime=<%s>",p_pSNumber.m_szStsTime);


    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_bFind);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iOperId);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iSpcOperSeg);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iSts);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szHomeArea);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szOperCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szOperName);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szStsTime);
    
    //lua out    END
    return iLuaParamIdx;

}

int32    IsmgCodeAnalyze_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    char pAreaCode[64];
    const char *p_pchNumber= get_str_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS IsmgCodeAnalyze_lua IN p_pchNumber=<%s>",p_pchNumber);
    memset(pAreaCode,0,sizeof(pAreaCode));
    int iRes=IsmgCodeAnalysis(GetCurrentSnap(L),p_pchNumber,pAreaCode);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS IsmgCodeAnalyze_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS IsmgCodeAnalyze_lua out pAreaCode=<%d>",pAreaCode);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,pAreaCode);
    
    //lua out    END
    return iLuaParamIdx;

}

int32    VisitAreaAnalyze_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char *p_pchNumber= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua IN p_opercode=<%s>",p_pchNumber);
    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua IN iDateTime=<%ld>",iDateTime);

    AddOperatorInfo p_pSNumber;
     
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua out strRecTime=<%s>",strRecTime.c_str());

    int iRes=VisitInfoAnalysis(GetCurrentSnap(L),p_pchNumber,tm,&p_pSNumber);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua out m_bFind=<%d>",p_pSNumber.m_bFind);
    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua out m_iOperId=<%d>",p_pSNumber.m_iOperId);
    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua out m_iOperType=<%d>",p_pSNumber.m_iOperType);
    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua out m_iSpcOperSeg=<%d>",p_pSNumber.m_iSpcOperSeg);
    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua out m_iSts=<%ld>",p_pSNumber.m_iSts);
    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua out m_szHomeArea=<%s>",p_pSNumber.m_szHomeArea);
    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua out m_szOperCode=<%s>",p_pSNumber.m_szOperCode);
    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua out m_szOperName=<%s>",p_pSNumber.m_szOperName);
    REPORT_TRACE("ANASYS VisitAreaAnalyze_lua out m_szStsTime=<%s>",p_pSNumber.m_szStsTime);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_bFind);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iOperId);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iSpcOperSeg);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iSts);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szHomeArea);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szOperCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szOperName);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szStsTime);
    
    //lua out    END
    return iLuaParamIdx;

}

int32    SpecialUserAnalyze_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char * p_pchUserNumber= get_str_param(L,iLuaParamIdx);
    const char * p_pchAreaCode= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS SpecialUserAnalyze_lua IN p_pchUserNumber=<%s>",p_pchUserNumber);
    REPORT_TRACE("ANASYS SpecialUserAnalyze_lua IN p_pchAreaCode=<%s>",p_pchAreaCode);
    REPORT_TRACE("ANASYS SpecialUserAnalyze_lua IN iDateTime=<%ld>",iDateTime);

    SpecialUserInfo  p_pSNumber;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    REPORT_TRACE("ANASYS SpecialUserAnalyze_lua out strRecTime=<%s>",strRecTime.c_str());
    REPORT_TRACE("ANASYS SpecialUserAnalyze_lua out tm=<%ld>",tm);
    int iRes=SpecialUserAnalysis(GetCurrentSnap(L),p_pchUserNumber,p_pchAreaCode,tm,&p_pSNumber);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS SpecialUserAnalyze_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS SpecialUserAnalyze_lua out m_bGetInfo=<%d>",p_pSNumber.m_bGetInfo);
    REPORT_TRACE("ANASYS SpecialUserAnalyze_lua out m_iNumberType=<%d>",p_pSNumber.m_iNumberType);
    REPORT_TRACE("ANASYS SpecialUserAnalyze_lua out m_iOperatorId=<%d>",p_pSNumber.m_iOperatorId);
    REPORT_TRACE("ANASYS SpecialUserAnalyze_lua out m_szAreaCode=<%s>",p_pSNumber.m_szAreaCode);
    REPORT_TRACE("ANASYS SpecialUserAnalyze_lua out m_szBureauCode=<%s>",p_pSNumber.m_szBureauCode);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_bGetInfo);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iNumberType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iOperatorId);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szAreaCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szBureauCode);
    
    //lua out    END
    return iLuaParamIdx;

}

// 湖北移动 专网号头查询
int32    SpecialUserAnalyzeSN_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char * p_pchUserNumber= get_str_param(L,iLuaParamIdx);
    const char * p_pchAreaCode= get_str_param(L,iLuaParamIdx);
    const char * p_pchBureauCode= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS SpecialUserAnalyzeSN_lua IN p_pchUserNumber=<%s>",p_pchUserNumber);
    REPORT_TRACE("ANASYS SpecialUserAnalyzeSN_lua IN p_pchAreaCode=<%s>",p_pchAreaCode);
    REPORT_TRACE("ANASYS SpecialUserAnalyzeSN_lua IN p_pchBureauCode=<%s>",p_pchBureauCode);
    REPORT_TRACE("ANASYS SpecialUserAnalyzeSN_lua IN iDateTime=<%ld>",iDateTime);

    SpecialUserInfo  p_pSNumber;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    REPORT_TRACE("ANASYS SpecialUserAnalyzeSN_lua out strRecTime=<%s>",strRecTime.c_str());
    REPORT_TRACE("ANASYS SpecialUserAnalyzeSN_lua out tm=<%ld>",tm);
    int iRes=SpecialUserAnalysisSN(GetCurrentSnap(L),p_pchUserNumber,p_pchAreaCode,p_pchBureauCode,tm,&p_pSNumber);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS SpecialUserAnalyzeSN_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS SpecialUserAnalyzeSN_lua out m_bGetInfo=<%d>",p_pSNumber.m_bGetInfo);
    REPORT_TRACE("ANASYS SpecialUserAnalyzeSN_lua out m_iNumberType=<%d>",p_pSNumber.m_iNumberType);
    REPORT_TRACE("ANASYS SpecialUserAnalyzeSN_lua out m_iOperatorId=<%d>",p_pSNumber.m_iOperatorId);
    REPORT_TRACE("ANASYS SpecialUserAnalyzeSN_lua out m_szAreaCode=<%s>",p_pSNumber.m_szAreaCode);
    REPORT_TRACE("ANASYS SpecialUserAnalyzeSN_lua out m_szBureauCode=<%s>",p_pSNumber.m_szBureauCode);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_bGetInfo);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iNumberType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iOperatorId);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szAreaCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szBureauCode);
    
    //lua out    END
    return iLuaParamIdx;

}


int32    ImsiAreaCodeAnalysis_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    char szAreaCode[64];
    const char  *p_pchImsiNumber= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS ImsiAreaCodeAnalysis_lua IN p_pchImsiNumber=<%s>",p_pchImsiNumber);
    REPORT_TRACE("ANASYS ImsiAreaCodeAnalysis_lua IN iDateTime=<%ld>",iDateTime);

    SpecialUserInfo  p_pSNumber;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&p_pSNumber,0,sizeof(SpecialUserInfo));
    int iRes=ImsiAreaCodeAnalysisNew(GetCurrentSnap(L),p_pchImsiNumber,tm,szAreaCode);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS ImsiAreaCodeAnalysis_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS ImsiAreaCodeAnalysis_lua out szAreaCode=<%s>",szAreaCode);
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,szAreaCode);
    
    //lua out    END
    return iLuaParamIdx;

}


int32    FindSpRatio_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    char sSettleAmountBuff[128];
    const char  *p_pchSpCode= get_str_param(L,iLuaParamIdx);
    const char  *p_pchOperatorCode= get_str_param(L,iLuaParamIdx);
    const int    nType= get_int64_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS FindSpRatio_lua IN p_pchSpCode=<%s>",p_pchSpCode);
    REPORT_TRACE("ANASYS FindSpRatio_lua IN p_pchOperatorCode=<%s>",p_pchOperatorCode);
    REPORT_TRACE("ANASYS FindSpRatio_lua IN nType=<%d>",nType);
    REPORT_TRACE("ANASYS FindSpRatio_lua IN iDateTime=<%ld>",iDateTime);

    SSpSettleRatio p_pRatioInfo;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&p_pRatioInfo,0,sizeof(SSpSettleRatio));
    int iRes=FindSpRatio(GetCurrentSnap(L),p_pchSpCode,p_pchOperatorCode,nType,tm,&p_pRatioInfo);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS FindSpRatio_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS FindSpRatio_lua out nAccSettleId=<%d>",p_pRatioInfo.nAccSettleId);
    REPORT_TRACE("ANASYS FindSpRatio_lua out nChargeDir=<%d>",p_pRatioInfo.nChargeDir);
    REPORT_TRACE("ANASYS FindSpRatio_lua out nCMRatio=<%d>",p_pRatioInfo.nCMRatio);
    REPORT_TRACE("ANASYS FindSpRatio_lua out nSettleAmount=<%ld>",p_pRatioInfo.nSettleAmount);
    REPORT_TRACE("ANASYS FindSpRatio_lua out nSettleMode=<%d>",p_pRatioInfo.nSettleMode);
    REPORT_TRACE("ANASYS FindSpRatio_lua out nSpRatio=<%d>",p_pRatioInfo.nSpRatio);
    REPORT_TRACE("ANASYS FindSpRatio_lua out nSpType=<%d>",p_pRatioInfo.nSpType);
    REPORT_TRACE("ANASYS FindSpRatio_lua out nThirdRatio=<%d>",p_pRatioInfo.nThirdRatio);
    REPORT_TRACE("ANASYS FindSpRatio_lua out szExpireDate=<%s>",p_pRatioInfo.szExpireDate);
    REPORT_TRACE("ANASYS FindSpRatio_lua out szOperCode=<%s>",p_pRatioInfo.szOperCode);
    REPORT_TRACE("ANASYS FindSpRatio_lua out szSpCode=<%s>",p_pRatioInfo.szSpCode);
    REPORT_TRACE("ANASYS FindSpRatio_lua out szValidDate=<%s>",p_pRatioInfo.szValidDate);
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pRatioInfo.nAccSettleId);
    put_lua_param(L,iLuaParamIdx,p_pRatioInfo.nChargeDir);
    put_lua_param(L,iLuaParamIdx,p_pRatioInfo.nCMRatio);
    memset(sSettleAmountBuff,0,sizeof(sSettleAmountBuff));
    snprintf(sSettleAmountBuff,sizeof(sSettleAmountBuff),"%ld",p_pRatioInfo.nSettleAmount);
    put_lua_param(L,iLuaParamIdx,sSettleAmountBuff);
    put_lua_param(L,iLuaParamIdx,p_pRatioInfo.nSettleMode);
    put_lua_param(L,iLuaParamIdx,p_pRatioInfo.nSpRatio);
    put_lua_param(L,iLuaParamIdx,p_pRatioInfo.nSpType);
    put_lua_param(L,iLuaParamIdx,p_pRatioInfo.nThirdRatio);
    put_lua_param(L,iLuaParamIdx,p_pRatioInfo.szExpireDate);
    put_lua_param(L,iLuaParamIdx,p_pRatioInfo.szOperCode);
    put_lua_param(L,iLuaParamIdx,p_pRatioInfo.szSpCode);
    put_lua_param(L,iLuaParamIdx,p_pRatioInfo.szValidDate);
        
    //lua out    END
    return iLuaParamIdx;

}


int32    GetThirdRatioInfo_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    iLuaParamIdx = 0;
    //lua out    END
    return iLuaParamIdx;
}

int32    GetMscIdFromFileName_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    char p_pchMscId[32];
    const char *p_pchFileName= get_str_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS GetMscIdFromFileName_lua IN p_pchSpCode=<%s>",p_pchFileName);
    
    int iRes=GetMscIdFromFileName(GetCurrentSnap(L),p_pchFileName,p_pchMscId);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GetMscIdFromFileName_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS GetMscIdFromFileName_lua out p_pchMscId=<%s>",p_pchMscId);

        //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pchMscId);
    
    
    //lua out    END
    return iLuaParamIdx;

}


int32    FindGprsIpaddr_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char *p_pchProvCode= get_str_param(L,iLuaParamIdx);
    const char *p_pchIpaddr= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS FindGprsIpaddr_lua IN p_pchProvCode=<%s>",p_pchProvCode);
    REPORT_TRACE("ANASYS FindGprsIpaddr_lua IN p_pchIpaddr=<%s>",p_pchIpaddr);
    REPORT_TRACE("ANASYS FindGprsIpaddr_lua IN iDateTime=<%ld>",iDateTime);
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    //memset(&p_pRatioInfo,0,sizeof(SSpSettleRatio));
    int iRes=FindGprsIpaddr(GetCurrentSnap(L),p_pchProvCode,p_pchIpaddr,tm);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS FindGprsIpaddr_lua out iRes=<%d>",iRes);
    
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,iRes);
    
    //lua out    END
    return iLuaParamIdx;

}


int32    GetProvByAreaCode_lua(lua_State *L)
{
    char  pchProv[32];
    int32 iLuaParamIdx = 1;
    const char *pchAreaCode= get_str_param(L,iLuaParamIdx);
    int64   iStartTime = get_int64_param(L,iLuaParamIdx);     

    REPORT_TRACE("ANASYS GetProvByAreaCode_lua IN pchAreaCode=<%s>",pchAreaCode);
    REPORT_TRACE("ANASYS GetProvByAreaCode_lua IN iStartTime=<%ld>",iStartTime);
    AISTD string strRecTime(ltoa(iStartTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);

    int iRes=getProvByAreaCode(GetCurrentSnap(L),pchAreaCode,pchProv,tm);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GetProvByAreaCode_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS GetProvByAreaCode_lua out pchProv=<%s>",pchProv);
    
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,pchProv);

    //lua out    END
    return iLuaParamIdx;
}


int32    FindNpInfo_lua(lua_State *L)
{
    int iRes = 0;
    int32 iLuaParamIdx = 1;
    const char *szNumber= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS FindNpInfo_lua IN szNumber=<%s>",szNumber);
    REPORT_TRACE("ANASYS FindNpInfo_lua IN iDateTime=<%ld>",iDateTime);

    SNpInfo p_pNpInfo;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&p_pNpInfo,0,sizeof(SNpInfo));
    iRes=FindNpInfo(GetCurrentSnap(L),szNumber,strRecTime.c_str(),p_pNpInfo);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS FindNpInfo_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS FindNpInfo_lua out nFlag=<%d>",p_pNpInfo.nFlag);
    REPORT_TRACE("ANASYS FindNpInfo_lua out nNetWork=<%d>",p_pNpInfo.nNetWork);
    REPORT_TRACE("ANASYS FindNpInfo_lua out nOperatorParty=<%d>",p_pNpInfo.nOperatorParty);
    REPORT_TRACE("ANASYS FindNpInfo_lua out szPhoneId=<%s>",p_pNpInfo.szPhoneId);
    
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pNpInfo.nFlag);
    put_lua_param(L,iLuaParamIdx,p_pNpInfo.nNetWork);
    put_lua_param(L,iLuaParamIdx,p_pNpInfo.nOperatorParty);
    put_lua_param(L,iLuaParamIdx,p_pNpInfo.szPhoneId);
    
    //lua out    END
    return iLuaParamIdx;
}


int32    IvrRatio_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    char strFeeBuff[128];
    char strSettleAmountBuff[128];
    const char *szOper= get_str_param(L,iLuaParamIdx);
    const char *szSpCode= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    const char *szServ= get_str_param(L,iLuaParamIdx);
    const int32 ifee= get_int64_param(L,iLuaParamIdx);
    const int32 ikey= get_int64_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS IvrRatio_lua IN szOper=<%s>",szOper);
    REPORT_TRACE("ANASYS IvrRatio_lua IN szSpCode=<%s>",szSpCode);
    REPORT_TRACE("ANASYS IvrRatio_lua IN iDateTime=<%ld>",iDateTime);
    REPORT_TRACE("ANASYS IvrRatio_lua IN szServ=<%s>",szServ);
    REPORT_TRACE("ANASYS IvrRatio_lua IN ifee=<%d>",ifee);
    REPORT_TRACE("ANASYS IvrRatio_lua IN ikey=<%d>",ikey);

    SIvrRatio pSIvrRatio;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&pSIvrRatio,0,sizeof(SIvrRatio));
    int iRes=IvrRatioAnalysis(GetCurrentSnap(L),szOper,szSpCode,tm,szServ,ifee,ikey,&pSIvrRatio);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS IvrRatio_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS IvrRatio_lua out lFee=<%d>",pSIvrRatio.lFee);
    REPORT_TRACE("ANASYS IvrRatio_lua out lSettleAmount=<%d>",pSIvrRatio.lSettleAmount);
    REPORT_TRACE("ANASYS IvrRatio_lua out nInProp=<%d>",pSIvrRatio.nInProp);
    REPORT_TRACE("ANASYS IvrRatio_lua out nOurProp=<%d>",pSIvrRatio.nOurProp);
    REPORT_TRACE("ANASYS IvrRatio_lua out nSettleMode=<%d>",pSIvrRatio.nSettleMode);
    REPORT_TRACE("ANASYS IvrRatio_lua out szOperCode=<%s>",pSIvrRatio.szOperCode);
    REPORT_TRACE("ANASYS IvrRatio_lua out szServType=<%s>",pSIvrRatio.szServType);
    REPORT_TRACE("ANASYS IvrRatio_lua out szSpCode=<%s>",pSIvrRatio.szSpCode);
    
    //lua out    BEGIN
    memset(strFeeBuff,0,sizeof(strFeeBuff));
    snprintf(strFeeBuff,sizeof(strFeeBuff),"%ld",pSIvrRatio.lFee);
    put_lua_param(L,iLuaParamIdx,strFeeBuff);
    memset(strSettleAmountBuff,0,sizeof(strSettleAmountBuff));
    snprintf(strSettleAmountBuff,sizeof(strSettleAmountBuff),"%ld",pSIvrRatio.lSettleAmount);
    put_lua_param(L,iLuaParamIdx,strSettleAmountBuff);
    put_lua_param(L,iLuaParamIdx,pSIvrRatio.nInProp);
    put_lua_param(L,iLuaParamIdx,pSIvrRatio.nOurProp);
    put_lua_param(L,iLuaParamIdx,pSIvrRatio.nSettleMode);
    put_lua_param(L,iLuaParamIdx,pSIvrRatio.szOperCode);
    put_lua_param(L,iLuaParamIdx,pSIvrRatio.szServType);
    put_lua_param(L,iLuaParamIdx,pSIvrRatio.szSpCode);
    
    //lua out    END
    return iLuaParamIdx;

}

int32    ImsiOperAnalysis_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char *piImsiCode= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    

    REPORT_TRACE("ANASYS ImsiOperAnalysis_lua IN piImsiCode=<%s>",piImsiCode);
    REPORT_TRACE("ANASYS ImsiOperAnalysis_lua IN iDateTime=<%ld>",iDateTime);

    ImsiOperInfo poImsiOperInfo;
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&poImsiOperInfo,0,sizeof(ImsiOperInfo));
    int iRes=ImsiOperInfoAnalysis(GetCurrentSnap(L),piImsiCode,tm,&poImsiOperInfo);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS ImsiOperAnalysis_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS ImsiOperAnalysis_lua out nIsGetInfo=<%d>",poImsiOperInfo.nIsGetInfo);
    REPORT_TRACE("ANASYS ImsiOperAnalysis_lua out szFindType=<%s>",poImsiOperInfo.szFindType);
    REPORT_TRACE("ANASYS ImsiOperAnalysis_lua out szImsiCode=<%s>",poImsiOperInfo.szImsiCode);
    REPORT_TRACE("ANASYS ImsiOperAnalysis_lua out szImsiCodeName=<%s>",poImsiOperInfo.szImsiCodeName);
    
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,poImsiOperInfo.nIsGetInfo);
    put_lua_param(L,iLuaParamIdx,poImsiOperInfo.szFindType);
    put_lua_param(L,iLuaParamIdx,poImsiOperInfo.szImsiCode);
    put_lua_param(L,iLuaParamIdx,poImsiOperInfo.szImsiCodeName);
    
    //lua out    END
    return iLuaParamIdx;

}

int32 GetSmsNewsAccFee_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    int iRes = 0;
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    const char* p_pchNumber= get_str_param(L,iLuaParamIdx);
    int32 iFee= get_int64_param(L,iLuaParamIdx);
    
    REPORT_TRACE("ANASYS GetSmsNewsAccFee_lua IN iDateTime=<%ld>",iDateTime);
    REPORT_TRACE("ANASYS GetSmsNewsAccFee_lua IN p_pchNumber=<%s>",p_pchNumber);

    //int iRes=getSmsNewsAccFee(GetCurrentSnap(L),iDateTime,p_pchNumber,iFee);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GetSmsNewsAccFee_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS GetSmsNewsAccFee_lua out iFee=<%d>",iFee);
    
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,iFee);
    
    //lua out    END
    return iLuaParamIdx;

}

int32 GetSpProdFeeInfo_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    char strHortationFeeBuff[128];
    char strPunishmentFeeBuff[128];
    char strRemunerationFeeBuff[128];
    int iRes = 0;
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS GetSpProdFeeInfo_lua IN iDateTime=<%ld>",iDateTime);

    SpProdInfo p_pSpProdInfo;

    //int iRes=getSpProdFee(GetCurrentSnap(L),iDateTime,&p_pSpProdInfo);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GetSpProdFeeInfo_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS GetSpProdFeeInfo_lua out m_iFlag=<%d>",p_pSpProdInfo.m_iFlag);
    
    REPORT_TRACE("ANASYS GetSpProdFeeInfo_lua out m_iHortationFee=<%d>",p_pSpProdInfo.m_iHortationFee);
    REPORT_TRACE("ANASYS GetSpProdFeeInfo_lua out m_iPunishmentFee=<%d>",p_pSpProdInfo.m_iPunishmentFee);
    REPORT_TRACE("ANASYS GetSpProdFeeInfo_lua out m_iRemunerationFee=<%d>",p_pSpProdInfo.m_iRemunerationFee);
    REPORT_TRACE("ANASYS GetSpProdFeeInfo_lua out m_szBillMonth=<%s>",p_pSpProdInfo.m_szBillMonth);
    REPORT_TRACE("ANASYS GetSpProdFeeInfo_lua out m_szRemark=<%s>",p_pSpProdInfo.m_szRemark);
    REPORT_TRACE("ANASYS GetSpProdFeeInfo_lua out m_szUserCode=<%s>",p_pSpProdInfo.m_szUserCode);
    REPORT_TRACE("ANASYS GetSpProdFeeInfo_lua out m_iRemunerationFee=<%d>",p_pSpProdInfo.m_iRemunerationFee);
    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,p_pSpProdInfo.m_iFlag);
    memset(strHortationFeeBuff,0,sizeof(strHortationFeeBuff));
    snprintf(strHortationFeeBuff,sizeof(strHortationFeeBuff),"%ld",p_pSpProdInfo.m_iHortationFee);
    put_lua_param(L,iLuaParamIdx,strHortationFeeBuff);
    memset(strPunishmentFeeBuff,0,sizeof(strPunishmentFeeBuff));
    snprintf(strPunishmentFeeBuff,sizeof(strPunishmentFeeBuff),"%ld",p_pSpProdInfo.m_iPunishmentFee);
    put_lua_param(L,iLuaParamIdx,strPunishmentFeeBuff);
    memset(strRemunerationFeeBuff,0,sizeof(strRemunerationFeeBuff));
    snprintf(strRemunerationFeeBuff,sizeof(strRemunerationFeeBuff),"%ld",p_pSpProdInfo.m_iPunishmentFee);
    put_lua_param(L,iLuaParamIdx,strRemunerationFeeBuff);
    put_lua_param(L,iLuaParamIdx,p_pSpProdInfo.m_szBillMonth);
    put_lua_param(L,iLuaParamIdx,p_pSpProdInfo.m_szRemark);
    put_lua_param(L,iLuaParamIdx,p_pSpProdInfo.m_szUserCode);
    
    //lua out    END
    return iLuaParamIdx;
}

int32 GetFreeFeeWoffType_lua(lua_State *L)
{
    int iRes = 0;
    int32 iLuaParamIdx = 1;
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    const char*    p_pchNumber= get_str_param(L,iLuaParamIdx);
    int32 iType = 0;

    REPORT_TRACE("ANASYS GetFreeFeeWoffType_lua IN iDateTime=<%ld>",iDateTime);
    REPORT_TRACE("ANASYS GetFreeFeeWoffType_lua IN p_pchNumber=<%s>",p_pchNumber);

    //int iRes=getFreeFeeWoffType(GetCurrentSnap(L),iDateTime,p_pchNumber,&iType);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GetFreeFeeWoffType_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS GetFreeFeeWoffType_lua out iType=<%d>",iType);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,iType);
    
    //lua out    END
    return iLuaParamIdx;

}

int32 GetUserTradeMark_lua(lua_State *L)
{
    int iRes = 0;
    int32 iLuaParamIdx = 1;
    const char*    p_pchNumber= get_str_param(L,iLuaParamIdx);
    int32 iType = 0;

    REPORT_TRACE("ANASYS GetUserTradeMark_lua IN p_pchNumber=<%s>",p_pchNumber);

//    int iRes=getUserTradeMark(GetCurrentSnap(L),p_pchNumber,&iType);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GetUserTradeMark_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS GetUserTradeMark_lua out iType=<%d>",iType);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,iType);
    
    //lua out    END
    return iLuaParamIdx;

}

int32 RCSMSNumberAnalyze_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char    *p_pchNumber= get_str_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS RCSMSNumberAnalyze_lua IN p_pchNumber=<%s>",p_pchNumber);

    RCSMSNumberInfo p_pSNumber;
    /*AISTD string strRecTime(ltoa(iStartTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);
    memset(&p_pSNumber,0,sizeof(RCSMSNumberInfo));*/
    int iRes=RCSMSNumberAnalysis(GetCurrentSnap(L),p_pchNumber,&p_pSNumber);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS RCSMSNumberAnalyze_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS RCSMSNumberAnalyze_lua out m_iAreaCode=<%d>",p_pSNumber.m_iAreaCode);
    REPORT_TRACE("ANASYS RCSMSNumberAnalyze_lua out m_iIsFound=<%d>",p_pSNumber.m_iIsFound);
    REPORT_TRACE("ANASYS RCSMSNumberAnalyze_lua out m_iUserType=<%d>",p_pSNumber.m_iUserType);
    REPORT_TRACE("ANASYS RCSMSNumberAnalyze_lua out m_szUserNumber=<%s>",p_pSNumber.m_szUserNumber);

    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iAreaCode);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iIsFound);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_iUserType);
    put_lua_param(L,iLuaParamIdx,p_pSNumber.m_szUserNumber);

    //lua out    END
    return iLuaParamIdx;

}

int32 getAreaCodeByCeil_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char *pszLacId= get_str_param(L,iLuaParamIdx);
    const char  *pszCeilId= get_str_param(L,iLuaParamIdx);
    char *p_pchAreaCode = NULL;

    REPORT_TRACE("ANASYS getAreaCodeByCeil_lua IN p_pchNumber=<%s>",pszLacId);
    REPORT_TRACE("ANASYS getAreaCodeByCeil_lua IN pszCeilId=<%s>",pszCeilId);

    int iRes=getAreaCodeByCeil(GetCurrentSnap(L),pszLacId,pszCeilId,p_pchAreaCode);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS getAreaCodeByCeil_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS getAreaCodeByCeil_lua out p_pchAreaCode=<%s>",p_pchAreaCode);    

    put_lua_param(L,iLuaParamIdx,pszCeilId);    

    //lua out    END
    return iLuaParamIdx;

}

int32 getUserLocation_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    const char *pszLacId= get_str_param(L,iLuaParamIdx);
    const char *pszCellId= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS getUserLocation_lua IN pszLacId=<%s>",pszLacId);
    REPORT_TRACE("ANASYS getUserLocation_lua IN pszCellId=<%s>",pszCellId);
    REPORT_TRACE("ANASYS getUserLocation_lua IN iDateTime=<%ld>",iDateTime);
    
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);

    UserLocationInfo pUserLocationInfo;

    int iRes=getUserLocation(GetCurrentSnap(L),pszLacId,pszCellId,tm,&pUserLocationInfo);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS getUserLocation_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS getUserLocation_lua out m_iFlag=<%d>",pUserLocationInfo.m_iFlag);
    REPORT_TRACE("ANASYS getUserLocation_lua out m_szAreaCode=<%s>",pUserLocationInfo.m_szAreaCode);
    REPORT_TRACE("ANASYS getUserLocation_lua out m_szCountyCode=<%s>",pUserLocationInfo.m_szCountyCode);

    put_lua_param(L,iLuaParamIdx,pUserLocationInfo.m_iFlag);
    put_lua_param(L,iLuaParamIdx,pUserLocationInfo.m_szAreaCode);
    put_lua_param(L,iLuaParamIdx,pUserLocationInfo.m_szCountyCode);

    //lua out    END
    return iLuaParamIdx;

}
int32 getGridIdByUser_lua(lua_State *L)
{
    int iRes = 0;
    int32 iLuaParamIdx = 1;
    const char *szNumber= get_str_param(L,iLuaParamIdx);
    const char *cdrTime= get_str_param(L,iLuaParamIdx);
    int32 gridId= 0;

    REPORT_TRACE("ANASYS getGridIdByUser_lua IN szNumber=<%s>",szNumber);
    REPORT_TRACE("ANASYS getGridIdByUser_lua IN cdrTime=<%s>",cdrTime);

//    int iRes=getGridIdByUser(GetCurrentSnap(L),szNumber,cdrTime,&gridId);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GetUserTradeMark_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS GetUserTradeMark_lua out gridId=<%d>",gridId);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,gridId);
    
    //lua out    END
    return iLuaParamIdx;

}

int32 getGridIdByLacCeil_lua(lua_State *L)
{
    int iRes = 0;
    int32 iLuaParamIdx = 1;
    const char *lacIdr= get_str_param(L,iLuaParamIdx);
    const char *ceilId= get_str_param(L,iLuaParamIdx);
     const char *cdrTime= get_str_param(L,iLuaParamIdx);
    int32 gridId= 0;

    REPORT_TRACE("ANASYS getGridIdByLacCeil_lua IN lacIdr=<%s>",lacIdr);
    REPORT_TRACE("ANASYS getGridIdByLacCeil_lua IN ceilId=<%s>",ceilId);
    REPORT_TRACE("ANASYS getGridIdByLacCeil_lua IN cdrTime=<%s>",cdrTime);

    //int iRes=getGridIdByLacCeil(GetCurrentSnap(L),lacIdr,ceilId,cdrTime,&gridId);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS getGridIdByLacCeil_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS getGridIdByLacCeil_lua out gridId=<%d>",gridId);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,gridId);
    
    //lua out    END
    return iLuaParamIdx;

}


int32 getCardHomeProv_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    char szProv[32];
    const char *szCardCode= get_str_param(L,iLuaParamIdx);
    int64 iDateTime= get_int64_param(L,iLuaParamIdx);
    REPORT_TRACE("ANASYS getCardHomeProv_lua IN szCardCode=<%s>",szCardCode);
    REPORT_TRACE("ANASYS getCardHomeProv_lua IN iDateTime=<%ld>",iDateTime);
    
    AISTD string strRecTime(ltoa(iDateTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);

    int iRes=findBpsCardHomeProv(GetCurrentSnap(L),szCardCode,tm,szProv);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS getCardHomeProv_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS getCardHomeProv_lua out szProv=<%s>",szProv);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,szProv);
    
    //lua out    END
    return iLuaParamIdx;

}

int32 getDateFromFileName_lua(lua_State *L)
{
    int32 iLuaParamIdx = 1;
    char fileNameDate[32];
    char tempBuff[128];
    const char *pszFileName= get_str_param(L,iLuaParamIdx);
    memset(fileNameDate,0,sizeof(fileNameDate));
    REPORT_TRACE("ANASYS getDateFromFileName_lua IN pszFileName=<%s>",pszFileName);

    strncpy(tempBuff,pszFileName,sizeof(tempBuff));

    int iRes=getDateFromFileName(tempBuff,fileNameDate);
    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS getDateFromFileName_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS getDateFromFileName_lua out fileNameDate=<%s>",fileNameDate);

    //lua out    BEGIN
    put_lua_param(L,iLuaParamIdx,fileNameDate);
    
    //lua out    END
    return iLuaParamIdx;

}

int32 IpAddressAnalysis_lua(lua_State *L)
{
    int iRes = 0;
    int32 iLuaParamIdx = 1;
    
    const char *address = get_str_param(L,iLuaParamIdx);
    int64 statTime= get_int64_param(L,iLuaParamIdx);
    int findType = get_int64_param(L,iLuaParamIdx);
    const char *ipType = get_str_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS IpAddressAnalysis_lua IN address=<%s>",address);
    REPORT_TRACE("ANASYS IpAddressAnalysis_lua IN statTime=<%d>",statTime);
    REPORT_TRACE("ANASYS IpAddressAnalysis_lua IN findType=<%d>",findType);
    REPORT_TRACE("ANASYS IpAddressAnalysis_lua IN ipType=<%s>",ipType);

    AISTD string strRecTime(ltoa(statTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);

    char areaCode[16] = {0};    
    iRes=IpAddressAnalysis(GetCurrentSnap(L),address,tm,findType,ipType,areaCode);
    if(areaCode == NULL || strlen(areaCode) == 0){
        strcpy(areaCode,"000");
    }

    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS Ipv4AddressAnalysis_lua out areaCode=<%s>",areaCode);

    //lua out BEGIN
    put_lua_param(L,iLuaParamIdx,areaCode);
    //lua out END
    
    return iLuaParamIdx;
}

int32 GetSjDefaultPriceInfo_lua(lua_State *L)
{
    int iRes = 0;
    int32 iLuaParamIdx = 1;
    int32 settlementPrice = 0;

    const char *settlementType = get_str_param(L,iLuaParamIdx);
    int64 statTime= get_int64_param(L,iLuaParamIdx);

    REPORT_TRACE("ANASYS GetSjDefaultPriceInfo_lua IN settlementType=<%s>",settlementType);
    REPORT_TRACE("ANASYS GetSjDefaultPriceInfo_lua IN statTime=<%d>",statTime);
    AISTD string strRecTime(ltoa(statTime));
    if (strRecTime.size() < 14)
    {
        string strTemp("20000101000000");
        strRecTime = strRecTime +  strTemp.substr(strRecTime.size(), 14 - strRecTime.size());
    }
    time_t tm=StringToDatetime(strRecTime);

    iRes=getSjDefaultPriceInfo(GetCurrentSnap(L),settlementType,tm,settlementPrice);

    iLuaParamIdx = 0;
    REPORT_TRACE("ANASYS GetSjDefaultPriceInfo_lua out iRes=<%d>",iRes);
    REPORT_TRACE("ANASYS GetSjDefaultPriceInfo_lua out settlementPrice=<%d>",settlementPrice);

    //lua out BEGIN
    //put_lua_param(L,iLuaParamIdx,iRes);
    put_lua_param(L,iLuaParamIdx,settlementPrice);
    //lua out END

    return iLuaParamIdx;
}

//regist lua function here
int32 RegCommAnalyse(aiexpress_interface* pEi)
{
    pEi->register_cfunction("LogAppend",(void*)LogAppend_lua);
    
    pEi->register_cfunction("SMSNumberAnalysis",(void*)SMSNumberAnalysis_lua);
    
    pEi->register_cfunction("NumberAnalysis",(void*)NumberAnalysis_lua);    
    
    pEi->register_cfunction("GetLongType",(void*)GetLongType_lua);
    
    pEi->register_cfunction("GetRoamType",(void*)GetRoamType_lua);
    
    pEi->register_cfunction("GsmSwitchInfo",(void*)GsmSwitchInfo_lua);
    
    pEi->register_cfunction("GsmRouterInfo",(void*)GsmRouterInfo_lua);
    
    pEi->register_cfunction("GsmRouterInfoNew",(void*)GsmRouterInfoNew_lua);
        
    pEi->register_cfunction("GetRoamAreaCode",(void*)GetRoamAreaCode_lua);
    
    pEi->register_cfunction("SPNumberAnalyze",(void*)SPNumberAnalyze_lua);
    
    pEi->register_cfunction("SPNumberSpAnalyze",(void*)SPNumberSpAnalyze_lua);
    
    pEi->register_cfunction("SPNumberServCodeAnalyze",(void*)SPNumberServCodeAnalyze_lua);
    
    pEi->register_cfunction("BsmsByServCode",(void*)BsmsByServCode_lua);
    
    pEi->register_cfunction("AccCodeAnalyze",(void*)AccCodeAnalyze_lua);
    
    pEi->register_cfunction("OperCodeAnalyze",(void*)OperCodeAnalyze_lua);
    
    pEi->register_cfunction("OperCodeMCCAnalyze",(void*)OperCodeMCCAnalyze_lua);
    
    pEi->register_cfunction("IsmgCodeAnalyze",(void*)IsmgCodeAnalyze_lua);
    
    pEi->register_cfunction("VisitAreaAnalyze",(void*)VisitAreaAnalyze_lua);
    
    pEi->register_cfunction("SpecialUserAnalyze",(void*)SpecialUserAnalyze_lua);

    pEi->register_cfunction("SpecialUserAnalyzeSN",(void*)SpecialUserAnalyzeSN_lua);
    
    pEi->register_cfunction("ImsiAreaCodeAnalysis",(void*)ImsiAreaCodeAnalysis_lua);
    
    pEi->register_cfunction("FindSpRatio",(void*)FindSpRatio_lua);
    
    pEi->register_cfunction("GetThirdRatioInfo",(void*)GetThirdRatioInfo_lua);
    
    pEi->register_cfunction("GetMscIdFromFileName",(void*)GetMscIdFromFileName_lua);
    
    pEi->register_cfunction("FindGprsIpaddr",(void*)FindGprsIpaddr_lua);
    
    pEi->register_cfunction("GetProvByAreaCode",(void*)GetProvByAreaCode_lua);
    
    pEi->register_cfunction("FindNpInfo",(void*)FindNpInfo_lua);
    
    pEi->register_cfunction("IvrRatio",(void*)IvrRatio_lua);
    
    pEi->register_cfunction("ImsiOperAnalysis",(void*)ImsiOperAnalysis_lua);
    /*ZJ-SPECIAL*/
    pEi->register_cfunction("GetSmsNewsAccFee",(void*)GetSmsNewsAccFee_lua);
    
    pEi->register_cfunction("GetSpProdFeeInfo",(void*)GetSpProdFeeInfo_lua);
    
    pEi->register_cfunction("GetFreeFeeWoffType",(void*)GetFreeFeeWoffType_lua);
    
    pEi->register_cfunction("GetUserTradeMark",(void*)GetUserTradeMark_lua);
    
    pEi->register_cfunction("RCSMSNumberAnalyze",(void*)RCSMSNumberAnalyze_lua);
    
    pEi->register_cfunction("getAreaCodeByCeil",(void*)getAreaCodeByCeil_lua);
    
    pEi->register_cfunction("getUserLocation",(void*)getUserLocation_lua);
    
    pEi->register_cfunction("getGridIdByUser",(void*)getGridIdByUser_lua);
    
    pEi->register_cfunction("getGridIdByLacCeil",(void*)getGridIdByLacCeil_lua);
    
    pEi->register_cfunction("getCardHomeProv",(void*)getCardHomeProv_lua);
    
    pEi->register_cfunction("getDateFromFileName",(void*)getDateFromFileName_lua);

    pEi->register_cfunction("IpAddressAnalysis",(void*)IpAddressAnalysis_lua);

    pEi->register_cfunction("GetSjDefaultPriceInfo",(void*)GetSjDefaultPriceInfo_lua);
        
    RegRplDataType(pEi);    
    return 0;
}
