-- base64编码表
local sbase64key = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
-- 默认密钥
local cryptkey = 'mykey'


local function to_bits(val, width)
    local t = {}
    for i = width,1,-1 do
        if math.floor(val / 2^(i-1)) % 2 == 1 then
            t[#t+1] = '1'
        else
            t[#t+1] = '0'
        end
    end
    return table.concat(t)
end
-- base64编码
function enc64(data)
    local s = {}
    data:gsub('.', function(x)
        s[#s + 1] = to_bits(string.byte(x), 8)
    end)
    s = table.concat(s)
    local r = {}
    for i = 1, #s, 6 do
        local sextet = s:sub(i, i + 5)
        if #sextet < 6 then
            sextet = sextet .. string.rep('0', 6 - #sextet)
        end
        local idx = tonumber(sextet, 2) + 1
        r[#r + 1] = sbase64key:sub(idx, idx)
    end
    return table.concat(r) .. ({ '', '==', '=' })[#data % 3 + 1]
end
-- base64解码
function dec64(data)
    data = data:gsub('[^' .. sbase64key .. '=]', '')
    local t = {}
    for c in data:gmatch('.') do
        if c ~= '=' then
            local val = sbase64key:find(c, 1, true) - 1
            t[#t + 1] = to_bits(val, 6)
        end
    end
    local s = table.concat(t)
    local r = {}
    for i = 1, #s - 7, 8 do
        r[#r + 1] = string.char(tonumber(s:sub(i, i + 7), 2))
    end
    return table.concat(r)
end

-- xor byte
local function xor(a, b)
    local res = 0
    for i=0,7 do
        res = res + (((a%2~=b%2) and 1 or 0)*(2^i))
        a = math.floor(a/2)
        b = math.floor(b/2)
    end
    return res
end

function xor_crypt(input, key)
    local output = {}
    for i = 1, #input do
        local c = string.byte(input, i)
        local k = string.byte(key, ((i - 1) % #key) + 1)
        output[i] = string.char(xor(c, k))
    end
    return table.concat(output)
end

-- 辅助：将长度整数（最大4字节）转为4个字节，存储在字符串里
local function int_to_bytes(n)
    local b1 = math.floor(n / (2^24)) % 256
    local b2 = math.floor(n / (2^16)) % 256
    local b3 = math.floor(n / (2^8)) % 256
    local b4 = n % 256
    return string.char(b1, b2, b3, b4)
end
-- 反解长度
local function bytes_to_int(s)
    local b1, b2, b3, b4 = string.byte(s, 1, 4)
    return b1*2^24 + b2*2^16 + b3*2^8 + b4
end

-----------------加密函数（自包含）------------------
function encrypt_with_len(plain_text, cryptkey)
    local len_bytes = int_to_bytes(#plain_text)      -- 明文长度4字节
    local raw = len_bytes .. xor_crypt(plain_text, cryptkey)
    return enc64(raw)                                -- base64整体输出
end

-----------------解密函数（自包含）------------------
function decrypt_with_len(cipher_base64, cryptkey)
    local all = dec64(cipher_base64)
    local len = bytes_to_int(all:sub(1,4))           -- 取前4字节复原长度
    local data = all:sub(5,4+len)                    -- 取后面密文体
    return xor_crypt(data, cryptkey)
end

-----------------------示例--------------------------
-- 处理命令行参数
local plain_text = arg[1]
if not plain_text then
    print("\27[33m用法: lua "..arg[0].." \"明文内容\" [密钥(可选，默认mykey)]\27[0m")
    os.exit(1)
end
if arg[2] then
    cryptkey = arg[2]
end

print("明文           :", plain_text)
print("密钥           :", cryptkey)

local crypted64 = encrypt_with_len(plain_text, cryptkey)
print("加密输出Base64 :", crypted64)

local decrypted = decrypt_with_len(crypted64, cryptkey)
print("还原明文       :", decrypted)
