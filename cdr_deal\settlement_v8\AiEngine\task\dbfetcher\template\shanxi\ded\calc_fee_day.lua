require "dbfetcher_util"

function main(args)
    -- 获取数据库连接
    local conn = getConnect()
    -- 获取任务 ID
    local inTaskId = getCode(args, "TASK_ID")
    local inDay = getCode(args, "DAY")

    writeLog("开始处理"..inDay.."日的数据")

    -- 获取 BILL_MONTH 参数
    billMonth = getBillMonth(conn, inTaskId)
    
    -- 如果获取不到，返回错误
    if not billMonth then
        writeLog("未提供 BILL_MONTH 参数")
        return -1
    end

    -- 实际结算月份等于出账月份前两月，取YYYYMM
    settleMonth = string.sub(getAdjustDate(billMonth, -2), 1, 6)  

    writeLog("BILL_MONTH:" .. billMonth .. "  实际结算月份:" .. settleMonth)

    -- 拼接分表日期后缀 需要保证日期是两位不足补0 这个由sys_process_param_info表args_cmd保证
    local tableSuffix = settleMonth .. inDay

    -- 获取当月最后一天
	local strSql = "select to_char(last_day(to_date(" .. settleMonth .. ",'YYYYMM')),'DD') from dual";
	local cursor = assert(conn:execute(strSql));
	local lastDay = cursor:fetch();
	cursor:close();
    
    -- 表插入 表清理已经在calc_fee_pre_cmp.lua中完成
    if tonumber(lastDay) >= tonumber(inDay) then
        local sql = [[INSERT INTO jsdr.dr_sett_mhtus_ddct
            (   month_id,
                busi_flag,
                msisdn,
                other_party,
                sp_code,
                oper_code,
                charge_type,
                member_type,
                sub_type,
                last_date,
                pro_date,
                mfee,
                dis_mfee,
                sett_ratio,
                sett_fee,
                other_flag,
                channel_code,
                cdr_seq,
                dev_id,
                mrkt_ctvty_id,
                fee3,
                billing_item3,
                user_id,
                fee,
                acct_begin_date,
                ded_fee,
                acc_fee,
                sum_mfee,
                comments)
            SELECT /*+parallel(h,20)*/
                ']] .. string.sub(settleMonth, 5, 6) .. [[',
                DECODE(h.cdr_type, 29, 'CM', 28, 'MMS', 330, 'MMK', 411, 'SJDH', 56, 'M', 390, 'CMIC', 41, 'MUSIC', 501, 'MIGU', TO_CHAR(h.cdr_type)),
                h.user_number,
                h.other_party,
                h.sp_code,
                h.oper_code,
                h.charge_type,
                h.user_type,
                h.sub_type,
                h.start_time,
                h.finish_time,
                h.month_fee,
                h.month_fee,
                '',
                h.charge,
                '',
                h.channel_id,
                h.msg_id,
                h.dev_code,
                '',
                '',
                '',
                '',
                '',
                '',
                h.month_fee,
                '',
                '',
                '未关联ismp清单'
            FROM jsdr.dr_sj_sp_]] .. tableSuffix .. [[ h, jsbd.bps_sp_normal_user_]] .. settleMonth .. [[ g
            WHERE g.p_month = ']] .. string.sub(settleMonth, 5, 6) .. [['
                AND g.month_id = ']] .. settleMonth .. [['
                AND g.user_state = '0'
                AND h.month_fee > 0
                AND h.user_number = g.msisdn
                AND NOT EXISTS (SELECT 1
                    FROM jsdr.dr_ismp_inte_list a
                    WHERE a.sp_code = h.sp_code
                    AND a.oper_code = h.oper_code
                    AND a.msisdn = h.user_number
                    AND a.charge_type = h.charge_type)
        ]]

        writeLog("插入未关联ismp清单数据")
        executeSQL(conn, sql)
        conn:commit()
    else
        writeLog("当前月份: " .. settleMonth .. ", 当前日期最后一天: " .. lastDay .. ", 当前inday: " .. inDay .. ", 不处理业务数据")
    end

------------------------------------------------------------------------------
    writeLog("处理完成")
    conn:close()
    return 0
end

environment = os.getenv("ENVIRONMENT")
if environment == "TEST" then
    main("TASK_ID=17990|DAY=01")
    os.exit()
end
